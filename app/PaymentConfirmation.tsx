import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Modal,
  Platform,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
} from "react-native";
import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import { MaterialCommunityIcons, MaterialIcons, FontAwesome5 } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { ProtectedRoute } from "../ui/common/ProtectedRoute";
import { processStripePayment, processGooglePayPayment, processApplePayPayment, PaymentData } from "../lib/services/paymentService";
import { getId, getFname } from "../lib/utils/variables";
import { firestoreService } from "../lib/services/database";
import { Program } from "../shared/types/CommonInterface";
import { ErrorModal, SuccessModal, ConfirmationModal } from "../shared/components/modals";
import { useTheme } from "../shared/contexts/ThemeContext";
import {
  ProgramPreferences,
  ReminderChannel,
  StrictnessLevel,
  NotificationTiming
} from "../shared/types/customStake";

import { PreferencesFeaturesForm } from "../ui/forms/PreferencesFeaturesForm";
import { ProgressBar } from "../ui/forms/ProgressBar";
import { getUserTimezone } from "../lib/utils/timezoneUtils";
import { useSubscription } from "../lib/hooks/useSubscription";
import { LazyScreen } from "../ui/common/LazyScreen";

import { SubscriptionModal } from "../ui/forms/SubscriptionModal";

// Category icons mapping - will be made theme-aware in the component
const getCategoryIcons = (iconColor: string): Record<string, JSX.Element> => ({
  gym: (
    <MaterialCommunityIcons name="weight-lifter" size={32} color={iconColor} />
  ),
  cardio: <FontAwesome5 name="running" size={32} color={iconColor} />,
  coding: <MaterialCommunityIcons name="laptop" size={32} color={iconColor} />,
  journaling: <FontAwesome5 name="book-open" size={32} color={iconColor} />,
  affirmations: (
    <MaterialCommunityIcons name="message-text" size={32} color={iconColor} />
  ),
  writing: <MaterialCommunityIcons name="pencil" size={32} color={iconColor} />,
});

const PaymentConfirmationComponent: React.FC = () => {
  const { colors, isDark, designSystem } = useTheme();
  const { programId } = useLocalSearchParams<{ programId?: string }>();
  const router = useRouter();
  const [program, setProgram] = useState<Program | null>(null);
  const [loading, setLoading] = useState(true);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [termsVisible, setTermsVisible] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [userId, setUserId] = useState<string>("");

  // Modal states for replacing Alert dialogs
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '', onPress: () => {} });
  const [confirmationModalVisible, setConfirmationModalVisible] = useState(false);
  const [confirmationModalData, setConfirmationModalData] = useState({ title: '', message: '', onConfirm: () => {} });

  // Step management
  const [currentStep, setCurrentStep] = useState(1);

  // Program preferences state
  const [programPreferences, setProgramPreferences] = useState<ProgramPreferences>({
    motivationalNote: '',
    reminderChannels: [],
    strictnessLevel: 'reasonable',
    notificationTiming: { type: 'hours-before', hoursBeforeDeadline: 2 },
    prioritySupport: false,
    advancedAnalytics: false,
    addToCalendar: false,
  });

  // Subscription status
  const { refreshSubscriptionStatus } = useSubscription();

  // Subscription modal state
  const [subscriptionModalVisible, setSubscriptionModalVisible] = useState(false);
  const [selectedFeatureName, setSelectedFeatureName] = useState('');

  // Inline styles matching CustomStake structure
  const styles = {
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.background,
    },
    backButton: {
      width: 44,
      height: 44,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    headerTitle: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: colors.text,
      textAlign: 'center' as const,
      marginLeft: -44, // Compensate for back button width
    },
    headerSpacer: {
      width: 44,
    },
    scrollContent: {
      flex: 1,
    },
    scrollContentContainer: {
      flexGrow: 1,
    },
    submitContainer: {
      paddingHorizontal: 20,
      paddingVertical: 20,
      backgroundColor: colors.background,
    },
    submitButton: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      ...designSystem.shadows.lg,
      shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    },
    submitButtonText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: '#000',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center" as const,
      alignItems: "center" as const,
      backgroundColor: colors.background,
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center" as const,
      alignItems: "center" as const,
      backgroundColor: colors.background,
      padding: 20,
    },
    errorText: {
      color: colors.text,
      fontSize: 18,
      marginBottom: 20,
      textAlign: "center" as const,
    },

    // Additional missing styles
    backButtonText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: '#000',
    },
    // Program card and payment styles
    programCard: {
      borderRadius: 20,
      marginVertical: 15,
      marginHorizontal: 20,
      padding: 20,
      ...designSystem.shadows.xl,
      shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    },
    backgroundPattern: {
      position: "absolute" as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: 0.1,
    },
    patternIcon1: {
      position: "absolute" as const,
      top: -20,
      right: -30,
    },
    patternIcon2: {
      position: "absolute" as const,
      bottom: -10,
      left: -20,
    },
    patternIcon3: {
      position: "absolute" as const,
      top: "50%" as const,
      left: "50%" as const,
      transform: [{ translateX: -30 }, { translateY: -30 }],
    },
    cardHeader: {
      flexDirection: "row" as const,
      alignItems: "center" as const,
      marginBottom: 20,
      zIndex: 1,
    },
    iconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      alignItems: "center" as const,
      justifyContent: "center" as const,
      marginRight: 15,
      ...designSystem.shadows.lg,
      shadowColor: colors.primary,
    },
    iconGlow: {
      alignItems: "center" as const,
      justifyContent: "center" as const,
    },
    cardTextContainer: {
      flex: 1,
      marginRight: 10,
    },
    programName: {
      fontSize: 18,
      fontFamily: "MontserratBold",
      color: "#fff",
      marginBottom: 4,
    },
    programHeadline: {
      fontSize: 14,
      fontFamily: "MontserratRegular",
      color: "#ccc",
      lineHeight: 18,
    },
    categoryBadge: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 15,
      alignItems: "center" as const,
      justifyContent: "center" as const,
    },
    categoryText: {
      fontSize: 12,
      fontFamily: "MontserratBold",
      color: "#000",
    },
    // Stats and timeline styles
    statsContainer: {
      flexDirection: "row" as const,
      justifyContent: "space-around" as const,
      alignItems: "center" as const,
      marginVertical: 20,
      paddingVertical: 15,
      backgroundColor: "rgba(255, 235, 59, 0.1)",
      borderRadius: 12,
    },
    statItem: {
      alignItems: "center" as const,
      flex: 1,
    },
    statLabel: {
      fontSize: 12,
      fontFamily: "MontserratRegular",
      color: "#ccc",
      marginTop: 4,
      textAlign: "center" as const,
    },
    statValue: {
      fontSize: 16,
      fontFamily: "MontserratBold",
      color: "#fff",
      marginTop: 2,
      textAlign: "center" as const,
    },
    statDivider: {
      width: 1,
      height: 40,
      backgroundColor: "#444",
      marginHorizontal: 10,
    },
    timelineContainer: {
      flexDirection: "row" as const,
      alignItems: "center" as const,
      justifyContent: "space-between" as const,
      marginVertical: 15,
      paddingHorizontal: 10,
    },
    timelineItem: {
      flexDirection: "row" as const,
      alignItems: "center" as const,
      flex: 1,
    },
    timelineIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: "rgba(255, 235, 59, 0.2)",
      alignItems: "center" as const,
      justifyContent: "center" as const,
      marginRight: 10,
    },
    timelineContent: {
      flex: 1,
    },
    timelineLabel: {
      fontSize: 12,
      fontFamily: "MontserratRegular",
      color: "#ccc",
      marginBottom: 2,
    },
    timelineValue: {
      fontSize: 14,
      fontFamily: "MontserratBold",
      color: "#fff",
    },
    timelineLine: {
      width: 40,
      height: 2,
      backgroundColor: "#444",
      marginHorizontal: 10,
    },
    amountContainer: {
      alignItems: "center" as const,
      marginTop: 20,
      paddingTop: 20,
      borderTopWidth: 1,
      borderTopColor: "#444",
    },
    amountLabel: {
      fontSize: 14,
      fontFamily: "MontserratRegular",
      color: "#ccc",
      marginBottom: 8,
    },
    amountValue: {
      fontSize: 32,
      fontFamily: "MontserratBold",
      color: "#FFEB3B",
    },
    // Terms and payment styles
    termsContainer: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.surface,
      marginHorizontal: 16,
      marginVertical: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    termsRow: {
      flexDirection: "row" as const,
      alignItems: "center" as const,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderWidth: 2,
      borderColor: colors.primary,
      borderRadius: 4,
      marginRight: 12,
      alignItems: "center" as const,
      justifyContent: "center" as const,
    },
    checkboxChecked: {
      width: 12,
      height: 12,
      backgroundColor: colors.primary,
      borderRadius: 2,
    },
    termsText: {
      fontSize: 14,
      fontFamily: "MontserratRegular",
      color: colors.text,
      flex: 1,
      lineHeight: 20,
    },
    termsLink: {
      color: colors.primary,
      fontFamily: "MontserratBold",
      textDecorationLine: "underline" as const,
    },
    paymentButtons: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      gap: 12,
    },
    paymentButton: {
      flexDirection: "row" as const,
      alignItems: "center" as const,
      justifyContent: "center" as const,
      paddingVertical: 16,
      paddingHorizontal: 20,
      borderRadius: 12,
      gap: 12,
    },
    stripeButton: {
      backgroundColor: "#6772E5",
    },
    applePayButton: {
      backgroundColor: "#000",
    },
    googlePayButton: {
      backgroundColor: "#4285F4",
    },
    disabledButton: {
      opacity: 0.5,
    },
    paymentButtonText: {
      fontSize: 16,
      fontFamily: "MontserratBold",
      color: "#fff",
    },
    freeJoinContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      alignItems: "center" as const,
    },
    freeJoinText: {
      fontSize: 14,
      fontFamily: "MontserratRegular",
      color: colors.textSecondary,
      textAlign: "center" as const,
      marginBottom: 12,
      lineHeight: 20,
    },
    freeJoinButton: {
      backgroundColor: colors.surface,
      borderWidth: 2,
      borderColor: colors.primary,
    },
    freeJoinButtonText: {
      fontSize: 16,
      fontFamily: "MontserratBold",
      color: colors.text,
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      justifyContent: "center" as const,
      alignItems: "center" as const,
      padding: 20,
    },
    modalContent: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 20,
      maxHeight: "80%" as const,
      width: "100%" as const,
      maxWidth: 400,
    },
    modalHeader: {
      flexDirection: "row" as const,
      justifyContent: "space-between" as const,
      alignItems: "center" as const,
      marginBottom: 15,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      color: colors.primary,
      fontSize: 18,
      fontFamily: "MontserratBold",
    },
    modalScrollView: {
      maxHeight: 400,
    },
    modalText: {
      color: colors.text,
      fontSize: 14,
      lineHeight: 20,
      fontFamily: "MontserratRegular",
    },
    modalCloseButton: {
      backgroundColor: colors.primary,
      padding: 12,
      borderRadius: 8,
      alignItems: "center" as const,
      marginTop: 15,
    },
    modalCloseButtonText: {
      color: "#000",
      fontSize: 16,
      fontFamily: "MontserratBold",
    },
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!programId) return;

      try {
        // Add delay to allow smooth navigation transition
        await new Promise(resolve => setTimeout(resolve, 150));

        // Fetch user ID
        const id = await getId();
        if (id) {
          setUserId(id);
        }

        // Fetch program data
        const programResult = await firestoreService.programs.getProgramById(programId);
        if (programResult.success && programResult.data) {
          setProgram(programResult.data as Program);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setErrorModalData({
          title: "Error",
          message: "Failed to load program details."
        });
        setErrorModalVisible(true);
      } finally {
        setLoading(false);
      }
    };

    // Use requestAnimationFrame to ensure this runs after the transition starts
    const frame = requestAnimationFrame(() => {
      fetchData();
    });

    return () => cancelAnimationFrame(frame);
  }, [programId]);

  const handleTermsPress = () => {
    setTermsVisible(true);
  };

  // Program preferences handlers
  const updateMotivationalNote = (note: string) => {
    setProgramPreferences(prev => ({ ...prev, motivationalNote: note }));
  };

  const updateReminderChannels = (channels: ReminderChannel[]) => {
    setProgramPreferences(prev => ({ ...prev, reminderChannels: channels }));
  };

  const updateStrictnessLevel = (level: StrictnessLevel) => {
    setProgramPreferences(prev => ({ ...prev, strictnessLevel: level }));
  };

  const updateNotificationTiming = (timing: NotificationTiming) => {
    setProgramPreferences(prev => ({ ...prev, notificationTiming: timing }));
  };

  const updateProgramPreferences = (updates: Partial<ProgramPreferences>) => {
    setProgramPreferences(prev => ({ ...prev, ...updates }));
  };



  // Step navigation functions
  const handleStep1Next = () => {
    setCurrentStep(2);
  };

  // Subscription modal handlers
  const handleSubscriptionModalClose = () => {
    setSubscriptionModalVisible(false);
    setSelectedFeatureName('');
  };

  const handleSubscriptionPurchase = async () => {
    // TODO: Implement actual subscription purchase logic
    // For now, just close the modal and show success
    setSubscriptionModalVisible(false);
    setSelectedFeatureName('');

    // Refresh subscription status after purchase
    await refreshSubscriptionStatus();

    setSuccessModalData({
      title: 'Premium Activated',
      message: 'Your premium subscription has been activated! You can now access all premium features.',
      onPress: () => setSuccessModalVisible(false)
    });
    setSuccessModalVisible(true);
  };

  const handleBackToStep1 = () => {
    setCurrentStep(1);
  };

  const handleStripePayment = async () => {
    if (!termsAgreed) {
      setErrorModalData({
        title: "Terms Required",
        message: "Please agree to the terms and conditions first."
      });
      setErrorModalVisible(true);
      return;
    }

    if (!program || !userId) {
      setErrorModalData({
        title: "Error",
        message: "Missing program or user information."
      });
      setErrorModalVisible(true);
      return;
    }

    setProcessingPayment(true);

    try {
      const paymentData: PaymentData = {
        amount: (program.betAmount || 0) * 100, // Convert to cents
        currency: 'usd',
        description: `Habit Royale - ${program.name}`,
        programId: program.id,
        userId: userId,
      };

      const result = await processStripePayment(paymentData);

      if (result.success) {
        setSuccessModalData({
          title: "Payment Successful!",
          message: "Your payment has been processed. You will be redirected to complete your program signup.",
          onPress: () => {
            setSuccessModalVisible(false);
            const programPreferencesParam = encodeURIComponent(JSON.stringify(programPreferences));
            router.push(`/ProgramDetails?programId=${programId}&paymentCompleted=true&programPreferences=${programPreferencesParam}`);
          }
        });
        setSuccessModalVisible(true);
      } else {
        setErrorModalData({
          title: "Payment Failed",
          message: result.error || "An unknown error occurred."
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error("Payment error:", error);
      setErrorModalData({
        title: "Payment Error",
        message: "Failed to process payment. Please try again."
      });
      setErrorModalVisible(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleGooglePayment = async () => {
    if (!termsAgreed) {
      setErrorModalData({
        title: "Terms Required",
        message: "Please agree to the terms and conditions first."
      });
      setErrorModalVisible(true);
      return;
    }

    if (!program || !userId) {
      setErrorModalData({
        title: "Error",
        message: "Missing program or user information."
      });
      setErrorModalVisible(true);
      return;
    }

    setProcessingPayment(true);

    try {
      const paymentData: PaymentData = {
        amount: (program.betAmount || 0) * 100, // Convert to cents
        currency: 'usd',
        description: `Habit Royale - ${program.name}`,
        programId: program.id,
        userId: userId,
      };

      const result = await processGooglePayPayment(paymentData);

      if (result.success) {
        setSuccessModalData({
          title: "Payment Successful!",
          message: "Your payment has been processed. You will be redirected to complete your program signup.",
          onPress: () => {
            setSuccessModalVisible(false);
            const programPreferencesParam = encodeURIComponent(JSON.stringify(programPreferences));
            router.push(`/ProgramDetails?programId=${programId}&paymentCompleted=true&programPreferences=${programPreferencesParam}`);
          }
        });
        setSuccessModalVisible(true);
      } else {
        setErrorModalData({
          title: "Payment Failed",
          message: result.error || "An unknown error occurred."
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error("Payment error:", error);
      setErrorModalData({
        title: "Payment Error",
        message: "Failed to process payment. Please try again."
      });
      setErrorModalVisible(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleJoinWithoutPayment = () => {
    if (!termsAgreed) {
      setErrorModalData({
        title: "Terms Required",
        message: "Please agree to the terms and conditions first."
      });
      setErrorModalVisible(true);
      return;
    }

    if (!program || !userId) {
      setErrorModalData({
        title: "Error",
        message: "Missing program or user information."
      });
      setErrorModalVisible(true);
      return;
    }

    setConfirmationModalData({
      title: "Join Program",
      message: "You are about to join this program without payment. You will not be eligible for monetary rewards but can still participate in the challenge.",
      onConfirm: () => {
        setConfirmationModalVisible(false);
        handleFreeEnrollment();
      }
    });
    setConfirmationModalVisible(true);
  };

  const handleFreeEnrollment = async () => {
    if (!program || !userId) return;
    setProcessingPayment(true);

    try {
      const result = await firestoreService.enrollUserInProgram(userId, program.id, {
        fname: (await getFname()) || 'User',
        paymentDone: false, // Free enrollment
        timezone: getUserTimezone(),
        programPreferences: programPreferences
      });

      if (result.success) {
        setSuccessModalData({
          title: "Success!",
          message: "You have successfully joined the program for free.",
          onPress: () => {
            setSuccessModalVisible(false);
            const programPreferencesParam = encodeURIComponent(JSON.stringify(programPreferences));
            router.replace(
              `/progress?selectedProgramId=${program.id}&t=${Date.now()}&programPreferences=${programPreferencesParam}`
            );
          }
        });
        setSuccessModalVisible(true);
      } else {
        setErrorModalData({
          title: "Error",
          message: result.error || "Failed to join program"
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error("Error joining program:", error);
      setErrorModalData({
        title: "Error",
        message: "Failed to join program. Please try again."
      });
      setErrorModalVisible(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleApplePayment = async () => {
    if (!termsAgreed) {
      setErrorModalData({
        title: "Terms Required",
        message: "Please agree to the terms and conditions first."
      });
      setErrorModalVisible(true);
      return;
    }

    if (!program || !userId) {
      setErrorModalData({
        title: "Error",
        message: "Missing program or user information."
      });
      setErrorModalVisible(true);
      return;
    }

    setProcessingPayment(true);

    try {
      const paymentData: PaymentData = {
        amount: (program.betAmount || 0) * 100, // Convert to cents
        currency: 'usd',
        description: `Habit Royale - ${program.name}`,
        programId: program.id,
        userId: userId,
      };

      const result = await processApplePayPayment(paymentData);

      if (result.success) {
        setSuccessModalData({
          title: "Payment Successful!",
          message: "Your payment has been processed. You will be redirected to complete your program signup.",
          onPress: () => {
            setSuccessModalVisible(false);
            const programPreferencesParam = encodeURIComponent(JSON.stringify(programPreferences));
            router.push(`/ProgramDetails?programId=${programId}&paymentCompleted=true&programPreferences=${programPreferencesParam}`);
          }
        });
        setSuccessModalVisible(true);
      } else {
        setErrorModalData({
          title: "Payment Failed",
          message: result.error || "An unknown error occurred."
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error("Payment error:", error);
      setErrorModalData({
        title: "Payment Error",
        message: "Failed to process payment. Please try again."
      });
      setErrorModalVisible(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFEB3B" />
      </View>
    );
  }

  if (!program) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Program not found</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
        translucent={false}
      />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={currentStep === 1 ? () => router.back() : handleBackToStep1}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <MaterialIcons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {currentStep === 1 ? 'Setup' : 'Payment Confirmation'}
          </Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Progress Bar */}
        <ProgressBar
          currentStep={currentStep}
          totalSteps={2}
        />

        {/* Scrollable Content */}
        <ScrollView
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >

      {/* Step-based Content */}
      {currentStep === 1 ? (
        /* Step 1: Program Preferences */
        <PreferencesFeaturesForm
          programPreferences={programPreferences}
          reportingFrequency="daily" // Default for program preferences
          onUpdateMotivationalNote={updateMotivationalNote}
          onUpdateReminderChannels={updateReminderChannels}
          onUpdateStrictnessLevel={updateStrictnessLevel}
          onUpdateNotificationTiming={updateNotificationTiming}
          onUpdateProgramPreferences={updateProgramPreferences}
          showActionButtons={false}
          title="Customize Your Experience"
          subtitle="Set up preferences and advanced options"
        />
      ) : (
        /* Step 2: Payment */
        <>
          {/* Enhanced Program Overview Card */}
          <LinearGradient
            colors={['#1a1a1a', '#2d2d2d', '#1a1a1a']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.programCard}
          >
            {/* Decorative Background Pattern */}
            <View style={styles.backgroundPattern}>
              <MaterialCommunityIcons name="hexagon-outline" size={120} color="#FFEB3B10" style={styles.patternIcon1} />
              <MaterialCommunityIcons name="circle-outline" size={80} color="#FFEB3B08" style={styles.patternIcon2} />
              <MaterialCommunityIcons name="triangle-outline" size={60} color="#FFEB3B05" style={styles.patternIcon3} />
            </View>

            {/* HEADER with Enhanced Icon and Glow Effect */}
            <View style={styles.cardHeader}>
              <LinearGradient
                colors={['#FFEB3B', '#FFC107', '#FF9800']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.iconContainer}
              >
                <View style={styles.iconGlow}>
                  {(program.category && getCategoryIcons(colors.background)[program.category]) || (
                    <MaterialCommunityIcons name="tag" size={32} color={colors.background} />
                  )}
                </View>
              </LinearGradient>

              <View style={styles.cardTextContainer}>
                <Text style={styles.programName} numberOfLines={2} ellipsizeMode="tail">
                  {program.name}
                </Text>
                {program.headline ? (
                  <Text style={styles.programHeadline} numberOfLines={2} ellipsizeMode="tail">
                    {program.headline}
                  </Text>
                ) : null}
              </View>

              <LinearGradient
                colors={['#FFEB3B', '#FFC107']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.categoryBadge}
              >
                <Text style={styles.categoryText}>
                  {program.category ? program.category.charAt(0).toUpperCase() + program.category.slice(1) : 'General'}
                </Text>
              </LinearGradient>
            </View>

            {/* Enhanced Stats Section */}
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <MaterialCommunityIcons name="calendar-clock" size={20} color="#FFEB3B" />
                <Text style={styles.statLabel}>Duration</Text>
                <Text style={styles.statValue}>{Math.ceil((Number(program.duration) || 0) / 7)} Weeks</Text>
              </View>

              <View style={styles.statDivider} />

              <View style={styles.statItem}>
                <MaterialCommunityIcons name="account-group" size={20} color="#FFEB3B" />
                <Text style={styles.statLabel}>Participants</Text>
                <Text style={styles.statValue}>{program.participantsCount}</Text>
              </View>

              <View style={styles.statDivider} />

              <View style={styles.statItem}>
                <MaterialCommunityIcons name="trophy" size={20} color="#FFEB3B" />
                <Text style={styles.statLabel}>Prize Pool</Text>
                <Text style={styles.statValue}>${(program.participantsCount || 0) * (program.betAmount || 0)}</Text>
              </View>
            </View>

            {/* Timeline Section */}
            <View style={styles.timelineContainer}>
              <View style={styles.timelineItem}>
                <View style={styles.timelineIcon}>
                  <MaterialCommunityIcons name="play-circle" size={16} color="#4CAF50" />
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineLabel}>Start Date</Text>
                  <Text style={styles.timelineValue}>{program.startDate ? new Date(program.startDate).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  }) : 'TBD'}</Text>
                </View>
              </View>

              <View style={styles.timelineLine} />

              <View style={styles.timelineItem}>
                <View style={styles.timelineIcon}>
                  <MaterialCommunityIcons name="flag-checkered" size={16} color="#FF5722" />
                </View>
                <View style={styles.timelineContent}>
                  <Text style={styles.timelineLabel}>End Date</Text>
                  <Text style={styles.timelineValue}>{program.endDate ? new Date(program.endDate).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  }) : 'TBD'}</Text>
                </View>
              </View>
            </View>

            {/* Simple Amount Section */}
            <View style={styles.amountContainer}>
              <Text style={styles.amountLabel}>Your Investment</Text>
              <Text style={styles.amountValue}>${program.betAmount}</Text>
            </View>
          </LinearGradient>

          <View style={styles.termsContainer}>
        <TouchableOpacity 
          style={styles.termsRow}
          onPress={() => setTermsAgreed(!termsAgreed)}
        >
          <View style={styles.checkbox}>
            {termsAgreed && <View style={styles.checkboxChecked} />}
          </View>
          <Text style={styles.termsText}>
            I agree to the{" "}
            <Text style={styles.termsLink} onPress={handleTermsPress}>
              Terms and Conditions
            </Text>
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.paymentButtons}>
        {/* Stripe Payment - Available on all platforms */}
        <TouchableOpacity
          style={[
            styles.paymentButton,
            styles.stripeButton,
            !termsAgreed && styles.disabledButton,
          ]}
          onPress={handleStripePayment}
          disabled={!termsAgreed || processingPayment}
        >
          {processingPayment ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialCommunityIcons name="credit-card" size={24} color="#fff" />
              <Text style={styles.paymentButtonText}>Pay with Card</Text>
            </>
          )}
        </TouchableOpacity>

        {/* Apple Pay - iOS only */}
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            style={[
              styles.paymentButton,
              styles.applePayButton,
              !termsAgreed && styles.disabledButton,
            ]}
            onPress={handleApplePayment}
            disabled={!termsAgreed || processingPayment}
          >
            {processingPayment ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialCommunityIcons name="apple" size={24} color="#fff" />
                <Text style={styles.paymentButtonText}>Pay with Apple Pay</Text>
              </>
            )}
          </TouchableOpacity>
        )}

        {/* Google Pay - Android only */}
        {Platform.OS === 'android' && (
          <TouchableOpacity
            style={[
              styles.paymentButton,
              styles.googlePayButton,
              !termsAgreed && styles.disabledButton,
            ]}
            onPress={handleGooglePayment}
            disabled={!termsAgreed || processingPayment}
          >
            {processingPayment ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialCommunityIcons name="google" size={24} color="#fff" />
                <Text style={styles.paymentButtonText}>Pay with Google Pay</Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Join Program Without Payment Button */}
      <View style={styles.freeJoinContainer}>
        <Text style={styles.freeJoinText}>
          Want to participate without the financial commitment?
        </Text>
        <TouchableOpacity
          style={[
            styles.paymentButton,
            styles.freeJoinButton,
            !termsAgreed && styles.disabledButton,
          ]}
          onPress={handleJoinWithoutPayment}
          disabled={!termsAgreed || processingPayment}
        >
          {processingPayment ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <>
              <MaterialCommunityIcons name="account-plus" size={24} color="#000" />
              <Text style={styles.freeJoinButtonText}>Join Program (Free)</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
        </>
      )}

      {/* Terms and Conditions Modal */}
      <Modal visible={termsVisible} animationType="slide" transparent={true}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Terms & Conditions</Text>
              <TouchableOpacity onPress={() => setTermsVisible(false)}>
                <MaterialIcons name="close" size={24} color="#FFEB3B" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.modalText}>
                {`1. By participating in this program, you acknowledge that you are entering into a legally binding agreement with Accustom and its affiliates.

2. Participants must be at least 13 years old. If you are under 18, parental consent is required.

3. All users must provide accurate information during sign-up, including valid identification if requested.

4. By pledging money to the challenge, you agree that this amount is non-refundable unless specified by the program rules.

5. Users who fail to check in or upload proof of progress according to the challenge rules may be disqualified and forfeit their pledge.

6. Grace days may be used only as per the program’s specific rules. Misuse or manipulation of grace day options may lead to disqualification.

7. Users are responsible for ensuring their check-ins are accurate and truthful. Any fraudulent submissions will lead to immediate disqualification and account suspension.

8. Moderators have the authority to verify, accept, or reject proof of progress submissions. Their decisions are final.

9. In case of disputes, Accustom reserves the right to review the entire challenge history and make a final ruling.

10. Program creators are entitled to a 6% commission from the final reward pool of their created programs, while Accustom takes 6% as platform fees.

11. The remaining 88% of the losers' pool is equally distributed among the winners.

12. Monthly and fortnightly leaderboards are based on points earned by participating and completing programs. Rewards are subject to availability and discretion of the platform.

13. Users must not engage in any form of harassment, hate speech, or inappropriate behavior within the app or associated chatrooms.

14. All in-app purchases, including extra bailouts or premium subscriptions, are final and non-refundable.

15. Users can create custom programs, but they are solely responsible for the program’s legitimacy, fairness, and participant engagement.

16. Users must grant access to third-party services (e.g., Strava, GitHub, Apple Health, etc.) to track and verify their progress as required by specific programs.

17. By joining a program, users agree to share relevant data including but not limited to: GPS location, fitness data, app usage statistics, submitted images, video proof, activity metadata, and third-party connection tokens.

18. All program-related data including GPS coordinates, photos, submissions, metadata, and 3rd party access will be automatically and permanently deleted from Accustom servers within 15 days after the program ends.

19. Users can manually revoke third-party access at any time via their profile settings, but this may result in automatic removal from active programs.

20. The platform reserves the right to suspend or terminate user accounts in the event of policy violations, fraudulent activity, or platform misuse.

21. Accustom is not responsible for any financial loss, health issues, or injuries incurred while participating in a challenge. Users are advised to consult their physician or relevant professionals before joining physical or mentally demanding programs.

22. By accepting these terms, users also consent to receive program-related updates, alerts, and occasional marketing communications, which can be managed from notification settings.

23. All collected data is handled in accordance with our Privacy Policy and is encrypted in transit and at rest.

24. Your participation in any challenge implies full understanding and acceptance of all terms listed above.

25. These Terms & Conditions are subject to change. Users will be notified via the app or email in case of updates.
`}
              </Text>
            </ScrollView>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setTermsVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
        onButtonPress={successModalData.onPress}
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        visible={confirmationModalVisible}
        onClose={() => setConfirmationModalVisible(false)}
        title={confirmationModalData.title}
        message={confirmationModalData.message}
        onConfirm={confirmationModalData.onConfirm}
        confirmText="Join Program"
        cancelText="Cancel"
      />

      {/* Subscription Modal */}
      <SubscriptionModal
        visible={subscriptionModalVisible}
        onClose={handleSubscriptionModalClose}
        onSubscribe={handleSubscriptionPurchase}
        featureName={selectedFeatureName}
      />

        {/* Bottom Navigation - Only show on Step 1 */}
        {currentStep === 1 && (
          <View style={styles.submitContainer}>
            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleStep1Next}
            >
              <Text style={styles.submitButtonText}>Next: Payment</Text>
            </TouchableOpacity>
          </View>
        )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const PaymentConfirmation: React.FC = () => {
  return (
    <ProtectedRoute>
      <LazyScreen delay={100}>
        <PaymentConfirmationComponent />
      </LazyScreen>
    </ProtectedRoute>
  );
};

export default PaymentConfirmation;
