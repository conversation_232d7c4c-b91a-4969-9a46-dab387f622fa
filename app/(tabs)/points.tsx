//TO-DO: 
//1. Implement Feed
//2. Posts with <PERSON><PERSON>, Comments, Posts Details 

import {
  StyleSheet,
  ScrollView,
  SafeAreaView,
  View,
} from "react-native";
import React, { useState, useEffect, useRef } from "react";
import { useFocusEffect } from "expo-router";

import { db } from "../../lib/config/firebase";
import { doc, getDoc, getDocs, collection } from "firebase/firestore";
import { getId } from "../../lib/utils/variables";
import Header from "../../ui/common/Header";
import StreakAndPercentile from "../../ui/pages/Points/StreakAndPercentile";
import CharacterUpgrade from "../../ui/pages/Points/CharacterUpgrade";
import Leaderboard from "../../ui/pages/Points/LeaderBoard";
import Rewards from "../../ui/pages/Points/Rewards";
import { useTheme } from "../../shared/contexts/ThemeContext";

function PointsPage() {
  const { colors } = useTheme();
  // Cache currentUserId so that we don't call getId() repeatedly
  const [currentUserId, setCurrentUserId] = useState<string>("");
  const [percentile, setPercentile] = useState<number | null>(null);
  const [loadingPercentile, setLoadingPercentile] = useState<boolean>(true);
  const [userPoints, setUserPoints] = useState<number | null>(null);
  const [loadingPoints, setLoadingPoints] = useState<boolean>(true);
  const [monthlyLeaderboard, setMonthlyLeaderboard] = useState<any[]>([]);
  const [fortnightlyLeaderboard, setFortnightlyLeaderboard] = useState<any[]>([]);

  // Add caching and lazy loading state
  const [isInitialized, setIsInitialized] = useState(false);
  const lastFetchTime = useRef<number>(0);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  // Fetch and cache the current user ID once
  useEffect(() => {
    const fetchCurrentUserId = async () => {
      const id: any = await getId();
      setCurrentUserId(id);
    };
    fetchCurrentUserId();
  }, []);

  // Lazy loading: only fetch data when tab is focused and data is stale
  const shouldFetchData = () => {
    const now = Date.now();
    return !isInitialized || (now - lastFetchTime.current) > CACHE_DURATION;
  };

  const fetchData = async () => {
    if (!currentUserId || !shouldFetchData()) return;

    lastFetchTime.current = Date.now();
      await Promise.all([
        // Fetch percentile with fallback
        (async () => {
          try {
            // Try external API first (non-blocking)
            const fetchPercentileAsync = async () => {
              try {
                const response = await fetch(
                  `https://fetch-percentile-851427087834.us-central1.run.app/fetchPercentile?userId=${currentUserId}`,
                  {
                    method: 'GET',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    // Add timeout to prevent hanging
                    signal: AbortSignal.timeout(5000)
                  }
                );

                if (response.ok) {
                  const data = await response.json();

                  if (data && typeof data.percentile === 'number') {
                    setPercentile(data.percentile);
                    return true; // Success
                  }
                }
              } catch (apiError) {
                console.warn("External API failed, will calculate locally:", apiError);
              }
              return false; // Failed
            };

            // Start API call but don't wait for it
            fetchPercentileAsync().then(async (success) => {
              if (success) return; // API succeeded, we're done

              // API failed, set a default percentile
              console.warn("External percentile API failed, setting default percentile");
              setPercentile(50); // Default to 50th percentile when API is unavailable
            });

          } catch (error) {
            console.error("Error fetching/calculating percentile:", error);
            setPercentile(null);
          } finally {
            setLoadingPercentile(false);
          }
        })(),
        // Fetch user points
        (async () => {
          try {
            const leaderboardEntryRef = doc(
              db,
              "leaderboard/allTime/entries",
              currentUserId
            );
            const leaderboardEntrySnap = await getDoc(leaderboardEntryRef);
            if (leaderboardEntrySnap.exists()) {
              setUserPoints(leaderboardEntrySnap.data().points);
            } else {
              setUserPoints(0);
            }
          } catch (error) {
            console.error("Error fetching user points:", error);
            setUserPoints(0);
          } finally {
            setLoadingPoints(false);
          }
        })(),
        // Fetch monthly leaderboard
        (async () => {
          try {
            const currentDate = new Date();
            const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");
            const year = currentDate.getFullYear();
            const leaderboardPath = `leaderboard/monthly/${month}-${year}`;
            const leaderboardRef = collection(db, leaderboardPath);
            const querySnapshot = await getDocs(leaderboardRef);
            const leaderboardArray: {
              userId: string;
              name: string;
              points: number;
              updatedAt: number;
            }[] = [];
            querySnapshot.forEach((docSnap) => {
              const data = docSnap.data();
              leaderboardArray.push({
                userId: docSnap.id,
                name: docSnap.id === currentUserId ? "You" : data.name,
                points: data.monthPoint,
                updatedAt: data.updatedAt,
              });
            });
            leaderboardArray.sort((a, b) => {
              if (b.points === a.points) {
                return a.updatedAt - b.updatedAt;
              }
              return b.points - a.points;
            });
            const leaderboardWithRank = leaderboardArray.map((entry, index) => ({
              rank: index + 1,
              ...entry,
            }));
            setMonthlyLeaderboard(leaderboardWithRank);
          } catch (error) {
            console.error("Error fetching monthly leaderboard:", error);
          }
        })(),
        // Fetch fortnightly leaderboard
        (async () => {
          try {
            const currentDate = new Date();
            const year = currentDate.getFullYear();
            const monthNumber = (currentDate.getMonth() + 1).toString().padStart(2, "0");
            let startDay: string, endDay: string;
            if (currentDate.getDate() <= 14) {
              startDay = "01";
              endDay = "14";
            } else {
              startDay = "15";
              const lastDay = new Date(year, currentDate.getMonth() + 1, 0).getDate();
              endDay = lastDay.toString().padStart(2, "0");
            }
            const monthNames = [
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ];
            const monthName = monthNames[currentDate.getMonth()];
            const leaderboardPath = `leaderboard/fortnightly/${startDay}-${monthNumber} | ${endDay}-${monthNumber} ${monthName} ${year}`;
            const leaderboardRef = collection(db, leaderboardPath);
            const querySnapshot = await getDocs(leaderboardRef);
            const leaderboardArray: {
              userId: string;
              name: string;
              points: number;
              updatedAt: number;
            }[] = [];
            querySnapshot.forEach((docSnap) => {
              const data = docSnap.data();
              leaderboardArray.push({
                userId: docSnap.id,
                name: docSnap.id === currentUserId ? "You" : data.name || "Anonymous",
                points: data.fortPoint,
                updatedAt: data.updatedAt,
              });
            });
            leaderboardArray.sort((a, b) => {
              if (b.points === a.points) {
                return a.updatedAt - b.updatedAt;
              }
              return b.points - a.points;
            });
            const leaderboardWithRank = leaderboardArray.map((entry, index) => ({
              rank: index + 1,
              ...entry,
            }));
            setFortnightlyLeaderboard(leaderboardWithRank);
          } catch (error) {
            console.error("Error fetching fortnightly leaderboard:", error);
          }
        })(),
      ]);
      setIsInitialized(true);
    };

  // Initial fetch when user ID is available
  useEffect(() => {
    if (currentUserId && !isInitialized) {
      fetchData();
    }
  }, [currentUserId, isInitialized]);

  // Lazy loading on tab focus
  useFocusEffect(
    React.useCallback(() => {
      if (currentUserId && shouldFetchData()) {
        fetchData();
      }
    }, [currentUserId])
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Header />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <StreakAndPercentile
            userPoints={userPoints}
            percentile={percentile}
            loading={loadingPoints || loadingPercentile}
          />
        </View>

        <View style={styles.section}>
          <Leaderboard
            monthlyLeaderboard={monthlyLeaderboard}
            fortnightlyLeaderboard={fortnightlyLeaderboard}
          />
        </View>

        <View style={styles.section}>
          <CharacterUpgrade userPoints={userPoints} />
        </View>

        <View style={styles.section}>
          <Rewards />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 16, // Removed paddingHorizontal since it's handled by components
  },
  section: {
    marginVertical: 4,
  },
});

export default PointsPage;