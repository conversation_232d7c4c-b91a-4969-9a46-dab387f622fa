import React, { useCallback, useEffect, useState, useRef, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
} from "react-native";
import { firestoreService } from "../../lib/services/database";
import { getFname, getId } from "../../lib/utils/variables";
import { MaterialIcons } from "@expo/vector-icons";
import {
  useGlobalSearchParams,
  useRouter,
} from "expo-router";

import Header from "../../ui/common/Header";
import { useTheme } from "../../shared/contexts/ThemeContext";

import ProgressBar from "../../ui/pages/Progress/ProgressBar";
import ProgramDropdown from "../../ui/pages/Progress/ProgramDropDown";
import ProgramEnd from "../../ui/pages/Progress/programEnd/ProgramEnd";
import { ChatModal } from "../../ui/pages/Progress/ChatModal";
import { NewCalendar, clearCalendarCaches } from "../../ui/pages/Progress/NewCalendar";
import { NewCalendarDetails } from "../../ui/pages/Progress/NewCalendarDetails";
import { AttentionModal } from "../../ui/pages/Progress/AttentionModal";
import { FloatingSetupButton } from "../../ui/components/FloatingSetupButton";
import { SetupBottomSheet } from "../../ui/components/SetupBottomSheet";
import { ProgramDayRenderer } from "../../ui/pages/Progress/ProgramDayRenderer";
import { TabViewSwitch } from "../../ui/pages/Progress/TabViewSwitch";
import { QuickViewSwitch } from "../../ui/pages/Progress/QuickViewSwitch";
import {
  calculateCurrentDay,
  calculateCurrentDayWithTimezone,
  calculateDaysToStart,
} from "../../ui/pages/Progress/Calculations";
import { getUserTimezone } from "../../lib/utils/timezoneUtils";

import {
  Program,
  DayTile,
  ParticipantList,
  ProgramTimelineData
} from "../../shared/types/CommonInterface";
import { Commit, UserCommit } from "../../lib/services/database/types";
import { InfoButton } from "../../ui/pages/Progress/InfoButton";
import CommitInput from "../../ui/pages/Progress/CommitInput";
import { DisqualifiedMessage } from "../../shared/components/program/DisqualifiedMessage";

export default function ProgressScreen() {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors, designSystem, isDark);
  const [signedUpPrograms, setSignedUpPrograms] = useState<Program[]>([]);
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [setupStatus, setSetupStatus] = useState<boolean | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loadingPrograms, setLoadingPrograms] = useState<boolean>(true);
  const [loadingSetupStatus, setLoadingSetupStatus] = useState<boolean>(false);
  const [userId, setUserId] = useState("");
  const [isChatVisible, setIsChatVisible] = useState<boolean>(false);
  const [userName, setUserName] = useState<string>("");

  const router = useRouter();
  const { selectedProgramId } = useGlobalSearchParams<{
    selectedProgramId?: string;
  }>();

  const [selectedDay, setSelectedDay] = useState<number>(1);

  const [hasPreselected, setHasPreselected] = useState(false);

  const [participantsList, setParticipantsList] = useState<ParticipantList[]>(
    []
  );

  const [submissionTriggered, setSubmissionTriggered] = useState(false);

  const [loading, setLoading] = useState<boolean>(true);

  const [currentDay, setCurrentDay] = useState<number>(1);

  const [days, setDays] = useState<DayTile[]>([]);

  // First, add a state to track participants status for the selected day
  const [selectedDayParticipants, setSelectedDayParticipants] = useState<any[]>(
    []
  );

  // Add this new state at the top with other state declarations
  const [endedProgramParticipants, setEndedProgramParticipants] =
    useState<number>(0);

  const [isUserDisqualified, setIsUserDisqualified] = useState<boolean>(false);

  // Add a new state for disqualification reason
  const [disqualifyReason, setDisqualifyReason] = useState<string | null>(null);

  // Add loading state for archive operation
  const [isArchiving, setIsArchiving] = useState<boolean>(false);

  // Calendar-based progress system state
  const [useCalendarView, setUseCalendarView] = useState<boolean>(true);
  const [selectedCalendarDate, setSelectedCalendarDate] = useState<string>('');
  const [selectedCalendarPrograms, setSelectedCalendarPrograms] = useState<any[]>([]);
  const [selectedCalendarCommits, setSelectedCalendarCommits] = useState<any[]>([]);
  const [attentionModalVisible, setAttentionModalVisible] = useState<boolean>(false);
  const [attentionProgram, setAttentionProgram] = useState<ProgramTimelineData | null>(null);

  // Setup modal state
  const [setupBottomSheetVisible, setSetupBottomSheetVisible] = useState(false);

  // UI preference state - can be moved to context/settings later
  const [useFloatingSwitch, setUseFloatingSwitch] = useState<boolean>(false);
  const [programsData, setProgramsData] = useState<{ [programId: string]: Program }>({});
  const [userDaysData, setUserDaysData] = useState<{ [programId: string]: DayTile[] }>({});
  const [participantsData, setParticipantsData] = useState<{ [programId: string]: ParticipantList[] }>({});

  // Animation for smooth view transitions
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Helper function to check if user has any active content (programs or commits)
  const hasActiveContent = () => {
    return signedUpPrograms.length > 0 || commits.filter(c => c.status === 'active').length > 0;
  };

  // List view specific state - only loaded when needed
  const [listViewInitialized, setListViewInitialized] = useState<boolean>(false);

  // Calendar data loading state
  const [calendarDataLoading, setCalendarDataLoading] = useState<boolean>(false);

  // Calendar-specific state for feature parity
  const [calendarDisqualificationReasons, setCalendarDisqualificationReasons] = useState<{ [programId: string]: string | null }>({});
  const [calendarArchiveLoading, setCalendarArchiveLoading] = useState<{ [programId: string]: boolean }>({});
  const [calendarSetupLoading, setCalendarSetupLoading] = useState<{ [programId: string]: boolean }>({});
  const [calendarEndedParticipants, setCalendarEndedParticipants] = useState<{ [programId: string]: number }>({});

  // Commit-related state
  const [commits, setCommits] = useState<Commit[]>([]);
  const [userCommits, setUserCommits] = useState<UserCommit[]>([]);
  const [selectedCommit, setSelectedCommit] = useState<Commit | null>(null);
  const [loadingCommits, setLoadingCommits] = useState<boolean>(false);

  useEffect(() => {
    const fetchUserName = async () => {
      const fname = await getFname();
      setUserName(fname || "Unknown User");
    };
    fetchUserName();
  }, []);

  // Only load list-specific data when list view is active
  useEffect(() => {
    if (!useCalendarView && selectedProgram) {
      if (selectedProgram.startDate) {
        const userTimezone = getUserTimezone();
        const current = calculateCurrentDayWithTimezone(
          selectedProgram.startDate,
          userTimezone,
          (selectedProgram.status || 'ongoing') as 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified'
        );
        setCurrentDay(current);
        setSelectedDay(current); // initialize selectedDay to current day
      }

      const fetchSetupStatus = async () => {
        setLoadingSetupStatus(true);
        try {
          const userId = await getId();
          if (!userId) return;

          const result = await firestoreService.participants.getParticipant(selectedProgram.id, userId);
          if (result.success && result.data) {
            const participantData = result.data;
            setSetupStatus(participantData.setupStatus);
            // Check disqualification status here
            setIsUserDisqualified(participantData.disqualified === true);
            setDisqualifyReason(participantData.disqualifyReason || null);
          } else {
            setSetupStatus(null);
            setIsUserDisqualified(false);
            setDisqualifyReason(null);
          }
        } catch (error) {
          console.error("Error fetching setup status:", error);
          setSetupStatus(null);
          setIsUserDisqualified(false);
          setDisqualifyReason(null);
        } finally {
          setLoadingSetupStatus(false);
        }
      };

      const fetchEndedProgramParticipants = async () => {
        if (selectedProgram.status !== "ended") return;

        try {
          const result = await firestoreService.participants.getParticipantCount(selectedProgram.id);
          if (result.success) {
            setEndedProgramParticipants(result.data || 0);
          }
        } catch (error) {
          console.error("Error fetching ended program participants:", error);
        }
      };

      fetchSetupStatus();
      fetchEndedProgramParticipants();
      setListViewInitialized(true);
    }
  }, [selectedProgram, useCalendarView]);

  // Only load participants day status for list view
  useEffect(() => {
    const fetchParticipantsDayStatus = async () => {
      if (
        !selectedProgram ||
        selectedProgram.status !== "ongoing" ||
        !setupStatus ||
        useCalendarView // Don't load for calendar view
      )
        return;

      setLoading(true);

      try {
        const { id: programId, startDate } = selectedProgram;
        const userTimezone = getUserTimezone();
        const currentDay = calculateCurrentDayWithTimezone(
          startDate,
          userTimezone,
          selectedProgram.status || 'ongoing'
        );
        setCurrentDay(currentDay);

        // Use centralized service to get participants day status
        const result = await firestoreService.getParticipantsDayStatus(
          programId,
          userId,
          currentDay,
          selectedDay
        );

        if (result.success && result.data) {
          const { currentDayParticipants, selectedDayParticipants, userDays } = result.data;

          setParticipantsList(currentDayParticipants);
          setDays(userDays);

          if (selectedDayParticipants) {
            setSelectedDayParticipants(selectedDayParticipants);
          }
        } else {
          console.error("Error fetching participants day status:", result.error);
        }
      } catch (error) {
        console.error("Error fetching participants day status:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchParticipantsDayStatus();
  }, [selectedProgram, setupStatus, submissionTriggered, selectedDay, userId, useCalendarView]);

  // Refresh calendar data when submission occurs
  useEffect(() => {
    const refreshCalendarDataAfterSubmission = async () => {
      if (!useCalendarView || !submissionTriggered || selectedCalendarPrograms.length === 0 || !userId) {
        return;
      }

      setCalendarDataLoading(true);
      const updatedUserDaysData = { ...userDaysData };
      const updatedParticipantsData = { ...participantsData };
      let hasNewData = false;

      try {
        // Refresh data for all programs on the selected calendar date
        const batchRequests = selectedCalendarPrograms.map(programDay => ({
          programId: programDay.programId,
          userId,
          currentDay: programDay.programDay,
          selectedDay: programDay.programDay
        }));

        const batchResult = await firestoreService.getBatchParticipantsDayStatus(batchRequests);

        if (batchResult.success && batchResult.data) {
          Object.entries(batchResult.data).forEach(([programId, data]) => {
            if (data) {
              updatedUserDaysData[programId] = data.userDays || [];
              updatedParticipantsData[programId] = data.selectedDayParticipants?.map((p: any) => ({
                id: p.id,
                fname: p.fname,
                status: p.status,
                borderColor: p.borderColor,
                livesLeft: p.livesLeft || 0,
                livesPurchaseLeft: p.livesPurchaseLeft || 0
              })) || [];
              hasNewData = true;
            }
          });

          if (hasNewData) {
            setUserDaysData(updatedUserDaysData);
            setParticipantsData(updatedParticipantsData);
          }
        }
      } catch (error) {
        console.error("Error refreshing calendar data after submission:", error);
      } finally {
        setCalendarDataLoading(false);
      }
    };

    refreshCalendarDataAfterSubmission();
  }, [submissionTriggered, useCalendarView, selectedCalendarPrograms, userId]);

  const fetchSignedUpPrograms = useCallback(async () => {
    setLoadingPrograms(true);
    try {
      const id = await getId();
      setUserId(String(id));

      // Use centralized service to get user programs with participant data
      const result = await firestoreService.getUserProgramsWithParticipantData(String(id));

      if (result.success && result.data) {
        const programs = result.data as Program[];

        // Only update state if data has actually changed
        setSignedUpPrograms(prevPrograms => {
          if (JSON.stringify(prevPrograms) !== JSON.stringify(programs)) {
            // Clear calendar caches when programs change
            clearCalendarCaches();
            return programs;
          }
          return prevPrograms;
        });

        if (programs.length > 0) {
          if (selectedProgramId && !hasPreselected) {
            const prog = programs.find((p) => p.id === selectedProgramId);
            setSelectedProgram(prog || programs[0]);
            setHasPreselected(true);
            // Clear the query parameter from the URL so it doesn't persist.
            router.setParams({ selectedProgramId: undefined });
          } else {
            // Check if current selected program still exists in the list
            const currentProgramExists = selectedProgram && programs.find(p => p.id === selectedProgram.id);
            if (!currentProgramExists) {
              setSelectedProgram(programs[0]);
            } else {
              setSelectedProgram((prev) => prev || programs[0]);
            }
          }
        } else {
          // No programs left, clear program selection
          setSelectedProgram(null);

          // Auto-select first active commit if available and no commit is currently selected
          if (!selectedCommit && commits.length > 0) {
            const activeCommits = commits.filter(c => c.status === 'active');
            if (activeCommits.length > 0) {
              setSelectedCommit(activeCommits[0]);
            }
          }
        }
      } else {
        console.error("Error fetching signed-up programs:", result.error);
      }
    } catch (error) {
      console.error("Error fetching signed-up programs:", error);
    } finally {
      setLoadingPrograms(false);
    }
  }, [selectedProgramId, router, hasPreselected]);



  // Fetch user commits
  const fetchCommits = useCallback(async () => {
    setLoadingCommits(true);
    try {
      const id = await getId();
      if (!id) {
        setCommits([]);
        setUserCommits([]);
        return;
      }

      // Fetch both commits and user commits
      const [commitsResult, userCommitsResult] = await Promise.all([
        firestoreService.commits.getCommitsByUser(String(id)),
        firestoreService.commits.getUserCommits(String(id))
      ]);

      if (commitsResult.success && commitsResult.data) {
        setCommits(commitsResult.data);

        // Auto-select first active commit if no programs are available
        if (signedUpPrograms.length === 0 && !selectedCommit) {
          const activeCommits = commitsResult.data.filter(c => c.status === 'active');
          if (activeCommits.length > 0) {
            setSelectedCommit(activeCommits[0]);
          }
        }
      } else {
        console.error("Error fetching commits:", commitsResult.error);
        setCommits([]);
      }

      if (userCommitsResult.success && userCommitsResult.data) {
        setUserCommits(userCommitsResult.data);
      } else {
        console.error("Error fetching user commits:", userCommitsResult.error);
        setUserCommits([]);
      }
    } catch (error) {
      console.error("Error fetching commits:", error);
      setCommits([]);
      setUserCommits([]);
    } finally {
      setLoadingCommits(false);
    }
  }, []);

  // Only fetch on initial mount, not on every focus
  useEffect(() => {
    fetchSignedUpPrograms();
    fetchCommits();
  }, [fetchSignedUpPrograms, fetchCommits]);



  // Populate programs data for calendar view and prefetch data
  useEffect(() => {
    const initializeCalendarData = async () => {
      const programsMap: { [programId: string]: Program } = {};
      const userDaysMap: { [programId: string]: DayTile[] } = {};
      const participantsMap: { [programId: string]: ParticipantList[] } = {};

      // Set up programs data immediately
      signedUpPrograms.forEach(program => {
        programsMap[program.id] = program;
        userDaysMap[program.id] = [];
        participantsMap[program.id] = [];
      });

      setProgramsData(programsMap);
      setUserDaysData(userDaysMap);
      setParticipantsData(participantsMap);

      // Prefetch data for today's programs to reduce loading time
      if (userId && signedUpPrograms.length > 0) {
        const today = new Date().toISOString().split('T')[0];
        const todayPrograms = signedUpPrograms.filter(program => {
          if (!program.startDate || !program.endDate) return false;
          const startDate = new Date(program.startDate);
          const endDate = new Date(program.endDate);
          const todayDate = new Date(today);
          return todayDate >= startDate && todayDate <= endDate;
        });

        // Prefetch data for today's programs using batch function
        const prefetchRequests = todayPrograms.map(program => {
          const startDate = new Date(program.startDate!);
          const todayDate = new Date(today);
          const daysDiff = Math.floor((todayDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          const programDay = daysDiff + 1;

          return {
            programId: program.id,
            userId,
            currentDay: programDay,
            selectedDay: programDay
          };
        });

        if (prefetchRequests.length > 0) {
          const batchResult = await firestoreService.getBatchParticipantsDayStatus(prefetchRequests);

          if (batchResult.success && batchResult.data) {
            // Update state with prefetched data
            const updatedUserDaysData = { ...userDaysMap };
            const updatedParticipantsData = { ...participantsMap };

            Object.entries(batchResult.data).forEach(([programId, data]) => {
              if (data) {
                updatedUserDaysData[programId] = data.userDays || [];
                updatedParticipantsData[programId] = data.selectedDayParticipants?.map((p: any) => ({
                  id: p.id,
                  fname: p.fname,
                  status: p.status,
                  borderColor: p.borderColor,
                  livesLeft: p.livesLeft || 0,
                  livesPurchaseLeft: p.livesPurchaseLeft || 0
                })) || [];
              }
            });

            setUserDaysData(updatedUserDaysData);
            setParticipantsData(updatedParticipantsData);
          }
        }
      }
    };

    initializeCalendarData();
  }, [signedUpPrograms, userId]);

  // Fetch data for programs when calendar date is selected (only if not already cached)
  useEffect(() => {
    const fetchCalendarProgramsData = async () => {
      if (!selectedCalendarDate || selectedCalendarPrograms.length === 0 || !userId) return;

      // Check if we need to fetch any new data
      const needsData = selectedCalendarPrograms.some(programDay =>
        !userDaysData[programDay.programId]?.length ||
        !participantsData[programDay.programId]?.length
      );

      if (!needsData) return;

      setCalendarDataLoading(true);
      const updatedUserDaysData = { ...userDaysData };
      const updatedParticipantsData = { ...participantsData };
      let hasNewData = false;

      try {
        // Filter programs that need data fetching
        const programsNeedingData = selectedCalendarPrograms.filter(programDay =>
          !userDaysData[programDay.programId]?.length ||
          !participantsData[programDay.programId]?.length
        );

        if (programsNeedingData.length === 0) return;

        // Use batch function for better performance
        const batchRequests = programsNeedingData.map(programDay => ({
          programId: programDay.programId,
          userId,
          currentDay: programDay.programDay,
          selectedDay: programDay.programDay
        }));

        const batchResult = await firestoreService.getBatchParticipantsDayStatus(batchRequests);

        if (batchResult.success && batchResult.data) {
          Object.entries(batchResult.data).forEach(([programId, data]) => {
            if (data) {
              updatedUserDaysData[programId] = data.userDays || [];
              updatedParticipantsData[programId] = data.selectedDayParticipants?.map((p: any) => ({
                id: p.id,
                fname: p.fname,
                status: p.status,
                borderColor: p.borderColor,
                livesLeft: p.livesLeft || 0,
                livesPurchaseLeft: p.livesPurchaseLeft || 0
              })) || [];
              hasNewData = true;
            }
          });

          if (hasNewData) {
            setUserDaysData(updatedUserDaysData);
            setParticipantsData(updatedParticipantsData);
          }
        }
      } finally {
        setCalendarDataLoading(false);
      }
    };

    fetchCalendarProgramsData();
  }, [selectedCalendarDate, selectedCalendarPrograms, userId]);

  // Optional: Add a manual refresh mechanism instead of auto-refresh on focus
  // useFocusEffect(
  //   useCallback(() => {
  //     // Only refetch if data is stale (older than 5 minutes)
  //     const lastFetch = Date.now() - (signedUpPrograms.length > 0 ? 0 : 5 * 60 * 1000);
  //     if (lastFetch > 5 * 60 * 1000) {
  //       fetchSignedUpPrograms();
  //     }
  //   }, [fetchSignedUpPrograms, signedUpPrograms.length])
  // );

  // Calendar event handlers
  const handleCalendarDateSelect = useCallback((date: string, programs: any[], commits: any[]) => {
    setSelectedCalendarDate(date);
    setSelectedCalendarPrograms(programs);
    setSelectedCalendarCommits(commits);
  }, []);

  const handleProgramAttention = (program: ProgramTimelineData) => {
    setAttentionProgram(program);
    setAttentionModalVisible(true);
  };

  // View switching handlers with smooth animations
  const handleSwitchToListView = () => {
    // Fade out, switch view, fade in
    Animated.timing(fadeAnim, {
      toValue: 0.3,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setUseCalendarView(false);
      // Ensure we have a selected program or commit for list view
      if (!selectedProgram && !selectedCommit) {
        if (signedUpPrograms.length > 0) {
          setSelectedProgram(signedUpPrograms[0]);
        } else {
          const activeCommits = commits.filter(c => c.status === 'active');
          if (activeCommits.length > 0) {
            setSelectedCommit(activeCommits[0]);
          }
        }
      }
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  };

  const handleSwitchToCalendarView = () => {
    // Fade out, switch view, fade in
    Animated.timing(fadeAnim, {
      toValue: 0.3,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setUseCalendarView(true);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  };

  const handleAttentionModalClose = () => {
    setAttentionModalVisible(false);
    setAttentionProgram(null);
  };

  const handleSetupComplete = (program: Program) => {
    // Update the programs data
    setProgramsData(prev => ({
      ...prev,
      [program.id]: program,
    }));

    // Refresh the signed up programs
    fetchSignedUpPrograms();
    handleAttentionModalClose();
  };

  // Memoized calculation of future items needing setup
  const futureItemsNeedingSetup = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDateObj = new Date(selectedCalendarDate + 'T00:00:00');
    selectedDateObj.setHours(0, 0, 0, 0);

    // Only show setup prompts when viewing today and there are no ongoing activities
    if (selectedDateObj.getTime() !== today.getTime() || selectedCalendarPrograms.length > 0 || selectedCalendarCommits.length > 0) {
      return { programs: [], commits: [] };
    }

    // Find future programs that need setup
    const futurePrograms = signedUpPrograms.filter(program => {
      if (!program.startDate) return false;
      const startDate = new Date(program.startDate);
      startDate.setHours(0, 0, 0, 0);

      // Program is in the future and needs setup
      return startDate > today &&
             program.status === 'upcoming' &&
             !programsData[program.id]?.setupStatus;
    });

    // Find future commits that need setup
    const futureCommits = commits.filter(commit => {
      if (!commit.schedule?.startDate) return false;
      const startDate = new Date(commit.schedule.startDate);
      startDate.setHours(0, 0, 0, 0);

      // Commit is in the future and needs setup
      return startDate > today &&
             commit.status === 'active' &&
             !commit.setupStatus;
    });

    return { programs: futurePrograms, commits: futureCommits };
  }, [selectedCalendarDate, selectedCalendarPrograms, selectedCalendarCommits, signedUpPrograms, commits, programsData]);

  const totalSetupItems = futureItemsNeedingSetup.programs.length + futureItemsNeedingSetup.commits.length;

  const handleArchiveProgram = async (programId: string) => {
    if (!userId) return;

    try {
      setIsArchiving(true);
      const result = await firestoreService.users.archiveUserProgram(userId, programId);

      if (result.success) {
        // Refresh the programs list
        fetchSignedUpPrograms();
        handleAttentionModalClose();
      } else {
        console.error('Failed to archive program:', result.error);
      }
    } catch (error) {
      console.error('Error archiving program:', error);
    } finally {
      setIsArchiving(false);
    }
  };

  // Calendar-specific archive handler
  const handleCalendarArchive = async (programId: string) => {
    if (!userId) return;

    setCalendarArchiveLoading(prev => ({ ...prev, [programId]: true }));
    try {
      const result = await firestoreService.users.archiveUserProgram(userId, programId);
      if (result.success) {
        // Refresh the programs list
        fetchSignedUpPrograms();
      } else {
        console.error('Calendar archive failed:', result.error);
      }
    } catch (error) {
      console.error('Calendar archive error:', error);
    } finally {
      setCalendarArchiveLoading(prev => ({ ...prev, [programId]: false }));
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Header />
      <View style={[styles.container, { position: 'relative' }]}>
        {loadingPrograms ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : hasActiveContent() ? (
          <>
            {/* Conditional View Switch - Tab or Hidden based on preference */}
            {!useFloatingSwitch && (
              <TabViewSwitch
                useCalendarView={useCalendarView}
                onToggle={(useCalendar) => {
                  if (useCalendar) {
                    handleSwitchToCalendarView();
                  } else {
                    handleSwitchToListView();
                  }
                }}
                programCount={signedUpPrograms.length + commits.filter(c => c.status === 'active').length}
                style={{ width: '100%' }}
              />
            )}

            <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
              {useCalendarView ? (
                <>
                  {/* New Calendar View */}
                  <NewCalendar
                    programs={signedUpPrograms}
                    commits={commits}
                    onDateSelect={handleCalendarDateSelect}
                    selectedDate={selectedCalendarDate}
                    userDaysData={userDaysData}
                  />

                {/* Calendar Date Details */}
                {selectedCalendarDate ? (
                  <NewCalendarDetails
                    selectedDate={selectedCalendarDate}
                    programs={selectedCalendarPrograms}
                    commits={selectedCalendarCommits}
                    fullCommits={commits}
                    userId={userId}
                    userName={userName}
                    onProgramAttention={(programId) => {
                      const program = signedUpPrograms.find(p => p.id === programId);
                      if (program) {
                        handleProgramAttention({
                          programId: program.id,
                          programName: program.name,
                          startDate: program.startDate || '',
                          endDate: program.endDate || '',
                          status: program.status || 'upcoming',
                          setupStatus: program.setupStatus || false,
                          category: program.category || '',
                          betAmount: program.betAmount,
                          currentDay: 1,
                          totalDays: typeof program.duration === 'string'
                            ? parseInt(program.duration, 10)
                            : program.duration,
                          needsAttention: true,
                          attentionReason: program.status === 'upcoming' ? 'setup' :
                                          program.status === 'disqualified' ? 'disqualified' : 'ended',
                        });
                      }
                    }}
                    programsData={programsData}
                    userDaysData={userDaysData}
                    participantsData={participantsData}
                    loading={loading || calendarDataLoading}
                    submissionTriggered={submissionTriggered}
                    onSubmissionTrigger={() => setSubmissionTriggered(!submissionTriggered)}
                    onSetupStatusUpdate={(programId, status) => {
                      // Update the program's setupStatus in programsData
                      setProgramsData(prev => ({
                        ...prev,
                        [programId]: { ...prev[programId], setupStatus: status }
                      }));
                    }}
                    onProgramUpdate={(program) => {
                      // Update the program in signedUpPrograms
                      setSignedUpPrograms(prev =>
                        prev.map(p => p.id === program.id ? program : p)
                      );
                      // Also update programsData
                      setProgramsData(prev => ({
                        ...prev,
                        [program.id]: program
                      }));
                    }}
                    onSignedUpProgramsUpdate={setSignedUpPrograms}
                    onArchive={handleCalendarArchive}
                    archiveLoading={calendarArchiveLoading}
                    setupLoading={calendarSetupLoading}
                  />
                ) : (
                  <View style={styles.emptyCalendarState}>
                    <MaterialIcons name="event" size={64} color={colors.textMuted} />
                    <Text style={styles.emptyCalendarTitle}>Select a Date</Text>
                    <Text style={styles.emptyCalendarSubtitle}>
                      Tap on a date in the calendar above to view your programs
                    </Text>
                  </View>
                )}
              </>
            ) : (
              <>
                {/* Original Dropdown View */}
                <ProgramDropdown
                  programs={signedUpPrograms}
                  commits={commits}
                  selectedProgram={selectedProgram}
                  selectedCommit={selectedCommit}
                  isOpen={isDropdownOpen}
                  onToggle={() => setIsDropdownOpen(!isDropdownOpen)}
                  onSelect={(prog) => {
                    setSelectedProgram(prog as Program);
                    setSelectedCommit(null); // Clear commit selection when program is selected
                    setIsDropdownOpen(false);
                  }}
                  onSelectCommit={(commit) => {
                    setSelectedCommit(commit);
                    setSelectedProgram(null); // Clear program selection when commit is selected
                    setIsDropdownOpen(false);
                  }}
                />

                {/* List View Content - Program or Commit */}
                {selectedCommit ? (
                  <View style={{ flex: 1 }}>
                    {/* Day Progress for Commit - Outside the main content */}
                    <View style={styles.commitDayProgressContainer}>
                      <InfoButton selectedCommitId={selectedCommit.id}/>
                      <Text style={styles.commitDayProgressText}>
                        {(() => {
                          const startDate = new Date(selectedCommit.schedule.startDate);
                          const today = new Date();
                          today.setHours(0, 0, 0, 0);
                          startDate.setHours(0, 0, 0, 0);

                          // Calculate total duration using the same logic as CommitService
                          let totalDuration = 1;
                          const frequency = selectedCommit.schedule.frequency;
                          const duration = selectedCommit.schedule.duration;
                          const endDate = selectedCommit.schedule.endDate;

                          if (frequency === 'daily') {
                            if (endDate && selectedCommit.schedule.startDate) {
                              const start = new Date(selectedCommit.schedule.startDate);
                              const end = new Date(endDate);
                              totalDuration = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
                            } else if (duration) {
                              totalDuration = duration;
                            }
                          } else if (frequency === 'weekly') {
                            totalDuration = duration || 1;
                          } else if (frequency === 'monthly') {
                            totalDuration = duration || 1;
                          } else if (frequency === 'once') {
                            totalDuration = 1;
                          }

                          // Check if commit has started yet
                          if (today < startDate) {
                            const daysUntilStart = Math.ceil((startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                            if (daysUntilStart === 1) {
                              return 'Starts tomorrow';
                            } else {
                              return `Starts in ${daysUntilStart} days`;
                            }
                          }

                          if (frequency === 'once') {
                            return 'Day 1 of 1';
                          } else if (frequency === 'daily') {
                            const daysDiff = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                            const currentDay = Math.max(1, daysDiff + 1);
                            return `Day ${currentDay} of ${totalDuration}`;
                          } else if (frequency === 'weekly') {
                            const weeksDiff = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
                            const currentWeek = Math.max(1, weeksDiff + 1);
                            return `Week ${currentWeek} of ${totalDuration}`;
                          } else if (frequency === 'monthly') {
                            const monthsDiff = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
                            const currentMonth = Math.max(1, monthsDiff + 1);
                            return `Month ${currentMonth} of ${totalDuration}`;
                          }
                          return 'Day 1 of 1';
                        })()}
                      </Text>
                    </View>

                    {/* Render commit content using CommitInput component */}
                    <CommitInput
                      commit={selectedCommit}
                      userId={userId}
                      selectedDate={undefined} // Use today's date for list view
                      onSubmissionSuccess={() => setSubmissionTriggered(!submissionTriggered)}
                      onCommitUpdate={(updatedCommit) => {
                        // Update the commits array with the updated commit
                        setCommits(prev => prev.map(commit =>
                          commit.id === updatedCommit.id ? updatedCommit : commit
                        ));
                        // Also update selectedCommit if it's the same one
                        if (selectedCommit.id === updatedCommit.id) {
                          setSelectedCommit(updatedCommit);
                        }
                      }}
                      isLoading={false}
                      refreshTrigger={submissionTriggered ? 1 : 0}
                    />
                  </View>
                ) : !listViewInitialized ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={colors.primary} />
                    <Text style={styles.messageText}>Loading program details...</Text>
                  </View>
                ) : loadingSetupStatus ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
              </View>
            ) : selectedProgram && isUserDisqualified ? (
              <>
                <DisqualifiedMessage
                  disqualificationReason={disqualifyReason}
                  containerStyle={styles.messageContainer}
                />

                {/* Archive and Explore buttons at the bottom */}
                <View style={styles.actionButtonsContainer}>
                  <View style={styles.buttonWrapper}>
                    <TouchableOpacity
                      style={[styles.archiveButton, {
                        backgroundColor: colors.surface,
                        borderColor: colors.primary,
                        opacity: isArchiving ? 0.6 : 1
                      }]}
                      disabled={isArchiving}
                      onPress={async () => {
                        if (selectedProgram && userId && !isArchiving) {
                          try {
                            setIsArchiving(true);
                            const result = await firestoreService.users.archiveUserProgram(userId, selectedProgram.id);

                            if (result.success) {
                              // Force a complete page reload by navigating away and back
                              router.replace('/(tabs)');
                              setTimeout(() => {
                                router.replace('/(tabs)/progress');
                              }, 100);
                            } else {
                              console.error('Failed to archive program:', result.error);
                              setIsArchiving(false);
                            }
                          } catch (error) {
                            console.error('Error archiving program:', error);
                            setIsArchiving(false);
                          }
                        }
                      }}
                    >
                      <View style={styles.archiveContent}>
                        <Text style={[styles.archiveMainText, { color: colors.primary }]}>
                          {isArchiving ? 'Archiving...' : 'Archive Program'}
                        </Text>
                        <Text style={[styles.archiveSubText, { color: colors.text }]}>
                          {isArchiving ? 'please wait' : 'hide from list'}
                        </Text>
                      </View>
                      {isArchiving ? (
                        <ActivityIndicator size={20} color={colors.primary} />
                      ) : (
                        <MaterialIcons name="archive" size={20} color={colors.primary} />
                      )}
                    </TouchableOpacity>
                  </View>

                  <View style={styles.buttonWrapper}>
                    <TouchableOpacity
                      style={[styles.exploreButton, { backgroundColor: colors.primary }]}
                      onPress={() => router.replace('/(tabs)')}
                    >
                      <View style={styles.exploreContent}>
                        <Text style={[styles.exploreMainText, { color: '#000' }]}>Pool Challenges</Text>
                        <Text style={[styles.exploreSubText, { color: '#000' }]}>find new challenges</Text>
                      </View>
                      <MaterialIcons name="explore" size={20} color="#000" />
                    </TouchableOpacity>
                  </View>
                </View>
              </>
            ) : selectedProgram &&
              selectedProgram.status === "upcoming" &&
              setupStatus === true ? (
              <View style={styles.messageContainer}>
                <InfoButton selectedProgramId={selectedProgram.id}/>

                <Text style={styles.messageTitle}>Get Ready!</Text>
                <Text style={styles.messageSubtitle}>
                  Your challenge kicks off in{" "}
                  {calculateDaysToStart(selectedProgram.startDate)} days.
                </Text>
                <Text style={styles.messageInfo}>
                  Focus up and prepare to give it your best!
                </Text>
              </View>
            ) : selectedProgram &&
              selectedProgram.status === "upcoming" &&
              setupStatus === false ? (
              <ProgramDayRenderer
                program={selectedProgram}
                programDay={1} // Default to day 1 for upcoming programs
                currentDay={1}
                userId={userId}
                participants={[]} // No participants data needed for setup
                userDays={[]}
                loading={loading}
                showProgramInfo={true}
                setupStatus={setupStatus}
                onSetupStatusUpdate={setSetupStatus}
                onProgramUpdate={setSelectedProgram}
                onSignedUpProgramsUpdate={setSignedUpPrograms}
              />
            ) : selectedProgram &&
              selectedProgram.status === "ongoing" &&
              setupStatus === true ? (
              <View style={{ flex: 1 }}>
                {selectedProgram && (
                  <ProgressBar
                    days={days}
                    currentDay={currentDay}
                    remainingDays={(typeof selectedProgram.duration === 'string'
                      ? parseInt(selectedProgram.duration, 10)
                      : selectedProgram.duration!) - currentDay}
                    selectedDay={selectedDay}
                    onDaySelect={(day) => setSelectedDay(day)}
                  />
                )}

                <ProgramDayRenderer
                  program={selectedProgram}
                  programDay={selectedDay}
                  currentDay={currentDay}
                  userId={userId}
                  participants={selectedDayParticipants} // For ParticipantsStatusDashboard
                  allParticipants={participantsList} // For money calculation
                  userDays={days}
                  loading={loading}
                  submissionTriggered={submissionTriggered}
                  onSubmissionTrigger={() => setSubmissionTriggered(!submissionTriggered)}
                  showProgramInfo={false} // Don't show program info in list view since we have ProgressBar
                />
              </View>
            ) : selectedProgram && selectedProgram.status === "ended" ? (
              <ProgramEnd
                programId={selectedProgram.id}
                duration={typeof selectedProgram.duration === 'string'
                  ? parseInt(selectedProgram.duration, 10)
                  : selectedProgram.duration || 0}
                currentUserId={userId}
                betAmount={Number(selectedProgram.betAmount)}
                totalPlayers={endedProgramParticipants} // Use the new state here
              />
            ) : selectedProgram && isUserDisqualified ? (
              <DisqualifiedMessage
                disqualificationReason={disqualifyReason}
                containerStyle={styles.messageContainer}
              />
            ) : !selectedProgram && !selectedCommit && commits.filter(c => c.status === 'active').length > 0 ? (
              <View style={styles.messageContainer}>
                <Text style={styles.messageTitle}>Select a Commit</Text>
                <Text style={styles.messageSubtitle}>
                  You have active commitments available. Please select one from the dropdown above.
                </Text>
              </View>
            ) : null}
                </>
              )}
            </Animated.View>
          </>
        ) : (
          <>
            {/* Show tabs even when no content */}
            <TabViewSwitch
              useCalendarView={useCalendarView}
              onToggle={(useCalendar) => {
                if (useCalendar) {
                  handleSwitchToCalendarView();
                } else {
                  handleSwitchToListView();
                }
              }}
              programCount={0}
              style={{ width: '100%' }}
            />

            <Animated.View style={{ opacity: fadeAnim, flex: 1 }}>
              {useCalendarView ? (
                <>
                  {/* Empty Calendar View */}
                  <NewCalendar
                    programs={[]}
                    commits={[]}
                    onDateSelect={handleCalendarDateSelect}
                    selectedDate={selectedCalendarDate}
                    userDaysData={{}}
                  />

                  {/* Marketing content for empty calendar */}
                  <View style={styles.emptyStateMarketingContainer}>
                    <MaterialIcons name="event" size={48} color={colors.primary} />
                    <Text style={styles.emptyStateTitle}>This looks empty!</Text>
                    <Text style={styles.emptyStateSubtitle}>
                      Keep yourself engaged with commitments and Pool Challenges
                    </Text>
                    <Text style={styles.emptyStateDescription}>
                      Pool Challenges let you earn money by competing with others, while commitments give you the flexibility to track personal goals.
                    </Text>

                    <View style={styles.emptyStateButtonsContainer}>
                      <TouchableOpacity
                        style={[styles.emptyStateButton, styles.poolChallengesButton]}
                        onPress={() => router.replace('/(tabs)')}
                      >
                        <MaterialIcons name="pool" size={20} color="#000" />
                        <Text style={styles.poolChallengesButtonText}>Pool Challenges</Text>
                        <Text style={styles.poolChallengesButtonSubtext}>Earn money</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.emptyStateButton, styles.commitmentsButton]}
                        onPress={() => router.push('/(tabs)/commits')}
                      >
                        <MaterialIcons name="track-changes" size={20} color={colors.primary} />
                        <Text style={styles.commitmentsButtonText}>Commitments</Text>
                        <Text style={styles.commitmentsButtonSubtext}>Stay flexible</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </>
              ) : (
                <>
                  {/* Empty List View */}
                  <View style={styles.emptyStateMarketingContainer}>
                    <MaterialIcons name="view-list" size={48} color={colors.primary} />
                    <Text style={styles.emptyStateTitle}>This looks empty!</Text>
                    <Text style={styles.emptyStateSubtitle}>
                      Keep yourself engaged with commitments and Pool Challenges
                    </Text>
                    <Text style={styles.emptyStateDescription}>
                      Pool Challenges let you earn money by competing with others, while commitments give you the flexibility to track personal goals.
                    </Text>

                    <View style={styles.emptyStateButtonsContainer}>
                      <TouchableOpacity
                        style={[styles.emptyStateButton, styles.poolChallengesButton]}
                        onPress={() => router.replace('/(tabs)')}
                      >
                        <MaterialIcons name="pool" size={20} color="#000" />
                        <Text style={styles.poolChallengesButtonText}>Pool Challenges</Text>
                        <Text style={styles.poolChallengesButtonSubtext}>Earn money</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.emptyStateButton, styles.commitmentsButton]}
                        onPress={() => router.push('/(tabs)/commits')}
                      >
                        <MaterialIcons name="track-changes" size={20} color={colors.primary} />
                        <Text style={styles.commitmentsButtonText}>Commitments</Text>
                        <Text style={styles.commitmentsButtonSubtext}>Stay flexible</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </>
              )}
            </Animated.View>
          </>
        )}
      </View>

      {/* Quick View Switch - Show floating button when enabled or always show as alternative */}
      {hasActiveContent() && (
        <QuickViewSwitch
          useCalendarView={useCalendarView}
          onToggle={() => {
            if (useCalendarView) {
              handleSwitchToListView();
            } else {
              handleSwitchToCalendarView();
            }
          }}
          style={{ bottom: 85 }} // Reduced gap with message button
        />
      )}

      {/* Chat Button - Show for ongoing programs in both views */}
      {(
        (useCalendarView && selectedCalendarPrograms.some(p =>
          programsData[p.programId]?.status === 'ongoing'
        )) ||
        (!useCalendarView && selectedProgram?.status === "ongoing")
      ) && (
        <TouchableOpacity
          style={[styles.chatButton, { backgroundColor: colors.surface }]}
          onPress={() => setIsChatVisible(true)}
        >
          <MaterialIcons name="chat" size={24} color={colors.primary} />
        </TouchableOpacity>
      )}
      <ChatModal
        userId={userId}
        userName={userName}
        isChatVisible={isChatVisible}
        selectedProgram={
          useCalendarView
            ? (selectedCalendarPrograms.length > 0
                ? programsData[selectedCalendarPrograms[0].programId]
                : null)
            : selectedProgram
        }
        chatVisibler={(chatVisible) => {
          setIsChatVisible(chatVisible);
        }}
      />

      {/* Attention Modal */}
      <AttentionModal
        visible={attentionModalVisible}
        onClose={handleAttentionModalClose}
        program={attentionProgram}
        fullProgramData={attentionProgram ? programsData[attentionProgram.programId] : undefined}
        onSetupComplete={handleSetupComplete}
        onArchiveProgram={handleArchiveProgram}
        isArchiving={isArchiving}
      />

      {/* Floating Setup Button - Only show in calendar view */}
      {useCalendarView && totalSetupItems > 0 && (
        <FloatingSetupButton
          visible={true}
          itemCount={totalSetupItems}
          onPress={() => {
            setSetupBottomSheetVisible(true);
          }}
        />
      )}

      {/* Setup Bottom Sheet */}
      <SetupBottomSheet
        visible={setupBottomSheetVisible}
        onClose={() => setSetupBottomSheetVisible(false)}
        programs={futureItemsNeedingSetup.programs}
        commits={futureItemsNeedingSetup.commits}
        onProgramSetup={(programId) => {
          // Find the program and trigger attention modal
          const program = signedUpPrograms.find(p => p.id === programId);
          if (program) {
            handleProgramAttention({
              programId: program.id,
              programName: program.name,
              startDate: program.startDate || '',
              endDate: program.endDate || '',
              status: program.status || 'upcoming',
              setupStatus: program.setupStatus || false,
              category: program.category || '',
              betAmount: program.betAmount,
              currentDay: 1,
              totalDays: typeof program.duration === 'string'
                ? parseInt(program.duration, 10)
                : program.duration,
              needsAttention: true,
              attentionReason: 'setup',
            });
          }
        }}
        onCommitSetup={(commitId) => {
          // Navigate to commit setup - could be implemented later
        }}
        onSetupComplete={() => {
          // Refresh data after setup completion
          fetchSignedUpPrograms();
          fetchCommits();
        }}
      />
    </View>
  );
}

// Create styles function that takes theme parameters
const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    padding: 20,
    alignItems: "center",
  },
  messageText: {
    color: colors.text,
    fontSize: 18,
    textAlign: "center",
    marginBottom: 10,
  },
  programDetails: {
    marginTop: 5,
    padding: 10,
    backgroundColor: colors.surface,
    borderRadius: 10,
  },
  detailsTitle: {
    color: colors.primary,
    fontSize: 20,
    fontFamily: "MontserratBold",
    marginBottom: 10,
  },
  noProgramsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  noProgramsText: {
    color: colors.text,
    fontSize: 20,
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginBottom: 25,
    lineHeight: 28,
  },
  exploreButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: "MontserratBold",
    letterSpacing: 0.5,
  },
  inputBlock: {
    paddingHorizontal: 20,
    backgroundColor: colors.surface,
    borderRadius: 10,
    marginTop: 10,
  },
  messageContainer: {
    marginVertical: 10,
    marginHorizontal: 10,
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
    alignItems: "center",
  },
  messageTitle: {
    fontSize: 20,
    color: colors.text,
    fontFamily: "MontserratBold",
    marginBottom: 6,
  },
  messageSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 6,
  },
  messageInfo: {
    fontFamily: "MontserratRegular",
    color: colors.textMuted,
    fontSize: 14,
    textAlign: "center",
    marginTop: 10,
    paddingHorizontal: 20,
    lineHeight: 20,
  },
  summaryButton: {
    marginTop: 10,
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  summaryButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
  chatButton: {
    position: "absolute",
    right: 20,
    bottom: 20,
    backgroundColor: colors.surface,
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  disputeButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginTop: 15,
    alignSelf: "center",
  },
  disputeButtonText: {
    fontFamily: "MontserratBold",
    color: colors.background,
    fontSize: 14,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    marginHorizontal: 12,
    marginVertical: 12,
    gap: 6,
  },
  buttonWrapper: {
    flex: 1,
    height: 50, // Consistent height for both buttons
  },
  archiveButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  archiveContent: {
    justifyContent: 'center',
  },
  archiveMainText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 2,
  },
  archiveSubText: {
    fontSize: 9,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    opacity: 0.8,
  },
  exploreButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  exploreContent: {
    justifyContent: 'center',
  },
  exploreMainText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.background,
    marginBottom: 2,
  },
  exploreSubText: {
    fontSize: 9,
    fontFamily: 'MontserratRegular',
    color: colors.background,
    opacity: 0.8,
  },
  emptyCalendarState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 40,
  },
  emptyCalendarTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyCalendarSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    lineHeight: 20,
  },
  commitDayProgressContainer: {
    alignItems: 'center',
    marginBottom: 4,
    paddingHorizontal: 20,
    position: 'relative',
  },
  commitDayProgressText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.textMuted,
    textAlign: 'center',
  },
  noProgramsExploreButton: {
    backgroundColor: colors.primary,
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Empty state marketing styles
  emptyStateMarketingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    paddingTop: 40,
  },
  emptyStateTitle: {
    color: colors.text,
    fontSize: 24,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    color: colors.text,
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    textAlign: 'center',
    marginBottom: 12,
  },
  emptyStateDescription: {
    color: colors.textMuted,
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  emptyStateButtonsContainer: {
    flexDirection: 'row',
    gap: 16,
    width: '100%',
    paddingHorizontal: 8,
  },
  emptyStateButton: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  poolChallengesButton: {
    backgroundColor: colors.primary,
  },
  poolChallengesButtonText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'MontserratBold',
    marginTop: 4,
    marginBottom: 2,
  },
  poolChallengesButtonSubtext: {
    color: '#000',
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    opacity: 0.8,
  },
  commitmentsButton: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  commitmentsButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontFamily: 'MontserratBold',
    marginTop: 4,
    marginBottom: 2,
  },
  commitmentsButtonSubtext: {
    color: colors.primary,
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    opacity: 0.8,
  },


});
