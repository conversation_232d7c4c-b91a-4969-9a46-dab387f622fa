import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import * as Notifications from "expo-notifications";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "../shared/contexts/ThemeContext";
import Header from "../ui/common/Header";
import { SuccessModal, ErrorModal } from "../shared/components/modals";

const PushNotifications: React.FC = () => {
  const router = useRouter();
  const { colors, isDark, designSystem } = useTheme();
  
  // Always-on notifications (untogglable)
  const alwaysOnValue = true;
  
  // Togglable notifications
  const [programAlerts, setProgramAlerts] = useState(true);
  const [marketingNotifications, setMarketingNotifications] = useState(false);
  const [chatRoomNotifications, setChatRoomNotifications] = useState(false);

  // State to track notification permissions
  const [permissionGranted, setPermissionGranted] = useState(false);

  // Modal states for replacing Alert dialogs
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '' });
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });

  useEffect(() => {
    const checkPermissions = async () => {
      const { status } = await Notifications.getPermissionsAsync();
      setPermissionGranted(status === "granted");
    };
    checkPermissions();
  }, []);

  const requestPermission = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status === "granted") {
      setPermissionGranted(true);
      setSuccessModalData({
        title: "Permission Granted",
        message: "You will now receive notifications."
      });
      setSuccessModalVisible(true);
    } else {
      setErrorModalData({
        title: "Permission Denied",
        message: "Notifications are disabled."
      });
      setErrorModalVisible(true);
    }
  };

  const testNotification = async () => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: "Test Notification",
          body: "This is a sample notification.",
        },
        trigger: null, // fires immediately
      });
      setSuccessModalData({
        title: "Notification Sent",
        message: "A test notification was sent."
      });
      setSuccessModalVisible(true);
    } catch (error) {
      console.error("Error sending test notification:", error);
      setErrorModalData({
        title: "Error",
        message: "Failed to send test notification."
      });
      setErrorModalVisible(true);
    }
  };

  const styles = createStyles(colors, isDark, designSystem);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <LinearGradient
          colors={getCardGradient(isDark, colors)}
          style={styles.headerCard}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.headerContent}>
            <MaterialCommunityIcons name="bell-outline" size={24} color={colors.primary} />
            <Text style={[styles.headerTitle, { color: colors.primary }]}>Push Notifications</Text>
          </View>
          <Text style={[styles.headerSubtitle, { color: colors.textMuted }]}>
            Manage your push notification preferences
          </Text>
        </LinearGradient>

        {/* Always-on Notifications Section */}
        <LinearGradient
          colors={getCardGradient(isDark, colors)}
          style={styles.settingsCard}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="shield-check" size={20} color={colors.success} />
            <Text style={[styles.sectionTitle, { color: colors.success }]}>Essential Notifications</Text>
          </View>

          <View style={[styles.notificationItem, { borderBottomColor: colors.border }]}>
            <View style={styles.notificationLeft}>
              <LinearGradient
                colors={getSuccessGradient(colors)}
                style={styles.notificationIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="alert-circle" size={18} color="#fff" />
              </LinearGradient>
              <View style={styles.notificationTextContainer}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>Critical Program Alerts</Text>
                <Text style={[styles.notificationSubtitle, { color: colors.textMuted }]}>Important updates about your programs</Text>
              </View>
            </View>
            <Switch
              value={alwaysOnValue}
              disabled
              trackColor={{ false: colors.border, true: colors.success }}
              thumbColor={colors.surface}
            />
          </View>

          <View style={[styles.notificationItem, { borderBottomWidth: 0 }]}>
            <View style={styles.notificationLeft}>
              <LinearGradient
                colors={getSuccessGradient(colors)}
                style={styles.notificationIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="bell-ring" size={18} color="#fff" />
              </LinearGradient>
              <View style={styles.notificationTextContainer}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>Reminders</Text>
                <Text style={[styles.notificationSubtitle, { color: colors.textMuted }]}>Daily reminders and deadlines</Text>
              </View>
            </View>
            <Switch
              value={alwaysOnValue}
              disabled
              trackColor={{ false: colors.border, true: colors.success }}
              thumbColor={colors.surface}
            />
          </View>
        </LinearGradient>

        {/* Optional Notifications Section */}
        <LinearGradient
          colors={getCardGradient(isDark, colors)}
          style={styles.settingsCard}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="tune" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Optional Notifications</Text>
          </View>

          <View style={[styles.notificationItem, { borderBottomColor: colors.border }]}>
            <View style={styles.notificationLeft}>
              <LinearGradient
                colors={getPrimaryGradient(colors)}
                style={styles.notificationIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="trophy" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.notificationTextContainer}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>Program Alerts</Text>
                <Text style={[styles.notificationSubtitle, { color: colors.textMuted }]}>Updates about program progress and results</Text>
              </View>
            </View>
            <Switch
              value={programAlerts}
              onValueChange={setProgramAlerts}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.surface}
            />
          </View>

          <View style={[styles.notificationItem, { borderBottomColor: colors.border }]}>
            <View style={styles.notificationLeft}>
              <LinearGradient
                colors={getPrimaryGradient(colors)}
                style={styles.notificationIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="bullhorn" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.notificationTextContainer}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>Marketing Notifications</Text>
                <Text style={[styles.notificationSubtitle, { color: colors.textMuted }]}>New features and promotional content</Text>
              </View>
            </View>
            <Switch
              value={marketingNotifications}
              onValueChange={setMarketingNotifications}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.surface}
            />
          </View>

          <View style={[styles.notificationItem, { borderBottomWidth: 0 }]}>
            <View style={styles.notificationLeft}>
              <LinearGradient
                colors={getPrimaryGradient(colors)}
                style={styles.notificationIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="chat" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.notificationTextContainer}>
                <Text style={[styles.notificationTitle, { color: colors.text }]}>Chat Room Notifications</Text>
                <Text style={[styles.notificationSubtitle, { color: colors.textMuted }]}>Messages and activity in chat rooms</Text>
              </View>
            </View>
            <Switch
              value={chatRoomNotifications}
              onValueChange={setChatRoomNotifications}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.surface}
            />
          </View>
        </LinearGradient>

        {/* Action Buttons */}
        <TouchableOpacity
          style={styles.testButton}
          onPress={() => router.push('/TestNotifications')}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={getSuccessGradient(colors)}
            style={styles.testButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <MaterialCommunityIcons name="bell-check" size={20} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.testButtonText}>Test Push Notifications</Text>
          </LinearGradient>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={getSecondaryGradient(colors, isDark)}
            style={styles.backButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <MaterialCommunityIcons name="arrow-left" size={20} color={colors.text} style={styles.buttonIcon} />
            <Text style={[styles.backButtonText, { color: colors.text }]}>Back</Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>

      {/* Permission Banner fixed at the bottom */}
      {!permissionGranted && (
        <View style={styles.permissionBannerWrapper}>
          <LinearGradient
            colors={getErrorGradient(colors)}
            style={styles.permissionBanner}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <MaterialCommunityIcons name="shield-alert" size={24} color="#fff" />
            <Text style={styles.permissionText}>
              Please enable notifications to receive updates.
            </Text>
            <TouchableOpacity
              style={styles.permissionButton}
              onPress={requestPermission}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={getPrimaryGradient(colors)}
                style={styles.permissionButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={styles.permissionButtonText}>Grant Permission</Text>
              </LinearGradient>
            </TouchableOpacity>
          </LinearGradient>
        </View>
      )}

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />
    </View>
  );
};

// Helper functions for consistent gradients
const getCardGradient = (isDark: boolean, colors: any): readonly [string, string, ...string[]] =>
  isDark ? [colors.surface, colors.card, colors.surface] as const : [colors.background, colors.surface, colors.card] as const;

const getPrimaryGradient = (colors: any): readonly [string, string, ...string[]] =>
  [colors.primary, colors.warning, colors.primary] as const;

const getSuccessGradient = (colors: any): readonly [string, string, ...string[]] =>
  [colors.success, colors.success + '80', colors.success + '60'] as const;

const getErrorGradient = (colors: any): readonly [string, string, ...string[]] =>
  [colors.error, colors.error + '80', colors.error + '60'] as const;

const getSecondaryGradient = (colors: any, isDark: boolean): readonly [string, string, ...string[]] =>
  isDark ? [colors.surface, colors.card] as const : [colors.card, colors.surface] as const;

const createStyles = (colors: any, isDark: boolean, designSystem: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.md,
  },
  headerCard: {
    marginTop: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.md,
    padding: designSystem.spacing.lg,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    marginBottom: designSystem.spacing.md,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: designSystem.spacing.sm,
  },
  headerTitle: {
    fontSize: designSystem.typography.fontSize.xl,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.md,
  },
  headerSubtitle: {
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: "MontserratRegular",
    lineHeight: 20,
  },
  settingsCard: {
    borderRadius: designSystem.borderRadius.md,
    padding: designSystem.spacing.lg,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    marginBottom: designSystem.spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designSystem.spacing.md,
    paddingBottom: designSystem.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.primary + '20',
  },
  sectionTitle: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm + 2,
    flex: 1,
  },
  notificationItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: designSystem.spacing.md,
    borderBottomWidth: 1,
  },
  notificationLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  notificationIconGradient: {
    width: 36,
    height: 36,
    borderRadius: designSystem.borderRadius.xl,
    justifyContent: "center",
    alignItems: "center",
    marginRight: designSystem.spacing.md,
    ...designSystem.shadows.md,
    shadowColor: colors.primary,
  },
  notificationTextContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: "MontserratBold",
    marginBottom: designSystem.spacing.xs / 4,
  },
  notificationSubtitle: {
    fontSize: designSystem.typography.fontSize.xs + 1,
    fontFamily: "MontserratRegular",
  },
  testButton: {
    marginTop: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.lg,
    ...designSystem.shadows.lg,
    shadowColor: colors.success,
  },
  testButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: designSystem.spacing.md,
    paddingHorizontal: designSystem.spacing.xl,
    borderRadius: designSystem.borderRadius.lg,
  },
  testButtonText: {
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: "MontserratBold",
    color: "#fff",
    marginLeft: designSystem.spacing.sm,
  },
  backButton: {
    marginTop: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.lg,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  backButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: designSystem.spacing.md,
    paddingHorizontal: designSystem.spacing.xl,
    borderRadius: designSystem.borderRadius.lg,
  },
  backButtonText: {
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm,
  },
  buttonIcon: {
    marginRight: designSystem.spacing.xs,
  },
  permissionBannerWrapper: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: designSystem.spacing.lg,
    backgroundColor: "transparent",
  },
  permissionBanner: {
    padding: designSystem.spacing.lg,
    borderRadius: designSystem.borderRadius.lg,
    alignItems: "center",
    ...designSystem.shadows.xl,
    shadowColor: colors.error,
  },
  permissionText: {
    color: "#fff",
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: "MontserratRegular",
    marginVertical: designSystem.spacing.sm,
    textAlign: "center",
  },
  permissionButton: {
    marginTop: designSystem.spacing.sm,
    borderRadius: designSystem.borderRadius.md,
  },
  permissionButtonGradient: {
    paddingVertical: designSystem.spacing.sm,
    paddingHorizontal: designSystem.spacing.lg,
    borderRadius: designSystem.borderRadius.md,
    alignItems: "center",
  },
  permissionButtonText: {
    color: "#000",
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: "MontserratBold",
  },
});

export default PushNotifications;
