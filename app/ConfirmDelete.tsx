import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
} from "react-native";
import { useRouter } from "expo-router";
import { doc, deleteDoc } from "firebase/firestore";
import { db } from "../lib/config/firebase";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { getId, getFname, getLname } from "../lib/utils/variables";
import { useAuth } from "../shared/contexts/AuthContext";
import { ErrorModal, SuccessModal } from "../shared/components/modals";

export default function ConfirmDelete() {
  const { signOut } = useAuth();
  const router = useRouter();
  const [userEmail, setUserEmail] = useState<string>("");
  const [typedValue, setTypedValue] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  // Modal states for replacing Alert dialogs
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '', onPress: () => {} });

  // Fetch the user's email (ID) using getId
  useEffect(() => {
    async function fetchUserEmail() {
      const id = await getId();
      setUserEmail(String(id));
    }
    fetchUserEmail();
  }, []);

  const handleFinalDelete = async () => {
    if (!userEmail) {
      setErrorModalData({
        title: "Error",
        message: "User email not found. Please try again."
      });
      setErrorModalVisible(true);
      return;
    }

    if (typedValue.trim().toLowerCase() !== "delete my account") {
      setErrorModalData({
        title: "Error",
        message: 'Please type "delete my account" exactly.'
      });
      setErrorModalVisible(true);
      return;
    }

    try {
      setIsDeleting(true);

      // 1. Delete user doc from Firestore
      const userDocRef = doc(db, "users", userEmail);
      await deleteDoc(userDocRef);

      // 2. Log user out using AuthContext
      await signOut();

      // 3. Show confirmation (AuthContext will handle redirect)
      setSuccessModalData({
        title: "Account Deleted",
        message: "Your account has been deleted.",
        onPress: () => setSuccessModalVisible(false)
      });
      setSuccessModalVisible(true);
    } catch (error: any) {
      console.error("Error deleting account:", error.message);
      setErrorModalData({
        title: "Error",
        message: "There was an error deleting your account."
      });
      setErrorModalVisible(true);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Profile</Text>

      <View style={styles.profileCard}>
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={40}
          color="#FFEB3B"
          style={{ marginBottom: 10 }}
        />
        <Text style={styles.infoText}>
          If you wish to delete your account, please note the following:
        </Text>
        <Text style={styles.bulletText}>
          • Any active memberships or subscriptions will be cancelled immediately with no refunds.
        </Text>
        <Text style={styles.bulletText}>
          • You will not be able to log into this account after your account is removed.
        </Text>
        <Text style={styles.bulletText}>
          • This cannot be undone. Enter{" "}
          <Text style={{ fontWeight: "bold" }}>"delete my account"</Text> below to proceed.
        </Text>
      </View>

      <TextInput
        style={styles.input}
        placeholder='Type "delete my account"'
        placeholderTextColor="#666"
        value={typedValue}
        onChangeText={setTypedValue}
      />

      <TouchableOpacity
        style={[
          styles.deleteButton,
          typedValue.trim().toLowerCase() !== "delete my account" && {
            backgroundColor: "#555",
          },
        ]}
        onPress={handleFinalDelete}
        disabled={isDeleting}
      >
        {isDeleting ? (
          <Text style={styles.buttonText}>Deleting...</Text>
        ) : (
          <Text style={styles.buttonText}>Delete my account</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
        <Text style={styles.buttonText}>Cancel</Text>
      </TouchableOpacity>

      {/** Optional: Redirect if logged out */}
      {/* {logOutRedirect && <Redirect href="/SignIn" />} */}

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
        onButtonPress={successModalData.onPress}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "black",
    alignItems: "center",
    padding: 20,
  },
  header: {
    fontSize: 30,
    color: "#FFCC00",
    fontFamily: "MontserratBold",
    marginBottom: 20,
    textAlign: "center",
  },
  profileCard: {
    backgroundColor: "#1F1F1F",
    borderRadius: 10,
    padding: 20,
    alignItems: "center",
    boxShadow: '0 5px 10px rgba(0, 0, 0, 0.3)',
    elevation: 8,
    marginBottom: 20,
  },
  infoText: {
    color: "#fff",
    fontSize: 14,
    marginBottom: 10,
    textAlign: "center",
  },
  bulletText: {
    color: "#aaa",
    fontSize: 13,
    marginBottom: 6,
    textAlign: "center",
  },
  input: {
    backgroundColor: "#2a2a2a",
    borderRadius: 8,
    color: "#fff",
    padding: 12,
    marginBottom: 16,
    width: "100%",
  },
  deleteButton: {
    backgroundColor: "#E53935",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 10,
    width: "100%",
  },
  cancelButton: {
    backgroundColor: "#333",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 10,
    width: "100%",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontFamily: "MontserratBold",
  },
});
