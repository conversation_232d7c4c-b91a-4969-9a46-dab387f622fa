import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { firestoreService } from "../lib/services/database";
import { Notification as NotificationType } from "../lib/services/database/types";
import { getId } from "../lib/utils/variables";
import BreathingDot from "../ui/pages/Progress/BreathingDot";
import { ProtectedRoute } from "../ui/common/ProtectedRoute";
import { formatTimeAgo } from "../lib/utils/timestampUtils";

interface NotificationItemProps {
  title: string;
  message: string;
  time: string;
  type: "account" | "program" | "points" | "reminder";
  priority: "low" | "medium" | "high";
  read: boolean;
}

const NotificationItem: React.FC<NotificationItemProps & { onPress: () => void }> = ({ 
  title, 
  message, 
  time, 
  type,
  priority,
  read,
  onPress
}) => (
  <TouchableOpacity onPress={onPress} style={styles.notificationItem}>
    {!read && (
      <View style={styles.unreadDot}>
        <BreathingDot />
      </View>
    )}
    <View style={styles.iconContainer}>
      <MaterialCommunityIcons 
        name={getNotificationIcon(type)}
        size={24} 
        color="#FFCC00" 
      />
    </View>
    <View style={styles.contentContainer}>
      <Text style={styles.notificationTitle}>{title}</Text>
      <Text style={styles.notificationMessage}>{message}</Text>
      <Text style={styles.notificationTime}>{time}</Text>
    </View>
  </TouchableOpacity>
);

const getNotificationIcon = (type: NotificationType['type']) => {
  switch (type) {
    case 'account':
      return 'account';
    case 'program':
      return 'lightning-bolt';
    case 'points':
      return 'star';
    case 'reminder':
      return 'bell';
    default:
      return 'bell';
  }
};

const NotificationsComponent = () => {
  const router = useRouter();
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const [loading, setLoading] = useState(true);
  const [userEmail, setUserEmail] = useState<string>("");

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const email = await getId();
        if (!email) return;

        setUserEmail(email);
        const result = await firestoreService.notifications.getAllNotifications(email);

        if (result.success) {
          setNotifications(result.data || []);
        } else {
          console.error('Error fetching notifications:', result.error);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  const toggleNotificationRead = async (notificationId: string) => {
    try {
      const notification = notifications.find(n => n.id === notificationId);
      if (!notification || !userEmail) return;

      const result = notification.read
        ? await firestoreService.notifications.markAsUnread(userEmail, notificationId)
        : await firestoreService.notifications.markAsRead(userEmail, notificationId);

      if (result.success) {
        // Update local state
        setNotifications(notifications.map(n =>
          n.id === notificationId ? { ...n, read: !n.read } : n
        ));
      } else {
        console.error('Error updating notification:', result.error);
      }
    } catch (error) {
      console.error('Error updating notification:', error);
    }
  };

  const renderNotificationItem = ({ item }: { item: NotificationType }) => (
    <NotificationItem
      title={item.title}
      message={item.message}
      time={formatTimeAgo(item.time)}
      type={item.type}
      priority={item.priority}
      read={item.read}
      onPress={() => item.id && toggleNotificationRead(item.id)}
    />
  );

  const markAllAsRead = async () => {
    try {
      if (!userEmail) return;

      const unreadNotifications = notifications.filter(n => !n.read);

      // If no unread notifications, don't proceed
      if (unreadNotifications.length === 0) return;

      const result = await firestoreService.notifications.markAllAsRead(userEmail);

      if (result.success) {
        // Update local state
        setNotifications(notifications.map(n => ({ ...n, read: true })));
      } else {
        console.error('Error marking all notifications as read:', result.error);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <MaterialCommunityIcons name="arrow-left" size={24} color="#FFCC00" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        {notifications.some(n => !n.read) && (
          <TouchableOpacity 
            style={styles.readAllButton} 
            onPress={markAllAsRead}
          >
            <Text style={styles.readAllText}>Mark all read</Text>
          </TouchableOpacity>
        )}
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FFCC00" />
        </View>
      ) : notifications.length > 0 ? (
        <FlatList
          data={notifications}
          renderItem={renderNotificationItem}
          keyExtractor={(item, index) => item.id || index.toString()}
          style={styles.container}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={10}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No notifications yet</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#121212",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingTop: 60,
    backgroundColor: "#1A1A1A",
  },
  headerTitle: {
    flex: 1, // This will make the title take up available space
    fontSize: 20,
    color: "#FFCC00",
    fontWeight: "bold",
    marginLeft: 16,
    fontFamily: "MontserratBold",
  },
  backButton: {
    padding: 8,
  },
  readAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: "#2A2A2A",
  },
  readAllText: {
    color: "#FFCC00",
    fontSize: 14,
    fontFamily: "MontserratRegular",
  },
  container: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    color: "#888888",
    fontSize: 16,
    fontFamily: "MontserratRegular",
  },
  notificationItem: {
    flexDirection: "row",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#2A2A2A",
    backgroundColor: "#1A1A1A",
    position: 'relative', // Ensures proper positioning of absolute elements
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2A2A2A",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
    paddingRight: 24, // Add padding to prevent text from going under the dot
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 4,
    fontFamily: "MontserratBold",
  },
  notificationMessage: {
    fontSize: 14,
    color: "#CCCCCC",
    marginBottom: 4,
    fontFamily: "MontserratRegular",
  },
  notificationTime: {
    fontSize: 12,
    color: "#888888",
    fontFamily: "MontserratRegular",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadDot: {
    position: 'absolute',
    right: 16, // Increased from 8 to 16 to move it more to the right
    top: 16,   // Increased from 8 to 16 to align with the top padding
    zIndex: 1, // Ensures the dot appears above other elements
  },
});

const Notifications = () => {
  return (
    <ProtectedRoute>
      <NotificationsComponent />
    </ProtectedRoute>
  );
};

export default Notifications;






