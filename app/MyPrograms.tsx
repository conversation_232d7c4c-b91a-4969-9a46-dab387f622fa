import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SectionList,
} from "react-native";
import { useRouter } from "expo-router";
import { MaterialIcons, MaterialCommunityIcons } from "@expo/vector-icons";
import { firestoreService } from "../lib/services/database";
import { getId } from "../lib/utils/variables";
import { Commit } from "../lib/services/database/types";
import { useTheme } from "../shared/contexts/ThemeContext";
import { LazyScreen } from "../ui/common/LazyScreen";

import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';

import { Program as BaseProgram } from "../shared/types/CommonInterface";

// Extended Program interface with disqualified property
interface Program extends BaseProgram {
  disqualified?: boolean;
}

// Section data interfaces for SectionList
interface ProgramSection {
  title: string;
  data: Program[];
  type: 'programs';
}

interface CommitSection {
  title: string;
  data: Commit[];
  type: 'commits';
}

type SectionData = ProgramSection | CommitSection;

function MyProgramsComponent() {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [commits, setCommits] = useState<Commit[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  const fetchPrograms = useCallback(async () => {
    // Add delay to allow smooth navigation transition
    await new Promise(resolve => setTimeout(resolve, 150));

    setLoading(true);
    try {
      const userId = await getId();
      if (!userId) return;

      // Fetch both programs and commits
      const [userProgramsResult, userCommitsResult] = await Promise.all([
        firestoreService.users.getUserPrograms(userId),
        firestoreService.commits.getCommitsByUser(userId)
      ]);

      if (!userProgramsResult.success) {
        console.error("Error fetching user programs:", userProgramsResult.error);
        return;
      }

      // Set commits data
      if (userCommitsResult.success && userCommitsResult.data) {
        setCommits(userCommitsResult.data);
      } else {
        console.error("Error fetching user commits:", userCommitsResult.error);
        setCommits([]);
      }

      const programsPromises = (userProgramsResult.data || [])
        .filter(userProgram => userProgram && userProgram.programId) // Filter out invalid entries
        .map(async (userProgram) => {
          const programId = userProgram.programId;

          try {
            // Get program details
            const programResult = await firestoreService.programs.getProgramById(programId);
            if (!programResult.success || !programResult.data) return null;

            // Get participant data
            const participantResult = await firestoreService.participants.getParticipant(programId, userId);
            const participantData = participantResult.success ? participantResult.data : null;

            // Map database status to CommonInterface status
            const mapStatus = (dbStatus: string): "upcoming" | "ongoing" | "ended" | "disqualified" => {
              switch (dbStatus) {
                case 'active':
                  return 'ongoing';
                case 'upcoming':
                  return 'upcoming';
                case 'ended':
                  return 'ended';
                default:
                  return 'upcoming'; // fallback
              }
            };

            return {
              id: programId,
              name: programResult.data.name || "Unnamed Program",
              description: programResult.data.description || "",
              category: programResult.data.category || "default",
              status: mapStatus(programResult.data.status || 'upcoming'),
              startDate: programResult.data.startDate,
              endDate: programResult.data.endDate,
              setupStatus: participantData?.setupStatus || false,
              disqualified: participantData?.disqualified || false,
            };
          } catch (error) {
            console.error(`Error processing program ${programId}:`, error);
            return null;
          }
        });

      const fetchedPrograms = (await Promise.all(programsPromises)).filter(
        (program): program is NonNullable<typeof program> => program !== null && program !== undefined
      );

      setPrograms(fetchedPrograms);
    } catch (error) {
      console.error("Error fetching programs:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // Use requestAnimationFrame to ensure this runs after the transition starts
    const frame = requestAnimationFrame(() => {
      fetchPrograms();
    });

    return () => cancelAnimationFrame(frame);
  }, [fetchPrograms]);

  const getProgramStatusText = (program: Program) => {
    if (program.disqualified) return "Disqualified";
    if (program.status === "upcoming" && !program.setupStatus) return "Setup Required";
    if (program.status === "upcoming") return "Upcoming";
    if (program.status === "ongoing") return "Active";
    if (program.status === "ended") return "Completed";
    return program.status;
  };

  const getProgramStatusColor = (program: Program) => {
    if (program.disqualified) return "#FF5252"; // Red for disqualified
    if (program.status === "upcoming" && !program.setupStatus) return "#FFA000"; // Orange for setup required
    if (program.status === "upcoming") return "#2196F3"; // Blue for upcoming
    if (program.status === "ongoing") return "#4CAF50"; // Green for ongoing/active
    if (program.status === "ended") return "#4CAF50"; // Green for completed programs
    return "#FFEB3B"; // Yellow fallback
  };

  const renderCommitItem = ({ item }: { item: Commit }) => (
    <View style={styles.programCard}>
      {/* Commit Header with Icon, Title and Status */}
      <View style={styles.programHeader}>
        <View style={styles.categoryIconContainer}>
          {getCommitIcon(item.evidence?.type || 'photo')}
        </View>
        <View style={styles.programInfo}>
          <Text style={styles.programName} numberOfLines={1} ellipsizeMode="tail">
            {item.title || 'Untitled Commit'}
          </Text>
          <Text style={styles.programDates}>
            {item.schedule?.frequency ? `${item.schedule.frequency.charAt(0).toUpperCase() + item.schedule.frequency.slice(1)} commitment` : 'Personal commitment'}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getCommitStatusColor(item) }
          ]}
        >
          <Text style={styles.statusText}>{getCommitStatusText(item)}</Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => {
            router.push(`/CommitDetails?commitId=${item.id}`);
          }}
        >
          <MaterialIcons name="visibility" size={16} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>View Details</Text>
        </TouchableOpacity>

        {item.status === 'active' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.progressButton]}
            onPress={() => {
              router.push(`/progress?selectedCommitId=${item.id}`);
            }}
          >
            <MaterialIcons name="play-arrow" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Open</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderProgramItem = ({ item }: { item: Program }) => (
    <View style={styles.programCard}>
      {/* Program Header with Icon, Name, Dates and Status */}
      <View style={styles.programHeader}>
        <View style={styles.categoryIconContainer}>
          {getCategoryIcon(item.category || "default")}
        </View>
        <View style={styles.programInfo}>
          <Text style={styles.programName} numberOfLines={1} ellipsizeMode="tail">
            {item.name}
          </Text>
          <Text style={styles.programDates}>
            {item.startDate ? new Date(item.startDate).toLocaleDateString() : 'TBD'} - {item.endDate ? new Date(item.endDate).toLocaleDateString() : 'TBD'}
          </Text>
        </View>
        <View 
          style={[
            styles.statusBadge, 
            { backgroundColor: getProgramStatusColor(item) }
          ]}
        >
          <Text style={styles.statusText}>{getProgramStatusText(item)}</Text>
        </View>
      </View>
      
      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => {
            router.push(`/ProgramDetails?programId=${item.id}`);
          }}
        >
          <MaterialIcons name="visibility" size={16} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>View Details</Text>
        </TouchableOpacity>

        {(item.status === "ongoing" || (item.status === "upcoming" && !item.setupStatus)) && (
          <TouchableOpacity
            style={[styles.actionButton, styles.progressButton]}
            onPress={() => {
              router.push(`/progress?selectedProgramId=${item.id}`);
            }}
          >
            <MaterialIcons name="play-arrow" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Open</Text>
          </TouchableOpacity>
        )}

        {(item.status === "ended") && (
          <TouchableOpacity
            style={[styles.actionButton, styles.downloadButton]}
            onPress={() => handleDownloadReport(item)}
          >
            <MaterialIcons name="download" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Download Report</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const handleDownloadReport = async (program: Program) => {
    try {
      const userId = await getId();
      if (!userId) return;

      // Fetch comprehensive program data
      const [programResult, participantsResult] = await Promise.all([
        firestoreService.programs.getProgramById(program.id),
        firestoreService.participants.getAllParticipants(program.id)
      ]);

      if (!programResult.success || !programResult.data) {
        Alert.alert("Error", "Failed to fetch program data");
        return;
      }

      const programData = programResult.data;
      const rawParticipants = participantsResult.success ? participantsResult.data || [] : [];

      // Fetch submissions for each participant
      const participants = await Promise.all(
        rawParticipants.map(async (participant) => {
          const submissionsResult = await firestoreService.submissions.getAllSubmissions(
            program.id,
            participant.userId
          );
          const submissions = submissionsResult.success ? submissionsResult.data || [] : [];

          // Convert submissions array to record format (using submission ID as key)
          const submissionRecord: Record<string, { status: string }> = {};
          submissions.forEach((sub) => {
            if (sub.id) {
              submissionRecord[sub.id] = { status: sub.status || 'not_submitted' };
            }
          });

          return {
            id: participant.userId,
            fname: participant.fname || 'Anonymous',
            disqualified: participant.disqualified ?? false,
            livesLeft: participant.livesLeft ?? 0,
            livesPurchaseLeft: participant.livesPurchaseLeft ?? 0,
            submissions: submissionRecord,
          };
        })
      );

      // Calculate comprehensive statistics (same logic as ProgramEnd component)
      const duration = programData.duration || 0;
      const betAmount = programData.betAmount || 0;
      const totalPlayers = participants.length;

      // Submission stats
      const totalSubmissions = participants.reduce((acc, p) => {
        const good = Object.values(p.submissions).filter((s) =>
          ['submitted', 'verified'].includes(s.status.toLowerCase())
        ).length;
        return acc + good;
      }, 0);
      const possible = participants.length * duration;
      const completionRate = possible > 0
        ? Math.round((totalSubmissions / possible) * 10000) / 100
        : 0;

      // Disqualifications
      const disqCount = participants.filter((p) => p.disqualified).length;
      const loserPool = disqCount * betAmount;

      // Money stats
      const totalPool = betAmount * totalPlayers;
      const distributable = loserPool * 0.88;

      // Winners & share calculation
      const rawWinners = participants.filter((p) => !p.disqualified);
      let totalBailouts = 0;
      rawWinners.forEach((p) => {
        const used = 3 - p.livesLeft + (3 - p.livesPurchaseLeft);
        totalBailouts += used;
      });
      const baseShare = rawWinners.length > 0 ? 100 / rawWinners.length : 0;

      const beforeNorm = rawWinners.map((p) => {
        const used = 3 - p.livesLeft + (3 - p.livesPurchaseLeft);
        const penaltyPct = totalBailouts > 0 ? (used / totalBailouts) * 50 : 0;
        return {
          id: p.id,
          name: p.fname,
          bailoutsUsed: used,
          shareRaw: baseShare * (1 - penaltyPct / 100),
        };
      });

      const sumRaw = beforeNorm.reduce((acc, w) => acc + w.shareRaw, 0);
      const winners = beforeNorm
        .map((w) => {
          const sharePct = sumRaw > 0 ? (w.shareRaw / sumRaw) * 100 : 0;
          return {
            id: w.id,
            name: w.name,
            bailoutsUsed: w.bailoutsUsed,
            sharePercentage: sharePct,
            winnings: (sharePct / 100) * distributable,
          };
        })
        .sort((a, b) => b.winnings - a.winnings);

      // Current user's prize & rank
      const currentUser = winners.find((w) => w.id === userId);
      const userWinnings = currentUser?.winnings ?? 0;
      const userRank = currentUser
        ? winners.findIndex((w) => w.id === userId) + 1
        : undefined;

      // Generate comprehensive HTML report
      const generateHTML = () => {
        const topWinners = winners.slice(0, 10); // Show top 10 winners
        const winnersTable = topWinners.map((winner, index) => `
          <tr style="background: ${index % 2 === 0 ? '#f9f9f9' : '#fff'};">
            <td style="padding: 8px; border-bottom: 1px solid #eee;">${index + 1}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">${winner.name}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">${winner.bailoutsUsed}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">${winner.sharePercentage.toFixed(2)}%</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">$${winner.winnings.toFixed(2)}</td>
          </tr>
        `).join('');

        return `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  padding: 20px;
                  color: #333;
                  max-width: 900px;
                  margin: 0 auto;
                  line-height: 1.6;
                }
                h1 {
                  color: #000;
                  font-size: 28px;
                  margin-bottom: 30px;
                  text-align: center;
                  border-bottom: 3px solid #FFEB3B;
                  padding-bottom: 15px;
                }
                .section {
                  margin: 25px 0;
                  padding: 20px;
                  background: #f8f8f8;
                  border-radius: 12px;
                  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                  border-left: 4px solid #FFEB3B;
                }
                .section-title {
                  font-size: 20px;
                  font-weight: bold;
                  margin-bottom: 15px;
                  color: #2A2A2A;
                }
                .data-row {
                  display: flex;
                  justify-content: space-between;
                  padding: 10px 0;
                  border-bottom: 1px solid #ddd;
                }
                .data-row:last-child {
                  border-bottom: none;
                }
                .label {
                  font-weight: 600;
                  color: #333;
                }
                .value {
                  color: #666;
                  font-weight: 500;
                }
                .highlight {
                  background: #fff3cd;
                  padding: 15px;
                  border-radius: 8px;
                  margin: 15px 0;
                  border-left: 4px solid #ffc107;
                }
                table {
                  width: 100%;
                  border-collapse: collapse;
                  margin-top: 15px;
                }
                th {
                  background: #2A2A2A;
                  color: white;
                  padding: 12px 8px;
                  text-align: left;
                  font-weight: bold;
                }
                td {
                  padding: 10px 8px;
                  border-bottom: 1px solid #eee;
                }
                .user-highlight {
                  background: #e8f5e8 !important;
                  font-weight: bold;
                }
                @media print {
                  body { padding: 0; }
                  .section { break-inside: avoid; }
                }
              </style>
            </head>
            <body>
              <h1>Program Report - ${programData.name || 'N/A'}</h1>

              <div class="section">
                <div class="section-title">📋 Program Details</div>
                <div class="data-row">
                  <span class="label">Duration:</span>
                  <span class="value">${duration} days</span>
                </div>
                <div class="data-row">
                  <span class="label">Category:</span>
                  <span class="value">${programData.category || 'N/A'}</span>
                </div>
                <div class="data-row">
                  <span class="label">Start Date:</span>
                  <span class="value">${programData.startDate ? new Date(programData.startDate).toLocaleDateString() : 'N/A'}</span>
                </div>
                <div class="data-row">
                  <span class="label">End Date:</span>
                  <span class="value">${programData.endDate ? new Date(programData.endDate).toLocaleDateString() : 'N/A'}</span>
                </div>
                <div class="data-row">
                  <span class="label">Bet Amount:</span>
                  <span class="value">$${betAmount.toFixed(2)}</span>
                </div>
                <div class="data-row">
                  <span class="label">Total Participants:</span>
                  <span class="value">${totalPlayers}</span>
                </div>
              </div>

              <div class="section">
                <div class="section-title">📊 Performance Statistics</div>
                <div class="data-row">
                  <span class="label">Total Submissions:</span>
                  <span class="value">${totalSubmissions}</span>
                </div>
                <div class="data-row">
                  <span class="label">Possible Submissions:</span>
                  <span class="value">${possible}</span>
                </div>
                <div class="data-row">
                  <span class="label">Overall Completion Rate:</span>
                  <span class="value">${completionRate}%</span>
                </div>
                <div class="data-row">
                  <span class="label">Successful Participants:</span>
                  <span class="value">${rawWinners.length}</span>
                </div>
                <div class="data-row">
                  <span class="label">Disqualified Participants:</span>
                  <span class="value">${disqCount}</span>
                </div>
              </div>

              <div class="section">
                <div class="section-title">💰 Financial Summary</div>
                <div class="data-row">
                  <span class="label">Total Pool:</span>
                  <span class="value">$${totalPool.toFixed(2)}</span>
                </div>
                <div class="data-row">
                  <span class="label">Loser Pool (from disqualified):</span>
                  <span class="value">$${loserPool.toFixed(2)}</span>
                </div>
                <div class="data-row">
                  <span class="label">Distributable Amount (88% of loser pool):</span>
                  <span class="value">$${distributable.toFixed(2)}</span>
                </div>
                <div class="data-row">
                  <span class="label">Platform Fee (12% of loser pool):</span>
                  <span class="value">$${(loserPool * 0.12).toFixed(2)}</span>
                </div>
              </div>

              ${userRank ? `
              <div class="highlight">
                <div class="section-title">🎉 Your Performance</div>
                <div class="data-row">
                  <span class="label">Your Rank:</span>
                  <span class="value">#${userRank} out of ${rawWinners.length} winners</span>
                </div>
                <div class="data-row">
                  <span class="label">Your Winnings:</span>
                  <span class="value">$${userWinnings.toFixed(2)}</span>
                </div>
                <div class="data-row">
                  <span class="label">Your Share:</span>
                  <span class="value">${currentUser?.sharePercentage.toFixed(2)}%</span>
                </div>
                <div class="data-row">
                  <span class="label">Bailouts Used:</span>
                  <span class="value">${currentUser?.bailoutsUsed || 0}</span>
                </div>
              </div>
              ` : ''}

              <div class="section">
                <div class="section-title">🏆 Winner Leaderboard (Top 10)</div>
                <table>
                  <thead>
                    <tr>
                      <th>Rank</th>
                      <th>Name</th>
                      <th>Bailouts Used</th>
                      <th>Share %</th>
                      <th>Winnings</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${winnersTable}
                  </tbody>
                </table>
              </div>

              <div style="margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 8px; text-align: center; font-size: 12px; color: #666;">
                Report generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
              </div>
            </body>
          </html>
        `;
      };

      // Generate PDF file
      const { uri } = await Print.printToFileAsync({
        html: generateHTML(),
        base64: false,
        width: 612,
        height: 792,
        margins: {
          left: 36,
          top: 36,
          right: 36,
          bottom: 36
        }
      });

      // Check if sharing is available
      const canShare = await Sharing.isAvailableAsync();

      if (canShare) {
        await Sharing.shareAsync(uri, {
          UTI: '.pdf',
          mimeType: 'application/pdf',
          dialogTitle: 'Download Program Report'
        });
      } else {
        Alert.alert("Error", "Sharing is not available on this device");
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert("Error", "Failed to generate PDF report. Please try again.");
    }
  };

  const getCommitStatusText = (commit: Commit) => {
    switch (commit.status) {
      case 'active':
        return 'Active';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return commit.status;
    }
  };

  const getCommitStatusColor = (commit: Commit) => {
    switch (commit.status) {
      case 'active':
        return '#4CAF50'; // Green for active
      case 'completed':
        return '#2196F3'; // Blue for completed
      case 'failed':
        return '#FF5252'; // Red for failed
      case 'cancelled':
        return '#9E9E9E'; // Gray for cancelled
      default:
        return '#FFEB3B'; // Yellow fallback
    }
  };

  const getCommitIcon = (evidenceType: string) => {
    switch (evidenceType) {
      case 'photo':
        return <MaterialIcons name="camera-alt" size={24} color="#FFEB3B" />;
      case 'video':
        return <MaterialIcons name="videocam" size={24} color="#FFEB3B" />;
      case 'github':
        return <MaterialCommunityIcons name="github" size={24} color="#FFEB3B" />;
      case 'strava':
        return <MaterialCommunityIcons name="run" size={24} color="#FFEB3B" />;
      case 'screen_time':
        return <MaterialIcons name="screen-lock-portrait" size={24} color="#FFEB3B" />;
      default:
        return <MaterialCommunityIcons name="check-circle" size={24} color="#FFEB3B" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "cardio":
        return <MaterialIcons name="directions-run" size={24} color="#FFEB3B" />;
      case "coding":
        return <MaterialIcons name="code" size={24} color="#FFEB3B" />;
      case "journals":
        return <MaterialIcons name="book" size={24} color="#FFEB3B" />;
      case "affirmations":
        return <MaterialIcons name="record-voice-over" size={24} color="#FFEB3B" />;
      case "writing":
        return <MaterialIcons name="edit" size={24} color="#FFEB3B" />;
      default:
        return <MaterialCommunityIcons name="tag" size={24} color="#FFEB3B" />;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.titleContainer}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <MaterialIcons name="arrow-back" size={24} color="#FFEB3B" />
          </TouchableOpacity>
          <Text style={styles.title}>My Enrolment</Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FFEB3B" />
          </View>
        ) : programs.length > 0 || commits.length > 0 ? (
          <SectionList<Program | Commit, SectionData>
            sections={[
              ...(programs.length > 0 ? [{
                title: 'Programs',
                data: programs,
                type: 'programs' as const
              }] : []),
              ...(commits.length > 0 ? [{
                title: 'Commits',
                data: commits,
                type: 'commits' as const
              }] : [])
            ]}
            renderItem={({ item, section }) => {
              if (section.type === 'programs') {
                return renderProgramItem({ item: item as Program });
              } else {
                return renderCommitItem({ item: item as Commit });
              }
            }}
            renderSectionHeader={({ section }) => (
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionHeaderText}>{section.title}</Text>
              </View>
            )}
            keyExtractor={(item, index) => `${item.id}-${index}`}
            contentContainerStyle={styles.listContainer}
            stickySectionHeadersEnabled={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>You haven't joined any programs or created any commits yet.</Text>
            <View style={styles.emptyButtonsContainer}>
              <TouchableOpacity
                style={styles.exploreButton}
                onPress={() => router.replace("/(tabs)")}
              >
                <Text style={styles.exploreButtonText}>Pool Challenges</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.exploreButton, styles.commitsButton]}
                onPress={() => router.push("/(tabs)/commits")}
              >
                <Text style={styles.exploreButtonText}>Create Commits</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
    paddingTop: 50, // Reduced top padding
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
    position: "relative",
  },
  title: {
    fontSize: 24,
    fontFamily: "MontserratBold",
    color: colors.primary,
  },
  backButton: {
    padding: 8,
    position: "absolute",
    left: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  listContainer: {
    paddingBottom: 20,
  },
  programCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
  },
  programHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2A2A2A",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  programInfo: {
    flex: 1,
  },
  programName: {
    fontSize: 16,
    fontFamily: "MontserratBold",
    color: "#FFF",
    marginBottom: 4,
  },
  programDates: {
    fontSize: 12,
    fontFamily: "MontserratRegular",
    color: "#AAA",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 10,
    fontFamily: "MontserratBold",
    color: "#000",
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#333',
    gap: 8,
  },
  actionButton: {
    backgroundColor: '#2A2A2A',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    flex: 1,
  },
  downloadButton: {
    backgroundColor: '#4CAF50',
  },
  progressButton: {
    backgroundColor: '#2196F3',
  },
  actionButtonText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: '#FFFFFF',
  },
  sectionHeader: {
    backgroundColor: "#1A1A1A",
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 8,
  },
  sectionHeaderText: {
    fontSize: 18,
    fontFamily: "MontserratBold",
    color: "#FFEB3B",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: "MontserratRegular",
    color: "#FFF",
    textAlign: "center",
    marginBottom: 30,
  },
  emptyButtonsContainer: {
    flexDirection: "row",
    gap: 12,
  },
  exploreButton: {
    backgroundColor: "#FFEB3B",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  commitsButton: {
    backgroundColor: "#2196F3",
  },
  exploreButtonText: {
    fontSize: 14,
    fontFamily: "MontserratBold",
    color: "#000",
  },
});

export default function MyPrograms() {
  return (
    <LazyScreen delay={100}>
      <MyProgramsComponent />
    </LazyScreen>
  );
}
