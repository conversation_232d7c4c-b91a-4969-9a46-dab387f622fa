# Accustom

Accustom is a habit‑based betting platform built with React Native and Expo. Users join or create programs where they stake an amount on completing daily habits. The app tracks progress, awards points, and lets participants challenge disputed results. It integrates with Firebase for authentication, data storage and push notifications.

## Major features

- Explore and join betting programs
- Track progress with streaks, points and leaderboards
- View personal programs and program details
- In‑app and push notifications
- Dispute management and support pages
- User profile and email settings

## Development

1. Install dependencies:
   ```bash
   npm install
   ```
2. Start the development server:
   ```bash
   npx expo start
   ```
   This launches the Expo CLI where you can run the app on Android, iOS or the web.

### Tests and linting

Run `npm test` to execute Jest tests. You can also run `npm run lint` to check code style.

## Firebase configuration

The app uses Firebase for authentication, Firestore, and storage. To set up Firebase:

1. Copy the example environment file:
   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your Firebase credentials:
   ```bash
   EXPO_PUBLIC_FIREBASE_API_KEY=your_api_key
   EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   EXPO_PUBLIC_FIREBASE_DATABASE_URL=https://your_project-default-rtdb.region.firebasedatabase.app
   EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id
   EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
   ```

3. The Firebase configuration in `lib/config/firebase.ts` will automatically read these environment variables.

**Important:** Never commit `.env.local` to version control. It's already included in `.gitignore`.
