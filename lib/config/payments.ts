// Enhanced Payment configuration for dual payment streams
// Supports Stripe for commits/pools and platform-specific payments for premium/grace days

// Define payment types and providers locally to avoid circular imports
export enum PaymentType {
  COMMIT = 'commit',
  POOL = 'pool',
  PREMIUM_SUBSCRIPTION = 'premium_subscription',
  GRACE_DAY = 'grace_day'
}

export enum PaymentProvider {
  STRIPE = 'stripe',
  GOOGLE_PLAY = 'google_play',
  APPLE_APP_STORE = 'apple_app_store'
}

// In-App Purchase Product IDs
export const IN_APP_PRODUCTS = {
  PREMIUM_MONTHLY: 'premium_monthly_subscription',
  PREMIUM_YEARLY: 'premium_yearly_subscription',
  GRACE_DAY_SINGLE: 'grace_day_single',
  GRACE_DAY_PACK_3: 'grace_day_pack_3',
  GRACE_DAY_PACK_5: 'grace_day_pack_5',
} as const;

export const STRIPE_CONFIG = {
  // Publishable key (safe to expose in client-side code)
  PUBLISHABLE_KEY: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_51PT0BiEaVxDXnS46eQNplkbLNVbpT6oEBQR3sEtVJk5djbYo6H9ejkbfBiPqTNgCdzD85DpyxArml1edaErA53Mo00efIG4BsP',

  // Merchant identifier for Apple Pay (iOS only)
  MERCHANT_IDENTIFIER: 'merchant.com.accustom',

  // Test environment flag
  TEST_ENV: __DEV__, // Use test environment in development
};

// In-App Purchase Configuration
export const IN_APP_PURCHASE_CONFIG = {
  // Google Play Console configuration
  GOOGLE_PLAY: {
    // Your Google Play Console app package name
    PACKAGE_NAME: process.env.EXPO_PUBLIC_ANDROID_PACKAGE || 'com.accustom.app',

    // Google Play Console service account key (for server-side verification)
    SERVICE_ACCOUNT_KEY: process.env.GOOGLE_PLAY_SERVICE_ACCOUNT_KEY,

    // Test environment flag
    TEST_ENV: __DEV__,
  },

  // Apple App Store Connect configuration
  APPLE_APP_STORE: {
    // Your App Store Connect app bundle ID
    BUNDLE_ID: process.env.EXPO_PUBLIC_IOS_BUNDLE_ID || 'com.accustom.app',

    // App Store Connect shared secret (for server-side verification)
    SHARED_SECRET: process.env.APPLE_APP_STORE_SHARED_SECRET,

    // Test environment flag (sandbox)
    TEST_ENV: __DEV__,
  },

  // Product configuration
  PRODUCTS: {
    // Premium subscription products
    PREMIUM_MONTHLY: {
      id: IN_APP_PRODUCTS.PREMIUM_MONTHLY,
      type: 'subscription' as const,
      price: 15.00,
      currency: 'USD',
      title: 'Accustom Premium Monthly',
      description: 'Monthly premium subscription with all features',
      platform: 'both' as const,
    },
    PREMIUM_YEARLY: {
      id: IN_APP_PRODUCTS.PREMIUM_YEARLY,
      type: 'subscription' as const,
      price: 150.00,
      currency: 'USD',
      title: 'Accustom Premium Yearly',
      description: 'Yearly premium subscription with all features (save $30)',
      platform: 'both' as const,
    },

    // Grace day products
    GRACE_DAY_SINGLE: {
      id: IN_APP_PRODUCTS.GRACE_DAY_SINGLE,
      type: 'consumable' as const,
      price: 2.00,
      currency: 'USD',
      title: '1 Grace Day',
      description: 'Single grace day for flexibility',
      platform: 'both' as const,
    },
    GRACE_DAY_PACK_3: {
      id: IN_APP_PRODUCTS.GRACE_DAY_PACK_3,
      type: 'consumable' as const,
      price: 5.00,
      currency: 'USD',
      title: '3 Grace Days Pack',
      description: 'Pack of 3 grace days (save $1)',
      platform: 'both' as const,
    },
    GRACE_DAY_PACK_5: {
      id: IN_APP_PRODUCTS.GRACE_DAY_PACK_5,
      type: 'consumable' as const,
      price: 8.00,
      currency: 'USD',
      title: '5 Grace Days Pack',
      description: 'Pack of 5 grace days (save $2)',
      platform: 'both' as const,
    },
  },
};

// Backend API configuration
export const API_CONFIG = {
  // Replace with your actual backend API URL
  BASE_URL: process.env.EXPO_PUBLIC_API_URL || 'https://your-backend-api.com',
  
  // API endpoints
  ENDPOINTS: {
    CREATE_PAYMENT_INTENT: '/create-payment-intent',
    CONFIRM_PAYMENT: '/confirm-payment',
    WEBHOOK: '/stripe-webhook',
  },
};

// Enhanced payment method configuration for dual payment streams
export const PAYMENT_METHODS = {
  // Stripe configuration (for commits and pools)
  STRIPE: {
    enabled: true,
    name: 'Credit/Debit Card',
    icon: 'credit-card',
    platforms: ['ios', 'android', 'web'],
    supportedPaymentTypes: [PaymentType.COMMIT, PaymentType.POOL],
    provider: PaymentProvider.STRIPE,
  },

  // Apple Pay configuration (legacy Stripe-based, for commits and pools)
  APPLE_PAY_STRIPE: {
    enabled: true,
    name: 'Apple Pay (Stripe)',
    icon: 'apple',
    platforms: ['ios'],
    supportedPaymentTypes: [PaymentType.COMMIT, PaymentType.POOL],
    provider: PaymentProvider.STRIPE,
  },

  // Google Pay configuration (legacy Stripe-based, for commits and pools)
  GOOGLE_PAY_STRIPE: {
    enabled: true,
    name: 'Google Pay (Stripe)',
    icon: 'google',
    platforms: ['android'],
    supportedPaymentTypes: [PaymentType.COMMIT, PaymentType.POOL],
    provider: PaymentProvider.STRIPE,
  },

  // Apple App Store configuration (for premium subscriptions and grace days)
  APPLE_APP_STORE: {
    enabled: true,
    name: 'Apple App Store',
    icon: 'apple',
    platforms: ['ios'],
    supportedPaymentTypes: [PaymentType.PREMIUM_SUBSCRIPTION, PaymentType.GRACE_DAY],
    provider: PaymentProvider.APPLE_APP_STORE,
  },

  // Google Play Store configuration (for premium subscriptions and grace days)
  GOOGLE_PLAY_STORE: {
    enabled: true,
    name: 'Google Play Store',
    icon: 'google',
    platforms: ['android'],
    supportedPaymentTypes: [PaymentType.PREMIUM_SUBSCRIPTION, PaymentType.GRACE_DAY],
    provider: PaymentProvider.GOOGLE_PLAY,
  },
};

// Payment routing configuration
export const PAYMENT_ROUTING_CONFIG = {
  [PaymentType.COMMIT]: {
    primary: PaymentProvider.STRIPE,
    fallback: [],
    platforms: {
      ios: [PaymentProvider.STRIPE],
      android: [PaymentProvider.STRIPE],
      web: [PaymentProvider.STRIPE],
    },
  },
  [PaymentType.POOL]: {
    primary: PaymentProvider.STRIPE,
    fallback: [],
    platforms: {
      ios: [PaymentProvider.STRIPE],
      android: [PaymentProvider.STRIPE],
      web: [PaymentProvider.STRIPE],
    },
  },
  [PaymentType.PREMIUM_SUBSCRIPTION]: {
    primary: null, // Determined by platform
    fallback: [PaymentProvider.STRIPE],
    platforms: {
      ios: [PaymentProvider.APPLE_APP_STORE, PaymentProvider.STRIPE],
      android: [PaymentProvider.GOOGLE_PLAY, PaymentProvider.STRIPE],
      web: [PaymentProvider.STRIPE],
    },
  },
  [PaymentType.GRACE_DAY]: {
    primary: null, // Determined by platform
    fallback: [PaymentProvider.STRIPE],
    platforms: {
      ios: [PaymentProvider.APPLE_APP_STORE, PaymentProvider.STRIPE],
      android: [PaymentProvider.GOOGLE_PLAY, PaymentProvider.STRIPE],
      web: [PaymentProvider.STRIPE],
    },
  },
};

// Payment Method Configuration Interface
export interface PaymentMethodConfig {
  type: PaymentType;
  providers: PaymentProvider[];
  defaultProvider: PaymentProvider;
  fallbackProviders?: PaymentProvider[];
}

// Payment routing configuration (for payment router service)
export const PAYMENT_ROUTING: Record<PaymentType, PaymentMethodConfig> = {
  [PaymentType.COMMIT]: {
    type: PaymentType.COMMIT,
    providers: [PaymentProvider.STRIPE],
    defaultProvider: PaymentProvider.STRIPE,
  },
  [PaymentType.POOL]: {
    type: PaymentType.POOL,
    providers: [PaymentProvider.STRIPE],
    defaultProvider: PaymentProvider.STRIPE,
  },
  [PaymentType.PREMIUM_SUBSCRIPTION]: {
    type: PaymentType.PREMIUM_SUBSCRIPTION,
    providers: [PaymentProvider.GOOGLE_PLAY, PaymentProvider.APPLE_APP_STORE, PaymentProvider.STRIPE],
    defaultProvider: PaymentProvider.GOOGLE_PLAY, // Will be determined by platform
    fallbackProviders: [PaymentProvider.STRIPE],
  },
  [PaymentType.GRACE_DAY]: {
    type: PaymentType.GRACE_DAY,
    providers: [PaymentProvider.GOOGLE_PLAY, PaymentProvider.APPLE_APP_STORE, PaymentProvider.STRIPE],
    defaultProvider: PaymentProvider.GOOGLE_PLAY, // Will be determined by platform
    fallbackProviders: [PaymentProvider.STRIPE],
  },
};

// Currency configuration
export const CURRENCY_CONFIG = {
  DEFAULT: 'usd',
  SUPPORTED: ['usd', 'eur', 'gbp'],
  DISPLAY_SYMBOL: '$',
};

// Fee configuration
export const FEE_CONFIG = {
  // Platform fee percentage (e.g., 0.12 = 12%)
  PLATFORM_FEE_PERCENTAGE: 0.12,
  
  // Stripe processing fee (varies by region and payment method)
  STRIPE_FEE_PERCENTAGE: 0.029, // 2.9%
  STRIPE_FEE_FIXED: 30, // 30 cents in cents
};

// Validation helpers
export const validateStripeConfig = (): boolean => {
  const { PUBLISHABLE_KEY } = STRIPE_CONFIG;
  
  if (!PUBLISHABLE_KEY || PUBLISHABLE_KEY === 'pk_test_51234567890abcdef') {
    console.warn('⚠️ Stripe publishable key not configured properly');
    return false;
  }
  
  if (!PUBLISHABLE_KEY.startsWith('pk_')) {
    console.error('❌ Invalid Stripe publishable key format');
    return false;
  }
  
  return true;
};

export const validateAPIConfig = (): boolean => {
  const { BASE_URL } = API_CONFIG;

  if (!BASE_URL || BASE_URL === 'https://your-backend-api.com') {
    console.warn('⚠️ Backend API URL not configured properly');
    return false;
  }

  try {
    new URL(BASE_URL);
    return true;
  } catch {
    console.error('❌ Invalid backend API URL format');
    return false;
  }
};

// Validation for in-app purchase configuration
export const validateInAppPurchaseConfig = (): boolean => {
  const { GOOGLE_PLAY, APPLE_APP_STORE } = IN_APP_PURCHASE_CONFIG;

  // Validate Google Play configuration
  if (!GOOGLE_PLAY.PACKAGE_NAME || GOOGLE_PLAY.PACKAGE_NAME === 'com.accustom.app') {
    console.warn('⚠️ Google Play package name not configured properly');
  }

  // Validate Apple App Store configuration
  if (!APPLE_APP_STORE.BUNDLE_ID || APPLE_APP_STORE.BUNDLE_ID === 'com.accustom.app') {
    console.warn('⚠️ Apple App Store bundle ID not configured properly');
  }

  return true;
};

// Get available payment methods for a specific payment type and platform
export const getAvailablePaymentMethods = (
  paymentType: PaymentType,
  platform: 'ios' | 'android' | 'web' = 'web'
): PaymentProvider[] => {
  const config = PAYMENT_ROUTING_CONFIG[paymentType];
  return config.platforms[platform] || [];
};

// Get primary payment provider for a payment type and platform
export const getPrimaryPaymentProvider = (
  paymentType: PaymentType,
  platform: 'ios' | 'android' | 'web' = 'web'
): PaymentProvider => {
  const availableMethods = getAvailablePaymentMethods(paymentType, platform);
  return availableMethods[0] || PaymentProvider.STRIPE;
};

// Check if a payment method is supported for a payment type
export const isPaymentMethodSupported = (
  paymentType: PaymentType,
  provider: PaymentProvider,
  platform: 'ios' | 'android' | 'web' = 'web'
): boolean => {
  const availableMethods = getAvailablePaymentMethods(paymentType, platform);
  return availableMethods.includes(provider);
};

// Helper function to calculate total amount including fees
export const calculateTotalAmount = (baseAmount: number): {
  baseAmount: number;
  platformFee: number;
  processingFee: number;
  totalAmount: number;
} => {
  const platformFee = Math.round(baseAmount * FEE_CONFIG.PLATFORM_FEE_PERCENTAGE);
  const processingFee = Math.round(baseAmount * FEE_CONFIG.STRIPE_FEE_PERCENTAGE) + FEE_CONFIG.STRIPE_FEE_FIXED;
  const totalAmount = baseAmount + platformFee + processingFee;
  
  return {
    baseAmount,
    platformFee,
    processingFee,
    totalAmount,
  };
};

// Helper function to format currency
export const formatCurrency = (amountInCents: number, currency: string = CURRENCY_CONFIG.DEFAULT): string => {
  const amount = amountInCents / 100;
  
  switch (currency.toLowerCase()) {
    case 'usd':
      return `$${amount.toFixed(2)}`;
    case 'eur':
      return `€${amount.toFixed(2)}`;
    case 'gbp':
      return `£${amount.toFixed(2)}`;
    default:
      return `${amount.toFixed(2)} ${currency.toUpperCase()}`;
  }
};
