import { initializeApp } from "firebase/app";
import { Auth, initializeAuth } from "firebase/auth";
import ReactNativeAsyncStorage from '@react-native-async-storage/async-storage';
import { getFirestore } from "firebase/firestore";
import firebase from "firebase/compat/app";
import { getStorage } from "firebase/storage";

// Validate that all required environment variables are present
const requiredEnvVars = [
  'EXPO_PUBLIC_FIREBASE_API_KEY',
  'EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN',
  'EXPO_PUBLIC_FIREBASE_PROJECT_ID',
  'EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET',
  'EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
  'EXPO_PUBLIC_FIREBASE_APP_ID',
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Your firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.EXPO_PUBLIC_FIREBASE_DATABASE_URL,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase app
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with React Native persistence
// Using initializeAuth with AsyncStorage persistence to resolve the warning
let auth: Auth;

try {
  // Try to import getReactNativePersistence - it should be available in React Native environment
  const { getReactNativePersistence } = require('firebase/auth') as any;

  if (getReactNativePersistence) {
    auth = initializeAuth(app, {
      persistence: getReactNativePersistence(ReactNativeAsyncStorage)
    });
  } else {
    // Fallback to basic initializeAuth if getReactNativePersistence is not available
    auth = initializeAuth(app);
  }
} catch (error) {
  // Check if the error is due to already being initialized
  if (error && typeof error === 'object' && 'code' in error && error.code === 'auth/already-initialized') {
    // If already initialized, get the existing auth instance
    const { getAuth } = require('firebase/auth');
    auth = getAuth(app);
  } else {
    // For other errors, fallback to basic initializeAuth
    console.warn('Failed to initialize auth with persistence:', error);
    auth = initializeAuth(app);
  }
}

export { auth };
export const db = getFirestore(app);
export const storage = getStorage();

export { firebase };
