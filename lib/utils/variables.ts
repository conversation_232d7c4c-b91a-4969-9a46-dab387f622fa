import AsyncStorage from "@react-native-async-storage/async-storage";

async function getToken(): Promise<boolean> {
  try {
    const value = await AsyncStorage.getItem("authToken");
    return value === "true"; // Explicitly check if the value is "true"
  } catch {
    return false; // Default to `false` in case of an error
  }
}


async function updateToken(val: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem("authToken", JSON.stringify(val));
  } catch {
    // Silent error handling
  }
}

async function updateId(value: string): Promise<void> {
  try {
    await AsyncStorage.setItem("email", value);
  } catch {
    // Silent error handling
  }
}

const getId = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem("email");
  } catch {
    return null;
  }
};

async function updateFname(value: string): Promise<void> {
  try {
    await AsyncStorage.setItem("fname", value);
  } catch {
    // Silent error handling
  }
}

const getFname = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem("fname");
  } catch {
    return null;
  }
};

async function updateLname(value: string): Promise<void> {
  try {
    await AsyncStorage.setItem("lname", value);
  } catch {
    // Silent error handling
  }
}

const getLname = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem("lname");
  } catch {
    return null;
  }
};

async function updateIsLoggedIn(value: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem("isLoggedIn", JSON.stringify(value));
  } catch {
    // Silent error handling
  }
}

const getIsLoggedIn = async (): Promise<boolean> => {
  try {
    const temp = await AsyncStorage.getItem("isLoggedIn");
    return temp === "true";
  } catch {
    return false;
  }
};

const logout = async () => {
  try {
    await AsyncStorage.clear();
    await updateToken(false); // Reset the token to false
  } catch {
    // Silent error handling
  }
};

export {
  getToken,
  updateToken,
  getId,
  updateId,
  updateFname,
  getFname,
  updateLname,
  getLname,
  updateIsLoggedIn,
  getIsLoggedIn,
  logout,
};
