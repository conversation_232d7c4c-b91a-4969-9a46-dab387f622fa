import * as Calendar from 'expo-calendar';
import { CalendarEvent } from '../services/calendarService';
import { Commit } from '../services/database/types';
import { Program } from '../../shared/types/CommonInterface';

/**
 * Generate calendar event from commitment data
 */
export const createCommitmentCalendarEvent = (
  commit: Commit,
  options: {
    includeReminders?: boolean;
    reminderMinutesBefore?: number;
  } = {}
): CalendarEvent => {
  const { includeReminders = true, reminderMinutesBefore = 60 } = options;

  const startDate = new Date(commit.schedule.startDate);
  const endDate = commit.schedule.endDate ? new Date(commit.schedule.endDate) : null;

  // Calculate event duration based on frequency
  let eventStartDate = new Date(startDate);
  let eventEndDate = new Date(startDate);
  
  // Set default event duration to 1 hour
  eventEndDate.setHours(eventStartDate.getHours() + 1);

  // Adjust time based on deadline configuration
  if (commit.schedule.deadline) {
    const deadline = commit.schedule.deadline;
    
    if (deadline.type === 'before' && deadline.time) {
      const [hours, minutes] = deadline.time.split(':').map(Number);
      eventStartDate.setHours(hours - 1, minutes, 0, 0); // 1 hour before deadline
      eventEndDate.setHours(hours, minutes, 0, 0);
    } else if (deadline.type === 'after' && deadline.time) {
      const [hours, minutes] = deadline.time.split(':').map(Number);
      eventStartDate.setHours(hours, minutes, 0, 0);
      eventEndDate.setHours(hours + 1, minutes, 0, 0);
    } else if (deadline.type === 'between' && deadline.startTime && deadline.endTime) {
      const [startHours, startMinutes] = deadline.startTime.split(':').map(Number);
      const [endHours, endMinutes] = deadline.endTime.split(':').map(Number);
      eventStartDate.setHours(startHours, startMinutes, 0, 0);
      eventEndDate.setHours(endHours, endMinutes, 0, 0);
    } else {
      // Default to evening reminder (7 PM - 8 PM)
      eventStartDate.setHours(19, 0, 0, 0);
      eventEndDate.setHours(20, 0, 0, 0);
    }
  } else {
    // Default to evening reminder (7 PM - 8 PM)
    eventStartDate.setHours(19, 0, 0, 0);
    eventEndDate.setHours(20, 0, 0, 0);
  }

  // Generate title
  const title = `${commit.title} - Commitment Reminder`;

  // Generate description
  let description = `🎯 Commitment: ${commit.title}\n`;
  if (commit.description) {
    description += `📝 Description: ${commit.description}\n`;
  }
  description += `📅 Frequency: ${commit.schedule.frequency.charAt(0).toUpperCase() + commit.schedule.frequency.slice(1)}\n`;
  description += `📸 Evidence Type: ${commit.evidence?.type?.charAt(0).toUpperCase() + commit.evidence?.type?.slice(1) || 'Photo'}\n`;
  
  if (commit.schedule.deadline) {
    const deadline = commit.schedule.deadline;
    if (deadline.type === 'before' && deadline.time) {
      description += `⏰ Submit before: ${deadline.time}\n`;
    } else if (deadline.type === 'after' && deadline.time) {
      description += `⏰ Submit after: ${deadline.time}\n`;
    } else if (deadline.type === 'between' && deadline.startTime && deadline.endTime) {
      description += `⏰ Submit between: ${deadline.startTime} - ${deadline.endTime}\n`;
    } else {
      description += `⏰ Submit by: Midnight\n`;
    }
  }
  
  description += `\n💪 Stay committed to your goals with Accustom!`;

  // Set up alarms/reminders
  const alarms: Calendar.Alarm[] = [];
  if (includeReminders) {
    alarms.push({
      relativeOffset: -reminderMinutesBefore, // Minutes before event
      method: Calendar.AlarmMethod.ALERT,
    });
  }

  // Set up recurrence rule based on frequency
  let recurrenceRule: Calendar.RecurrenceRule | undefined;
  
  if (commit.schedule.frequency === 'daily') {
    recurrenceRule = {
      frequency: Calendar.Frequency.DAILY,
      interval: 1,
      endDate: endDate || undefined,
    };
  } else if (commit.schedule.frequency === 'weekly') {
    const timesPerWeek = commit.schedule.timesPerWeek || 1;
    if (timesPerWeek === 1) {
      recurrenceRule = {
        frequency: Calendar.Frequency.WEEKLY,
        interval: 1,
        endDate: endDate || undefined,
      };
    } else {
      // For multiple times per week, create daily events but let user manage frequency
      recurrenceRule = {
        frequency: Calendar.Frequency.DAILY,
        interval: 1,
        endDate: endDate || undefined,
      };
    }
  } else if (commit.schedule.frequency === 'monthly') {
    recurrenceRule = {
      frequency: Calendar.Frequency.MONTHLY,
      interval: 1,
      endDate: endDate || undefined,
    };
  }
  // For 'once' frequency, no recurrence rule needed

  return {
    title,
    startDate: eventStartDate,
    endDate: eventEndDate,
    notes: description,
    alarms,
    recurrenceRule,
  };
};

/**
 * Generate calendar event from program data
 */
export const createProgramCalendarEvent = (
  program: Program,
  options: {
    includeReminders?: boolean;
    reminderMinutesBefore?: number;
  } = {}
): CalendarEvent => {
  const { includeReminders = true, reminderMinutesBefore = 60 } = options;

  const startDate = new Date(program.startDate || new Date());
  const endDate = program.endDate ? new Date(program.endDate) : null;

  // Set event time to 7 PM - 8 PM by default
  let eventStartDate = new Date(startDate);
  let eventEndDate = new Date(startDate);
  eventStartDate.setHours(19, 0, 0, 0);
  eventEndDate.setHours(20, 0, 0, 0);

  // Generate title
  const title = `${program.name} - Program Reminder`;

  // Generate description
  let description = `🏆 Program: ${program.name}\n`;
  if (program.description) {
    description += `📝 Description: ${program.description}\n`;
  }
  description += `📅 Duration: ${program.duration} days\n`;
  if (program.category) {
    description += `🏷️ Category: ${program.category.charAt(0).toUpperCase() + program.category.slice(1)}\n`;
  }
  if (program.betAmount) {
    description += `💰 Stake: $${program.betAmount}\n`;
  }
  
  description += `\n🎯 Stay on track with your program goals!`;

  // Set up alarms/reminders
  const alarms: Calendar.Alarm[] = [];
  if (includeReminders) {
    alarms.push({
      relativeOffset: -reminderMinutesBefore, // Minutes before event
      method: Calendar.AlarmMethod.ALERT,
    });
  }

  // Set up daily recurrence for the program duration
  let recurrenceRule: Calendar.RecurrenceRule | undefined;
  if (endDate) {
    recurrenceRule = {
      frequency: Calendar.Frequency.DAILY,
      interval: 1,
      endDate: endDate,
    };
  }

  return {
    title,
    startDate: eventStartDate,
    endDate: eventEndDate,
    notes: description,
    alarms,
    recurrenceRule,
  };
};

/**
 * Format date for calendar event display
 */
export const formatCalendarEventDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

/**
 * Get reminder time options for calendar events
 */
export const getReminderTimeOptions = () => [
  { label: '15 minutes before', value: 15 },
  { label: '30 minutes before', value: 30 },
  { label: '1 hour before', value: 60 },
  { label: '2 hours before', value: 120 },
  { label: '1 day before', value: 1440 },
];

/**
 * Validate calendar event data
 */
export const validateCalendarEvent = (event: CalendarEvent): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!event.title || event.title.trim().length === 0) {
    errors.push('Event title is required');
  }

  if (!event.startDate || isNaN(event.startDate.getTime())) {
    errors.push('Valid start date is required');
  }

  if (!event.endDate || isNaN(event.endDate.getTime())) {
    errors.push('Valid end date is required');
  }

  if (event.startDate && event.endDate && event.startDate >= event.endDate) {
    errors.push('End date must be after start date');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
