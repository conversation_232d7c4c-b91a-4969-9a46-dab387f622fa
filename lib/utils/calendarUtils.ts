import {Program, CalendarDate, CalendarDayData, ProgramTimelineData, DayTile } from "../../shared/types/CommonInterface";
import { getUserTimezone } from './timezoneUtils';

/**
 * Utility functions for calendar-based progress system
 */

// Performance optimization: Cache for expensive calculations
const dateCache = new Map<string, Date>();
const programDateRangeCache = new Map<string, { startDate: Date; endDate: Date }>();
const calendarDateCache = new Map<string, CalendarDate>();

// Helper to get cached date object
const getCachedDate = (dateStr: string): Date => {
  if (!dateCache.has(dateStr)) {
    const [year, month, day] = dateStr.split('-').map(Number);
    const date = new Date(year, month - 1, day);
    date.setHours(0, 0, 0, 0);
    dateCache.set(dateStr, date);
  }
  return dateCache.get(dateStr)!;
};

// Helper to get cached program date range
const getCachedProgramDateRange = (program: Program): { startDate: Date; endDate: Date } | null => {
  if (!program.startDate) return null;

  const cacheKey = `${program.id}-${program.startDate}-${program.endDate || program.duration}`;

  if (!programDateRangeCache.has(cacheKey)) {
    const startDate = getCachedDate(program.startDate);

    let endDate: Date;
    if (program.endDate) {
      endDate = getCachedDate(program.endDate);
    } else if (program.duration) {
      endDate = new Date(startDate);
      const durationNum = typeof program.duration === 'string' ? parseInt(program.duration, 10) : program.duration;
      endDate.setDate(endDate.getDate() + durationNum - 1);
    } else {
      return null;
    }

    programDateRangeCache.set(cacheKey, { startDate, endDate });
  }

  return programDateRangeCache.get(cacheKey)!;
};

// Clear caches when needed (call this when programs change significantly)
export const clearCalendarCaches = (): void => {
  dateCache.clear();
  programDateRangeCache.clear();
  calendarDateCache.clear();
  dateRangeCache.clear();
};

/**
 * Convert date string to CalendarDate object (optimized with caching)
 */
export const createCalendarDate = (dateStr: string): CalendarDate => {
  // Check cache first
  if (calendarDateCache.has(dateStr)) {
    return calendarDateCache.get(dateStr)!;
  }

  // Parse date string as local date to avoid timezone issues
  const date = getCachedDate(dateStr);
  const today = getCachedDate(new Date().toISOString().split('T')[0]);

  const calendarDate: CalendarDate = {
    date: dateStr,
    day: date.getDate(),
    month: date.getMonth(),
    year: date.getFullYear(),
    isToday: date.getTime() === today.getTime(),
    isCurrentMonth: date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear(),
  };

  // Cache the result
  calendarDateCache.set(dateStr, calendarDate);
  return calendarDate;
};

// Cache for date ranges
const dateRangeCache = new Map<string, string[]>();

/**
 * Generate date range between start and end dates (optimized with caching)
 */
export const generateDateRange = (startDate: string, endDate: string): string[] => {
  const cacheKey = `${startDate}-${endDate}`;

  // Check cache first
  if (dateRangeCache.has(cacheKey)) {
    return dateRangeCache.get(cacheKey)!;
  }

  const dates: string[] = [];
  const start = getCachedDate(startDate);
  const end = getCachedDate(endDate);

  const current = new Date(start);
  while (current <= end) {
    const year = current.getFullYear();
    const month = String(current.getMonth() + 1).padStart(2, '0');
    const day = String(current.getDate()).padStart(2, '0');
    dates.push(`${year}-${month}-${day}`);
    current.setDate(current.getDate() + 1);
  }

  // Cache the result
  dateRangeCache.set(cacheKey, dates);
  return dates;
};

/**
 * Calculate current day for a program based on start date
 * DEPRECATED: This is the legacy function - prefer calculateSimpleProgramDay for better accuracy
 */
export const calculateProgramCurrentDay = (startDate: string): number => {
  // Parse start date as local date to avoid timezone issues
  const [startYear, startMonth, startDay] = startDate.split('-').map(Number);
  const start = new Date(startYear, startMonth - 1, startDay);
  start.setHours(0, 0, 0, 0);

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const diff = Math.floor((today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  return Math.max(1, diff + 1);
};

/**
 * Calculate current day for a program with timezone awareness
 * UPDATED: Now uses simplified participant-level timezone system
 */
export const calculateProgramCurrentDayWithTimezone = (
  startDate: string,
  userTimezone: string,
  programStatus: 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified',
  programDuration: number = 30
): number => {
  try {
    const { calculateParticipantProgramDay } = require('./timezoneUtils');
    const result = calculateParticipantProgramDay(startDate, userTimezone, programDuration);
    return result.currentDay;
  } catch (error) {
    console.error('Error using simplified calculation, falling back to legacy:', error);
    return calculateProgramCurrentDay(startDate);
  }
};

/**
 * Convert Program to ProgramTimelineData
 */
export const programToTimelineData = (program: Program): ProgramTimelineData => {
  // Determine if program needs attention based on status and setup
  const needsAttention = (program.status === 'upcoming' && program.setupStatus === false) ||
                        program.status === 'disqualified' ||
                        program.status === 'ended';

  let attentionReason: 'setup' | 'disqualified' | 'ended' | undefined;
  if (program.status === 'upcoming' && program.setupStatus === false) attentionReason = 'setup';
  else if (program.status === 'disqualified') attentionReason = 'disqualified';
  else if (program.status === 'ended') attentionReason = 'ended';

  // Calculate end date if not provided
  let endDate = program.endDate;
  if (!endDate && program.startDate && program.duration) {
    const start = new Date(program.startDate);
    start.setDate(start.getDate() + Number(program.duration) - 1);
    endDate = start.toISOString().split('T')[0];
  }

  return {
    programId: program.id,
    programName: program.name,
    startDate: program.startDate || '',
    endDate: endDate || '',
    status: program.status || 'upcoming',
    setupStatus: program.setupStatus || null,
    category: program.category || '',
    betAmount: program.betAmount,
    currentDay: program.startDate ? calculateProgramCurrentDayWithTimezone(
      program.startDate,
      getUserTimezone(),
      program.status || 'ongoing'
    ) : 1,
    totalDays: typeof program.duration === 'string' ? Number(program.duration) : program.duration,
    needsAttention,
    attentionReason,
  };
};

/**
 * Generate calendar data for a specific month
 */
export const generateCalendarMonth = (
  year: number,
  month: number,
  programs: Program[],
  userDaysData?: { [programId: string]: DayTile[] }
): CalendarDayData[] => {
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startingDayOfWeek = firstDay.getDay();

  const calendarDays: CalendarDayData[] = [];

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < startingDayOfWeek; i++) {
    const emptyDate = new Date(year, month, -startingDayOfWeek + i + 1);
    const dateYear = emptyDate.getFullYear();
    const dateMonth = String(emptyDate.getMonth() + 1).padStart(2, '0');
    const dateDay = String(emptyDate.getDate()).padStart(2, '0');
    const dateStr = `${dateYear}-${dateMonth}-${dateDay}`;

    calendarDays.push({
      ...createCalendarDate(dateStr),
      isCurrentMonth: false,
      programs: [],
      hasPrograms: false,
      needsAttention: false,
      dayStatuses: {},
    });
  }

  // Add days of the current month
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const dateYear = date.getFullYear();
    const dateMonth = String(date.getMonth() + 1).padStart(2, '0');
    const dateDay = String(date.getDate()).padStart(2, '0');
    const dateStr = `${dateYear}-${dateMonth}-${dateDay}`;
    const calendarDate = createCalendarDate(dateStr);

    // Find programs that are active on this date
    const dayPrograms: ProgramTimelineData[] = [];
    const dayStatuses: { [programId: string]: DayTile } = {};

    programs.forEach(program => {
      const dateRange = getCachedProgramDateRange(program);
      if (dateRange) {
        const { startDate: programStart, endDate: programEnd } = dateRange;
        const currentDate = getCachedDate(dateStr);

        if (currentDate >= programStart && currentDate <= programEnd) {
          const timelineData = programToTimelineData(program);
          dayPrograms.push(timelineData);

          // Calculate the correct program day for this calendar date
          const daysDiff = Math.floor((currentDate.getTime() - programStart.getTime()) / (1000 * 60 * 60 * 24));
          const programDay = daysDiff + 1;

          // Add day status if available
          if (userDaysData && userDaysData[program.id]) {
            const dayTile = userDaysData[program.id].find(d => d.day === programDay);
            if (dayTile) {
              dayStatuses[program.id] = dayTile;
            }
          }
        }
      }
    });

    const hasPrograms = dayPrograms.length > 0;
    const needsAttention = dayPrograms.some(p => p.needsAttention);

    calendarDays.push({
      ...calendarDate,
      programs: dayPrograms,
      hasPrograms,
      needsAttention,
      dayStatuses,
    });
  }

  // Fill remaining cells to complete the calendar grid (42 cells total for 6 weeks)
  const totalCells = 42;
  const remainingCells = totalCells - calendarDays.length;

  for (let i = 1; i <= remainingCells; i++) {
    const nextMonthDate = new Date(year, month + 1, i);
    const dateYear = nextMonthDate.getFullYear();
    const dateMonth = String(nextMonthDate.getMonth() + 1).padStart(2, '0');
    const dateDay = String(nextMonthDate.getDate()).padStart(2, '0');
    const dateStr = `${dateYear}-${dateMonth}-${dateDay}`;

    calendarDays.push({
      ...createCalendarDate(dateStr),
      isCurrentMonth: false,
      programs: [],
      hasPrograms: false,
      needsAttention: false,
      dayStatuses: {},
    });
  }

  return calendarDays;
};

/**
 * Get programs that need attention
 */
export const getProgramsNeedingAttention = (programs: Program[]): ProgramTimelineData[] => {
  return programs
    .map(programToTimelineData)
    .filter(p => p.needsAttention);
};

/**
 * Format date for display
 */
export const formatDateForDisplay = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Get month name
 */
export const getMonthName = (month: number): string => {
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  return months[month];
};

/**
 * Navigate to previous/next month
 */
export const navigateMonth = (currentDate: Date, direction: 'prev' | 'next'): Date => {
  const newDate = new Date(currentDate);
  if (direction === 'prev') {
    newDate.setMonth(newDate.getMonth() - 1);
  } else {
    newDate.setMonth(newDate.getMonth() + 1);
  }
  return newDate;
};

/**
 * Navigate to previous/next week
 */
export const navigateWeek = (currentDate: Date, direction: 'prev' | 'next'): Date => {
  const newDate = new Date(currentDate);
  if (direction === 'prev') {
    newDate.setDate(newDate.getDate() - 7);
  } else {
    newDate.setDate(newDate.getDate() + 7);
  }
  return newDate;
};

/**
 * Get the start of the week for a given date (Sunday)
 */
export const getWeekStart = (date: Date): Date => {
  const start = new Date(date);
  start.setDate(start.getDate() - start.getDay());
  start.setHours(0, 0, 0, 0);
  return start;
};

/**
 * Generate calendar data for specific number of weeks
 */
export const generateCalendarWeeks = (
  year: number,
  month: number,
  programs: Program[],
  weeksToShow: number = 1,
  startDate?: Date,
  userDaysData?: { [programId: string]: DayTile[] }
): CalendarDayData[] => {
  // Determine the starting week
  let weekStart: Date;
  if (startDate) {
    weekStart = getWeekStart(startDate);
  } else {
    // Start from the first week of the month
    const firstDay = new Date(year, month, 1);
    weekStart = getWeekStart(firstDay);
  }

  const calendarDays: CalendarDayData[] = [];
  const totalDays = weeksToShow * 7;

  // Generate days for the specified number of weeks
  for (let i = 0; i < totalDays; i++) {
    const currentDate = new Date(weekStart);
    currentDate.setDate(weekStart.getDate() + i);

    const dateYear = currentDate.getFullYear();
    const dateMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    const dateDay = String(currentDate.getDate()).padStart(2, '0');
    const dateStr = `${dateYear}-${dateMonth}-${dateDay}`;

    const calendarDate = createCalendarDate(dateStr);

    // Check if this date is in the current month being viewed
    const isCurrentMonth = currentDate.getMonth() === month && currentDate.getFullYear() === year;

    // Find programs that are active on this date
    const dayPrograms: ProgramTimelineData[] = [];
    const dayStatuses: { [programId: string]: DayTile } = {};

    programs.forEach(program => {
      const dateRange = getCachedProgramDateRange(program);
      if (dateRange) {
        const { startDate: programStart, endDate: programEnd } = dateRange;
        const checkDate = getCachedDate(dateStr);

        if (checkDate >= programStart && checkDate <= programEnd) {
          const timelineData = programToTimelineData(program);
          dayPrograms.push(timelineData);

          // Calculate the correct program day for this calendar date
          const daysDiff = Math.floor((checkDate.getTime() - programStart.getTime()) / (1000 * 60 * 60 * 24));
          const programDay = daysDiff + 1;

          // Add day status if available
          if (userDaysData && userDaysData[program.id]) {
            const dayTile = userDaysData[program.id].find(d => d.day === programDay);
            if (dayTile) {
              dayStatuses[program.id] = dayTile;
            }
          }
        }
      }
    });

    const hasPrograms = dayPrograms.length > 0;
    const needsAttention = dayPrograms.some(p => p.needsAttention);

    calendarDays.push({
      ...calendarDate,
      isCurrentMonth,
      programs: dayPrograms,
      hasPrograms,
      needsAttention,
      dayStatuses,
    });
  }

  return calendarDays;
};
