// Payment Router Service
// Routes payments to appropriate payment providers based on payment type and platform

import { Platform } from 'react-native';
import { PaymentType, PaymentProvider, PAYMENT_ROUTING } from '../config/payments';
import {
  PaymentData,
  PaymentResult,
  StripePaymentData,
  InAppPurchaseData,
  SubscriptionPurchaseData,
  GraceDayPurchaseData,
  isStripePaymentData,
  isInAppPurchaseData,
  isSubscriptionPurchaseData,
  isGraceDayPurchaseData,
  convertToLegacyPaymentResult,
  LegacyPaymentData,
  LegacyPaymentResult,
  convertLegacyPaymentData,
} from '../../shared/types/payment';

// Import existing payment services
import { 
  processStripePayment as legacyProcessStripePayment,
  processGooglePayPayment as legacyProcessGooglePayPayment,
  processApplePayPayment as legacyProcessApplePayPayment,
} from './paymentService';

// Import new in-app purchase service
import { inAppPurchaseService } from './inAppPurchaseService';

class PaymentRouter {
  /**
   * Main payment processing function that routes to appropriate provider
   */
  async processPayment(paymentData: PaymentData): Promise<PaymentResult> {
    try {
      // Determine the appropriate payment provider
      const provider = this.determinePaymentProvider(paymentData.paymentType);
      
      console.log(`Routing ${paymentData.paymentType} payment to ${provider}`);

      // Route to appropriate payment method
      switch (provider) {
        case PaymentProvider.STRIPE:
          return await this.processStripePayment(paymentData);
          
        case PaymentProvider.GOOGLE_PLAY:
          return await this.processGooglePlayPayment(paymentData);
          
        case PaymentProvider.APPLE_APP_STORE:
          return await this.processAppleStorePayment(paymentData);
          
        default:
          throw new Error(`Unsupported payment provider: ${provider}`);
      }
    } catch (error) {
      console.error('Payment routing failed:', error);
      
      // Try fallback provider if available
      const fallbackProvider = this.getFallbackProvider(paymentData.paymentType);
      if (fallbackProvider && fallbackProvider !== this.determinePaymentProvider(paymentData.paymentType)) {
        console.log(`Attempting fallback payment with ${fallbackProvider}`);
        return await this.processPaymentWithProvider(paymentData, fallbackProvider);
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment processing failed',
        provider: this.determinePaymentProvider(paymentData.paymentType),
      } as PaymentResult;
    }
  }

  /**
   * Process payment with specific provider (for fallback scenarios)
   */
  private async processPaymentWithProvider(paymentData: PaymentData, provider: PaymentProvider): Promise<PaymentResult> {
    switch (provider) {
      case PaymentProvider.STRIPE:
        return await this.processStripePayment(paymentData);
        
      case PaymentProvider.GOOGLE_PLAY:
        return await this.processGooglePlayPayment(paymentData);
        
      case PaymentProvider.APPLE_APP_STORE:
        return await this.processAppleStorePayment(paymentData);
        
      default:
        throw new Error(`Unsupported fallback provider: ${provider}`);
    }
  }

  /**
   * Process Stripe payments (for commits and pools)
   */
  private async processStripePayment(paymentData: PaymentData): Promise<PaymentResult> {
    if (!isStripePaymentData(paymentData)) {
      throw new Error('Invalid payment data for Stripe payment');
    }

    // Convert to legacy format for existing Stripe service
    const legacyData: LegacyPaymentData = {
      amount: paymentData.amount,
      currency: paymentData.currency,
      description: paymentData.description,
      programId: paymentData.programId,
      userId: paymentData.userId,
    };

    // Use existing Stripe payment service
    const legacyResult = await legacyProcessStripePayment(legacyData);

    // Convert back to new format
    return {
      success: legacyResult.success,
      error: legacyResult.error,
      provider: PaymentProvider.STRIPE,
      paymentIntentId: legacyResult.paymentIntentId,
    };
  }

  /**
   * Process Google Play Store payments (for premium subscriptions and grace days)
   */
  private async processGooglePlayPayment(paymentData: PaymentData): Promise<PaymentResult> {
    if (!isInAppPurchaseData(paymentData)) {
      throw new Error('Invalid payment data for Google Play payment');
    }

    if (Platform.OS !== 'android') {
      throw new Error('Google Play payments are only supported on Android');
    }

    if (isSubscriptionPurchaseData(paymentData)) {
      return await inAppPurchaseService.processPremiumSubscription(paymentData);
    } else if (isGraceDayPurchaseData(paymentData)) {
      return await inAppPurchaseService.processGraceDayPurchase(paymentData);
    } else {
      throw new Error('Unsupported payment type for Google Play');
    }
  }

  /**
   * Process Apple App Store payments (for premium subscriptions and grace days)
   */
  private async processAppleStorePayment(paymentData: PaymentData): Promise<PaymentResult> {
    if (!isInAppPurchaseData(paymentData)) {
      throw new Error('Invalid payment data for Apple App Store payment');
    }

    if (Platform.OS !== 'ios') {
      throw new Error('Apple App Store payments are only supported on iOS');
    }

    if (isSubscriptionPurchaseData(paymentData)) {
      return await inAppPurchaseService.processPremiumSubscription(paymentData);
    } else if (isGraceDayPurchaseData(paymentData)) {
      return await inAppPurchaseService.processGraceDayPurchase(paymentData);
    } else {
      throw new Error('Unsupported payment type for Apple App Store');
    }
  }

  /**
   * Determine the appropriate payment provider based on payment type and platform
   */
  private determinePaymentProvider(paymentType: PaymentType): PaymentProvider {
    const config = PAYMENT_ROUTING[paymentType];
    
    // For Stripe-only payments (commits and pools)
    if (paymentType === PaymentType.COMMIT || paymentType === PaymentType.POOL) {
      return PaymentProvider.STRIPE;
    }
    
    // For platform-specific payments (premium subscriptions and grace days)
    if (paymentType === PaymentType.PREMIUM_SUBSCRIPTION || paymentType === PaymentType.GRACE_DAY) {
      if (Platform.OS === 'ios') {
        return PaymentProvider.APPLE_APP_STORE;
      } else if (Platform.OS === 'android') {
        return PaymentProvider.GOOGLE_PLAY;
      } else {
        // Web fallback to Stripe
        return PaymentProvider.STRIPE;
      }
    }
    
    return config.defaultProvider;
  }

  /**
   * Get fallback provider for a payment type
   */
  private getFallbackProvider(paymentType: PaymentType): PaymentProvider | null {
    const config = PAYMENT_ROUTING[paymentType];
    return config.fallbackProviders?.[0] || null;
  }

  /**
   * Legacy support: Process payment using old interface
   */
  async processLegacyPayment(
    legacyData: LegacyPaymentData,
    paymentType: PaymentType.COMMIT | PaymentType.POOL
  ): Promise<LegacyPaymentResult> {
    const paymentData = convertLegacyPaymentData(legacyData, paymentType);
    const result = await this.processPayment(paymentData);
    return convertToLegacyPaymentResult(result);
  }

  /**
   * Get available payment methods for a specific payment type
   */
  getAvailablePaymentMethods(paymentType: PaymentType): PaymentProvider[] {
    const config = PAYMENT_ROUTING[paymentType];
    
    // Filter based on platform availability
    return config.providers.filter(provider => {
      switch (provider) {
        case PaymentProvider.STRIPE:
          return true; // Available on all platforms
        case PaymentProvider.GOOGLE_PLAY:
          return Platform.OS === 'android';
        case PaymentProvider.APPLE_APP_STORE:
          return Platform.OS === 'ios';
        default:
          return false;
      }
    });
  }

  /**
   * Check if a payment method is available for a payment type
   */
  isPaymentMethodAvailable(paymentType: PaymentType, provider: PaymentProvider): boolean {
    const availableMethods = this.getAvailablePaymentMethods(paymentType);
    return availableMethods.includes(provider);
  }

  /**
   * Get recommended payment method for a payment type
   */
  getRecommendedPaymentMethod(paymentType: PaymentType): PaymentProvider {
    return this.determinePaymentProvider(paymentType);
  }
}

// Export singleton instance
export const paymentRouter = new PaymentRouter();

// Export convenience functions for backward compatibility
export async function processPayment(paymentData: PaymentData): Promise<PaymentResult> {
  return paymentRouter.processPayment(paymentData);
}

export async function processLegacyPayment(
  legacyData: LegacyPaymentData,
  paymentType: PaymentType.COMMIT | PaymentType.POOL
): Promise<LegacyPaymentResult> {
  return paymentRouter.processLegacyPayment(legacyData, paymentType);
}
