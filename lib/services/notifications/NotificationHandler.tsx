import React, { useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import * as Notifications from 'expo-notifications';
import { AppState, AppStateStatus } from 'react-native';
import { NotificationType } from './PushNotificationService';

/**
 * NotificationHandler component
 * 
 * This component handles incoming notifications and navigation based on notification type.
 * It should be placed at the root level of your app to ensure it's always active.
 */
export const NotificationHandler: React.FC = () => {
  const router = useRouter();
  const notificationListener = useRef<any>();
  const responseListener = useRef<any>();
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    // Set up notification handlers
    setupNotificationHandlers();

    // Set up app state change listener
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Cleanup function
    return () => {
      if (notificationListener.current) {
        notificationListener.current.remove();
      }
      if (responseListener.current) {
        responseListener.current.remove();
      }
      subscription?.remove();
    };
  }, []);

  /**
   * Set up notification handlers
   */
  const setupNotificationHandlers = () => {
    try {
      // Handle notifications received while the app is in the foreground
      notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
        handleForegroundNotification(notification);
      });

      // Handle notification responses (when user taps on a notification)
      responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
        handleNotificationResponse(response);
      });
    } catch (error) {
      console.warn('Failed to set up notification handlers (this is expected in Expo Go):', error);
    }
  };

  /**
   * Handle app state changes
   */
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      // You can refresh data, check for new notifications, etc.
    }
    appState.current = nextAppState;
  };

  /**
   * Handle notifications received while the app is in the foreground
   */
  const handleForegroundNotification = (notification: Notifications.Notification) => {
    const { title, body, data } = notification.request.content;
    
    // You can customize how foreground notifications are displayed
    // For example, show a custom in-app notification banner

    // If you want to show a custom UI for foreground notifications,
    // you can dispatch an event or update a global state here
  };

  /**
   * Handle notification responses (when user taps on a notification)
   */
  const handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    const data = response.notification.request.content.data;
    const notificationType = data?.type as NotificationType;
    
    // Navigate based on notification type and data
    switch (notificationType) {
      case NotificationType.PROGRAM:
        handleProgramNotification(data);
        break;
      
      case NotificationType.ACCOUNT:
        handleAccountNotification(data);
        break;
      
      case NotificationType.REMINDER:
        handleReminderNotification(data);
        break;
      
      case NotificationType.POINTS:
        handlePointsNotification(data);
        break;
      
      default:
        // Default: navigate to notifications screen
        router.push('/Notifications');
        break;
    }
  };

  /**
   * Handle program-related notifications
   */
  const handleProgramNotification = (data: any) => {
    if (data.programId) {
      // Navigate to specific program details
      router.push(`/ProgramDetails?id=${data.programId}`);
    } else if (data.action === 'view_progress') {
      // Navigate to progress screen
      router.push('/(tabs)/progress');
    } else {
      // Default: navigate to programs screen
      router.push('/');
    }
  };

  /**
   * Handle account-related notifications
   */
  const handleAccountNotification = (data: any) => {
    if (data.action === 'setup_integration') {
      // Navigate to integrations screen
      router.push('/integrations');
    } else if (data.action === 'view_profile') {
      // Navigate to user profile
      router.push('/UserProfile');
    } else {
      // Default: navigate to user profile
      router.push('/UserProfile');
    }
  };

  /**
   * Handle reminder notifications
   */
  const handleReminderNotification = (data: any) => {
    if (data.action === 'submit_progress') {
      // Navigate to progress screen for submission
      router.push('/(tabs)/progress');
    } else if (data.programId) {
      // Navigate to specific program
      router.push(`/ProgramDetails?id=${data.programId}`);
    } else {
      // Default: navigate to progress screen
      router.push('/(tabs)/progress');
    }
  };

  /**
   * Handle points-related notifications
   */
  const handlePointsNotification = (data: any) => {
    // Navigate to a points/rewards screen if you have one
    // For now, navigate to profile where points might be displayed
    router.push('/UserProfile');
  };

  // This component doesn't render anything visible
  return null;
};

/**
 * Hook to get the current notification permission status
 */
export const useNotificationPermissions = () => {
  const [permissionStatus, setPermissionStatus] = React.useState<string>('undetermined');

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const { status } = await Notifications.getPermissionsAsync();
        setPermissionStatus(status);
      } catch (error) {
        console.warn('Failed to check notification permissions (this is expected in Expo Go):', error);
        setPermissionStatus('unavailable');
      }
    };

    checkPermissions();
  }, []);

  const requestPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setPermissionStatus(status);
      return status === 'granted';
    } catch (error) {
      console.warn('Failed to request notification permissions (this is expected in Expo Go):', error);
      setPermissionStatus('unavailable');
      return false;
    }
  };

  return {
    permissionStatus,
    requestPermissions,
    hasPermission: permissionStatus === 'granted',
  };
};

/**
 * Hook to schedule a local notification
 */
export const useLocalNotification = () => {
  const scheduleNotification = async (
    title: string,
    body: string,
    data?: any,
    trigger?: Notifications.NotificationTriggerInput | null
  ) => {
    try {
      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
        },
        trigger: trigger || null, // null means immediate
      });
      return id;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  };

  const cancelNotification = async (notificationId: string) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  };

  const cancelAllNotifications = async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  };

  return {
    scheduleNotification,
    cancelNotification,
    cancelAllNotifications,
  };
};
