import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { getApp } from 'firebase/app';
import { getFunctions, httpsCallable } from 'firebase/functions';
import Constants from 'expo-constants';

// Define notification channel IDs for Android
export const NOTIFICATION_CHANNELS = {
  ACCOUNT: 'accustom-account',
  PROGRAM: 'accustom-program',
  POINTS: 'accustom-points',
  REMINDER: 'accustom-reminder',
  DEFAULT: 'accustom-default',
};

// Define notification types
export enum NotificationType {
  ACCOUNT = 'account',
  PROGRAM = 'program',
  POINTS = 'points',
  REMINDER = 'reminder'
}

// Define notification priorities
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// Define FCM message types
export enum FcmMessageType {
  NOTIFICATION = 'notification', // Shows even when app is in background
  DATA = 'data'                  // Only processed when app is in foreground
}

// Interface for device info
interface DeviceInfo {
  platform: string;
  model?: string;
  osVersion?: string;
  appVersion?: string;
}

/**
 * Service for handling push notifications using Firebase Cloud Messaging
 */
class PushNotificationService {
  private expoPushToken: string | null = null;
  private deviceInfo: DeviceInfo | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;
  private isInitialized = false;

  /**
   * Initialize the push notification service
   * - Configures notification handlers
   * - Sets up notification channels for Android
   * - Requests notification permissions
   * - Registers the device token with Firebase
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Check if we're in Expo Go (which has limited notification support)
      const isExpoGo = Constants.appOwnership === 'expo';
      if (isExpoGo) {
        console.warn('Push notifications have limited functionality in Expo Go. Use a development build for full functionality.');
      }

      // Configure notification handler for when the app is in the foreground
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });

      // Set up notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupNotificationChannels();
      }

      // Get the device push token (may fail in Expo Go)
      const token = await this.registerForPushNotifications();
      if (!token && !isExpoGo) {
        return false;
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.warn('Push notification service initialization failed (this is expected in Expo Go):', error);
      // Return true to allow app to continue functioning without push notifications
      return true;
    }
  }

  /**
   * Set up notification channels for Android
   * Each channel corresponds to a different notification type
   */
  private async setupNotificationChannels(): Promise<void> {
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync(NOTIFICATION_CHANNELS.ACCOUNT, {
        name: 'Account Notifications',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FFCC00',
      });

      await Notifications.setNotificationChannelAsync(NOTIFICATION_CHANNELS.PROGRAM, {
        name: 'Program Notifications',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FFCC00',
      });

      await Notifications.setNotificationChannelAsync(NOTIFICATION_CHANNELS.POINTS, {
        name: 'Points Notifications',
        importance: Notifications.AndroidImportance.DEFAULT,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FFCC00',
      });

      await Notifications.setNotificationChannelAsync(NOTIFICATION_CHANNELS.REMINDER, {
        name: 'Reminders',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FFCC00',
      });

      await Notifications.setNotificationChannelAsync(NOTIFICATION_CHANNELS.DEFAULT, {
        name: 'Default Notifications',
        importance: Notifications.AndroidImportance.DEFAULT,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FFCC00',
      });
    }
  }

  /**
   * Register for push notifications
   * - Checks if the device is capable of receiving push notifications
   * - Requests permission from the user
   * - Gets the Expo push token
   * - Registers the token with Firebase
   */
  private async registerForPushNotifications(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications are not supported on simulators/emulators');
        return null;
      }

      // Check if we're in Expo Go
      const isExpoGo = Constants.appOwnership === 'expo';
      if (isExpoGo) {
        console.warn('Push notifications have limited functionality in Expo Go');
      }

      // Request permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permission not granted');
        return null;
      }

      // Get the token (may fail in Expo Go)
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.expoPushToken = tokenData.data;

      // Get device info
      this.deviceInfo = {
        platform: Platform.OS,
        model: Device.modelName || 'Unknown',
        osVersion: Platform.Version.toString(),
        appVersion: Constants.expoConfig?.version || '1.0.0',
      };

      // Register the token with Firebase
      if (this.deviceInfo) {
        await this.registerTokenWithFirebase(this.expoPushToken, this.deviceInfo);
      }

      return this.expoPushToken;
    } catch (error) {
      console.warn('Failed to register for push notifications (this is expected in Expo Go):', error);
      return null;
    }
  }

  /**
   * Register the FCM token with Firebase
   * @param token The FCM token
   * @param deviceInfo Information about the device
   */
  private async registerTokenWithFirebase(token: string, deviceInfo: DeviceInfo): Promise<void> {
    try {
      const app = getApp();
      const functions = getFunctions(app);
      const registerFcmToken = httpsCallable(functions, 'registerFcmToken');

      // Get the user ID
      const userId = await this.getUserId();
      if (!userId) {
        return;
      }

      // Call the Firebase function to register the token
      await registerFcmToken({
        userId,
        token,
        deviceInfo,
      });

    } catch (error) {
      console.error('Error registering FCM token with Firebase:', error);
    }
  }

  /**
   * Set up notification listeners
   * - Notification received listener: triggered when a notification is received while the app is in the foreground
   * - Notification response listener: triggered when the user taps on a notification
   */
  private setupNotificationListeners(): void {
    // Remove any existing listeners
    this.removeNotificationListeners();

    // Set up the notification received listener
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      // Handle the notification here (e.g., update UI, play sound, etc.)
    });

    // Set up the notification response listener
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      // Handle the user's interaction with the notification here (e.g., navigate to a screen)
      this.handleNotificationResponse(response);
    });
  }

  /**
   * Handle notification response (when user taps on a notification)
   * @param response The notification response
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const data = response.notification.request.content.data;
    
    // Navigate based on notification type
    if (data.type === NotificationType.PROGRAM) {
      // Navigate to program details or progress screen
      // Example: navigation.navigate('ProgramDetails', { programId: data.programId });
    } else if (data.type === NotificationType.ACCOUNT) {
      // Navigate to account settings
      // Example: navigation.navigate('UserProfile');
    } else if (data.type === NotificationType.REMINDER) {
      // Navigate to the relevant screen based on the reminder
      // Example: navigation.navigate('Progress');
    } else {
      // Default: navigate to notifications screen
      // Example: navigation.navigate('Notifications');
    }
  }

  /**
   * Remove notification listeners
   */
  private removeNotificationListeners(): void {
    if (this.notificationListener) {
      this.notificationListener.remove();
      this.notificationListener = null;
    }

    if (this.responseListener) {
      this.responseListener.remove();
      this.responseListener = null;
    }
  }

  /**
   * Get the current user ID
   * This is a placeholder - implement based on your authentication system
   */
  private async getUserId(): Promise<string | null> {
    // Implement based on your authentication system
    // Example: return auth.currentUser?.uid || null;
    // For now, we'll use a placeholder
    try {
      // Import getId from your auth utilities
      const { getId } = await import('../../utils/variables');
      return await getId();
    } catch (error) {
      console.error('Error getting user ID:', error);
      return null;
    }
  }

  /**
   * Clean up the service
   * - Unregister the FCM token
   * - Remove notification listeners
   */
  async cleanup(): Promise<void> {
    try {
      // Unregister the FCM token if available
      if (this.expoPushToken) {
        const userId = await this.getUserId();
        if (userId) {
          const app = getApp();
          const functions = getFunctions(app);
          const unregisterFcmToken = httpsCallable(functions, 'unregisterFcmToken');
          
          await unregisterFcmToken({
            userId,
            token: this.expoPushToken,
          });
        }
      }

      // Remove notification listeners
      this.removeNotificationListeners();
      
      this.isInitialized = false;
    } catch (error) {
      console.error('Error cleaning up push notification service:', error);
    }
  }
}

// Export a singleton instance
export const pushNotificationService = new PushNotificationService();
