import { getFunctions, httpsCallable } from 'firebase/functions';
import { getApp } from 'firebase/app';
import { firestoreService } from '../database';
import { NotificationType, NotificationPriority } from './PushNotificationService';

/**
 * Service for testing push notifications
 * This service provides methods to test the complete push notification flow
 */
export class TestNotificationService {
  
  /**
   * Test sending a simple push notification
   */
  async testSimplePushNotification(userId: string): Promise<boolean> {
    try {
      
      const result = await firestoreService.notifications.createNotification(userId, {
        title: "Test Notification",
        message: "This is a test push notification from Habit Royale!",
        type: "account",
        priority: "medium",
        data: {
          test: "true",
          action: "view_profile"
        }
      });

      if (result.success) {
        return true;
      } else {
        console.error('❌ Simple push notification test failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error testing simple push notification:', error);
      return false;
    }
  }

  /**
   * Test sending a program-related notification
   */
  async testProgramNotification(userId: string, programId?: string): Promise<boolean> {
    try {
      
      const result = await firestoreService.notifications.createProgramJoinedNotification(
        userId,
        "Test Program",
        "2024-01-01",
        programId
      );

      if (result.success) {
        return true;
      } else {
        console.error('❌ Program notification test failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error testing program notification:', error);
      return false;
    }
  }

  /**
   * Test sending a reminder notification
   */
  async testReminderNotification(userId: string): Promise<boolean> {
    try {
      
      const result = await firestoreService.notifications.createSetupReminderNotification(
        userId,
        "Test Program",
        "test-program-id"
      );

      if (result.success) {
        return true;
      } else {
        console.error('❌ Reminder notification test failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error testing reminder notification:', error);
      return false;
    }
  }

  /**
   * Test sending a high-priority notification
   */
  async testHighPriorityNotification(userId: string): Promise<boolean> {
    try {
      
      const result = await firestoreService.notifications.createNotification(userId, {
        title: "🚨 Urgent: Program Deadline Approaching!",
        message: "You have 2 hours left to submit your daily progress. Don't lose your streak!",
        type: "reminder",
        priority: "high",
        data: {
          action: "submit_progress",
          urgency: "high",
          deadline: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2 hours from now
        }
      });

      if (result.success) {
        return true;
      } else {
        console.error('❌ High priority notification test failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error testing high priority notification:', error);
      return false;
    }
  }

  /**
   * Test sending a data-only notification (for in-app processing)
   */
  async testDataOnlyNotification(userId: string): Promise<boolean> {
    try {
      
      const result = await firestoreService.notifications.createNotification(userId, {
        title: "Data Update",
        message: "Your progress data has been updated",
        type: "points",
        priority: "low",
        data: {
          type: "data_update",
          points: "100",
          streak: "5"
        }
      }, {
        sendPush: true,
        pushMessageType: 'data' // This will send as data-only message
      });

      if (result.success) {
        return true;
      } else {
        console.error('❌ Data-only notification test failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error testing data-only notification:', error);
      return false;
    }
  }

  /**
   * Test direct FCM function call (bypassing the notification service)
   */
  async testDirectFcmCall(userId: string): Promise<boolean> {
    try {
      
      const app = getApp();
      const functions = getFunctions(app);
      const sendPushNotification = httpsCallable(functions, 'sendPushNotification');
      
      const result = await sendPushNotification({
        userId,
        notification: {
          title: "Direct FCM Test",
          message: "This notification was sent directly via FCM function",
          type: "account",
          priority: "medium",
          data: {
            test: "direct_fcm",
            timestamp: new Date().toISOString()
          }
        },
        messageType: 'notification'
      });

      return true;
    } catch (error) {
      console.error('❌ Error testing direct FCM call:', error);
      return false;
    }
  }

  /**
   * Run all notification tests
   */
  async runAllTests(userId: string, programId?: string): Promise<{
    passed: number;
    failed: number;
    results: Array<{ test: string; passed: boolean; error?: string }>;
  }> {
    
    const tests = [
      { name: 'Simple Push Notification', fn: () => this.testSimplePushNotification(userId) },
      { name: 'Program Notification', fn: () => this.testProgramNotification(userId, programId) },
      { name: 'Reminder Notification', fn: () => this.testReminderNotification(userId) },
      { name: 'High Priority Notification', fn: () => this.testHighPriorityNotification(userId) },
      { name: 'Data-Only Notification', fn: () => this.testDataOnlyNotification(userId) },
      { name: 'Direct FCM Call', fn: () => this.testDirectFcmCall(userId) },
    ];

    const results = [];
    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        const success = await test.fn();
        
        if (success) {
          passed++;
          results.push({ test: test.name, passed: true });
        } else {
          failed++;
          results.push({ test: test.name, passed: false });
        }
        
        // Wait a bit between tests to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        failed++;
        results.push({ 
          test: test.name, 
          passed: false, 
          error: error instanceof Error ? error.message : String(error)
        });
        console.error(`❌ Test ${test.name} threw an error:`, error);
      }
    }

    results.forEach(result => {
      const icon = result.passed ? '✅' : '❌';
    });

    return { passed, failed, results };
  }

  /**
   * Test FCM token registration
   */
  async testTokenRegistration(userId: string, testToken?: string): Promise<boolean> {
    try {
      
      const app = getApp();
      const functions = getFunctions(app);
      const registerFcmToken = httpsCallable(functions, 'registerFcmToken');
      
      // Use a test token or generate a fake one for testing
      const token = testToken || `test_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const result = await registerFcmToken({
        userId,
        token,
        deviceInfo: {
          platform: 'test',
          model: 'Test Device',
          osVersion: '1.0.0',
          appVersion: '1.0.0'
        }
      });

      return true;
    } catch (error) {
      console.error('❌ Error testing FCM token registration:', error);
      return false;
    }
  }
}

// Export a singleton instance
export const testNotificationService = new TestNotificationService();
