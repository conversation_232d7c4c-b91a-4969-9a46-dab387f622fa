// Subscription Status Sync Service
// Syncs subscription status from app stores with local database

import { Platform } from 'react-native';
import { subscriptionService } from './subscriptionService';
import { getId } from '../utils/variables';

interface AppStoreSubscription {
  productId: string;
  transactionId: string;
  originalTransactionId?: string;
  purchaseDate: string;
  expirationDate?: string;
  isActive: boolean;
  isInTrial?: boolean;
  autoRenewing?: boolean;
  platform: 'ios' | 'android';
}

interface SubscriptionSyncResult {
  success: boolean;
  subscriptionsFound: number;
  activeSubscriptions: number;
  syncedToDatabase: boolean;
  error?: string;
}

class SubscriptionStatusSyncService {
  private lastSyncTime: Date | null = null;
  private syncInProgress = false;

  /**
   * Sync subscription status from app stores
   */
  async syncSubscriptionStatus(forceSync = false): Promise<SubscriptionSyncResult> {
    if (this.syncInProgress) {
      return {
        success: false,
        subscriptionsFound: 0,
        activeSubscriptions: 0,
        syncedToDatabase: false,
        error: 'Sync already in progress',
      };
    }

    // Check if sync is needed (avoid too frequent syncs)
    if (!forceSync && this.lastSyncTime) {
      const timeSinceLastSync = Date.now() - this.lastSyncTime.getTime();
      const minSyncInterval = 5 * 60 * 1000; // 5 minutes
      
      if (timeSinceLastSync < minSyncInterval) {
        return {
          success: true,
          subscriptionsFound: 0,
          activeSubscriptions: 0,
          syncedToDatabase: false,
          error: 'Sync not needed yet',
        };
      }
    }

    this.syncInProgress = true;

    try {
      console.log('Starting subscription status sync...');

      // Get subscriptions from app store
      const appStoreSubscriptions = await this.getAppStoreSubscriptions();
      
      // Find active subscriptions
      const activeSubscriptions = appStoreSubscriptions.filter(sub => sub.isActive);
      
      // Sync with local database
      const syncedToDatabase = await this.syncWithDatabase(activeSubscriptions);

      this.lastSyncTime = new Date();

      const result: SubscriptionSyncResult = {
        success: true,
        subscriptionsFound: appStoreSubscriptions.length,
        activeSubscriptions: activeSubscriptions.length,
        syncedToDatabase,
      };

      console.log('Subscription sync completed:', result);
      return result;

    } catch (error) {
      console.error('Subscription sync failed:', error);
      return {
        success: false,
        subscriptionsFound: 0,
        activeSubscriptions: 0,
        syncedToDatabase: false,
        error: error instanceof Error ? error.message : 'Unknown sync error',
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Get subscriptions from app store
   */
  private async getAppStoreSubscriptions(): Promise<AppStoreSubscription[]> {
    try {
      if (Platform.OS === 'ios') {
        return await this.getIOSSubscriptions();
      } else if (Platform.OS === 'android') {
        return await this.getAndroidSubscriptions();
      } else {
        console.log('App store subscriptions not available on web platform');
        return [];
      }
    } catch (error) {
      console.error('Failed to get app store subscriptions:', error);
      return [];
    }
  }

  /**
   * Get iOS subscriptions from App Store
   */
  private async getIOSSubscriptions(): Promise<AppStoreSubscription[]> {
    try {
      const RNIap = await import('react-native-iap');
      
      // Get available purchases (including subscriptions)
      const purchases = await RNIap.getAvailablePurchases();
      
      const subscriptions: AppStoreSubscription[] = [];

      for (const purchase of purchases) {
        // Check if it's a subscription product
        if (this.isSubscriptionProduct(purchase.productId)) {
          const subscription: AppStoreSubscription = {
            productId: purchase.productId,
            transactionId: purchase.transactionId,
            originalTransactionId: purchase.originalTransactionId,
            purchaseDate: new Date(purchase.transactionDate).toISOString(),
            isActive: await this.isSubscriptionActive(purchase),
            platform: 'ios',
          };

          subscriptions.push(subscription);
        }
      }

      return subscriptions;
    } catch (error) {
      console.error('Failed to get iOS subscriptions:', error);
      return [];
    }
  }

  /**
   * Get Android subscriptions from Google Play
   */
  private async getAndroidSubscriptions(): Promise<AppStoreSubscription[]> {
    try {
      const RNIap = await import('react-native-iap');
      
      // Get available purchases (including subscriptions)
      const purchases = await RNIap.getAvailablePurchases();
      
      const subscriptions: AppStoreSubscription[] = [];

      for (const purchase of purchases) {
        // Check if it's a subscription product
        if (this.isSubscriptionProduct(purchase.productId)) {
          const subscription: AppStoreSubscription = {
            productId: purchase.productId,
            transactionId: purchase.transactionId,
            purchaseDate: new Date(purchase.transactionDate).toISOString(),
            isActive: await this.isSubscriptionActive(purchase),
            autoRenewing: purchase.autoRenewing,
            platform: 'android',
          };

          subscriptions.push(subscription);
        }
      }

      return subscriptions;
    } catch (error) {
      console.error('Failed to get Android subscriptions:', error);
      return [];
    }
  }

  /**
   * Check if a product ID is a subscription product
   */
  private isSubscriptionProduct(productId: string): boolean {
    const subscriptionProducts = [
      'premium_monthly_subscription',
      'premium_yearly_subscription',
    ];
    return subscriptionProducts.includes(productId);
  }

  /**
   * Check if a subscription is currently active
   */
  private async isSubscriptionActive(purchase: any): Promise<boolean> {
    try {
      // For iOS, check if the subscription is not expired
      if (Platform.OS === 'ios') {
        // iOS subscriptions are automatically validated by the App Store
        // If it's returned by getAvailablePurchases(), it should be active
        return true;
      }

      // For Android, check auto-renewing status and expiration
      if (Platform.OS === 'android') {
        return purchase.autoRenewing === true;
      }

      return false;
    } catch (error) {
      console.error('Failed to check subscription status:', error);
      return false;
    }
  }

  /**
   * Sync active subscriptions with local database
   */
  private async syncWithDatabase(activeSubscriptions: AppStoreSubscription[]): Promise<boolean> {
    try {
      const userId = await getId();
      if (!userId) {
        console.error('User ID not available for subscription sync');
        return false;
      }

      if (activeSubscriptions.length === 0) {
        // No active subscriptions, ensure user is not marked as premium
        await subscriptionService.updateUserSubscription('', 'inactive');
        console.log('No active subscriptions found, user marked as non-premium');
        return true;
      }

      // Find the most recent active subscription
      const latestSubscription = activeSubscriptions.reduce((latest, current) => {
        return new Date(current.purchaseDate) > new Date(latest.purchaseDate) ? current : latest;
      });

      // Determine subscription plan
      const plan = latestSubscription.productId.includes('yearly') ? 'yearly' : 'monthly';
      
      // Calculate end date based on plan
      const startDate = new Date(latestSubscription.purchaseDate);
      const endDate = new Date(startDate);
      
      if (plan === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }

      // Update subscription status in database
      const success = await subscriptionService.updateUserSubscription(
        latestSubscription.originalTransactionId || latestSubscription.transactionId,
        'active',
        startDate.toISOString(),
        endDate.toISOString()
      );

      if (success) {
        console.log('Subscription status synced successfully:', {
          subscriptionId: latestSubscription.originalTransactionId || latestSubscription.transactionId,
          plan,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        });
      }

      return success;
    } catch (error) {
      console.error('Failed to sync with database:', error);
      return false;
    }
  }

  /**
   * Force refresh subscription status
   */
  async forceRefreshSubscriptionStatus(): Promise<SubscriptionSyncResult> {
    return this.syncSubscriptionStatus(true);
  }

  /**
   * Get last sync time
   */
  getLastSyncTime(): Date | null {
    return this.lastSyncTime;
  }

  /**
   * Check if sync is in progress
   */
  isSyncInProgress(): boolean {
    return this.syncInProgress;
  }

  /**
   * Schedule periodic sync (call this on app startup)
   */
  schedulePeriodicSync(): void {
    // Sync every 30 minutes
    const syncInterval = 30 * 60 * 1000;
    
    setInterval(() => {
      this.syncSubscriptionStatus(false);
    }, syncInterval);

    // Initial sync after 5 seconds
    setTimeout(() => {
      this.syncSubscriptionStatus(false);
    }, 5000);
  }
}

// Export singleton instance
export const subscriptionStatusSyncService = new SubscriptionStatusSyncService();
