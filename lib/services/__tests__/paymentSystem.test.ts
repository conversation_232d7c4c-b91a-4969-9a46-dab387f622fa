// Payment System Integration Test
// Tests the dual payment system imports and basic functionality

import {
  PaymentType,
  PaymentProvider,
  PAYMENT_ROUTING,
  getAvailablePaymentMethods,
  getPrimaryPaymentProvider,
  isPaymentMethodSupported,
} from '../../config/payments';

describe('Payment System Configuration', () => {
  test('Payment types and providers are defined correctly', () => {
    // Test PaymentType enum
    expect(PaymentType.COMMIT).toBe('commit');
    expect(PaymentType.POOL).toBe('pool');
    expect(PaymentType.PREMIUM_SUBSCRIPTION).toBe('premium_subscription');
    expect(PaymentType.GRACE_DAY).toBe('grace_day');

    // Test PaymentProvider enum
    expect(PaymentProvider.STRIPE).toBe('stripe');
    expect(PaymentProvider.GOOGLE_PLAY).toBe('google_play');
    expect(PaymentProvider.APPLE_APP_STORE).toBe('apple_app_store');
  });

  test('Payment routing configuration is properly defined', () => {
    expect(PAYMENT_ROUTING).toBeDefined();
    expect(typeof PAYMENT_ROUTING).toBe('object');

    // Check that all payment types have routing configuration
    expect(PAYMENT_ROUTING[PaymentType.COMMIT]).toBeDefined();
    expect(PAYMENT_ROUTING[PaymentType.POOL]).toBeDefined();
    expect(PAYMENT_ROUTING[PaymentType.PREMIUM_SUBSCRIPTION]).toBeDefined();
    expect(PAYMENT_ROUTING[PaymentType.GRACE_DAY]).toBeDefined();
  });

  test('Commit and pool payments use Stripe exclusively', () => {
    const commitConfig = PAYMENT_ROUTING[PaymentType.COMMIT];
    const poolConfig = PAYMENT_ROUTING[PaymentType.POOL];

    expect(commitConfig.providers).toEqual([PaymentProvider.STRIPE]);
    expect(commitConfig.defaultProvider).toBe(PaymentProvider.STRIPE);

    expect(poolConfig.providers).toEqual([PaymentProvider.STRIPE]);
    expect(poolConfig.defaultProvider).toBe(PaymentProvider.STRIPE);
  });

  test('Premium subscriptions and grace days support multiple providers', () => {
    const subscriptionConfig = PAYMENT_ROUTING[PaymentType.PREMIUM_SUBSCRIPTION];
    const graceDayConfig = PAYMENT_ROUTING[PaymentType.GRACE_DAY];

    expect(subscriptionConfig.providers.length).toBeGreaterThan(1);
    expect(subscriptionConfig.providers).toContain(PaymentProvider.STRIPE);
    expect(subscriptionConfig.fallbackProviders).toContain(PaymentProvider.STRIPE);

    expect(graceDayConfig.providers.length).toBeGreaterThan(1);
    expect(graceDayConfig.providers).toContain(PaymentProvider.STRIPE);
    expect(graceDayConfig.fallbackProviders).toContain(PaymentProvider.STRIPE);
  });

  test('Helper functions work correctly', () => {
    // Test getAvailablePaymentMethods
    const commitMethods = getAvailablePaymentMethods(PaymentType.COMMIT, 'web');
    expect(Array.isArray(commitMethods)).toBe(true);
    expect(commitMethods).toContain(PaymentProvider.STRIPE);

    // Test getPrimaryPaymentProvider
    const primaryProvider = getPrimaryPaymentProvider(PaymentType.COMMIT, 'web');
    expect(primaryProvider).toBe(PaymentProvider.STRIPE);

    // Test isPaymentMethodSupported
    const isStripeSupported = isPaymentMethodSupported(PaymentType.COMMIT, PaymentProvider.STRIPE, 'web');
    expect(isStripeSupported).toBe(true);

    const isGooglePlaySupported = isPaymentMethodSupported(PaymentType.COMMIT, PaymentProvider.GOOGLE_PLAY, 'web');
    expect(isGooglePlaySupported).toBe(false);
  });

  test('Platform-specific payment methods are correctly configured', () => {
    // iOS should support Apple App Store for subscriptions
    const iosSubscriptionMethods = getAvailablePaymentMethods(PaymentType.PREMIUM_SUBSCRIPTION, 'ios');
    expect(iosSubscriptionMethods).toContain(PaymentProvider.APPLE_APP_STORE);
    expect(iosSubscriptionMethods).toContain(PaymentProvider.STRIPE);

    // Android should support Google Play for subscriptions
    const androidSubscriptionMethods = getAvailablePaymentMethods(PaymentType.PREMIUM_SUBSCRIPTION, 'android');
    expect(androidSubscriptionMethods).toContain(PaymentProvider.GOOGLE_PLAY);
    expect(androidSubscriptionMethods).toContain(PaymentProvider.STRIPE);

    // Web should only support Stripe for subscriptions
    const webSubscriptionMethods = getAvailablePaymentMethods(PaymentType.PREMIUM_SUBSCRIPTION, 'web');
    expect(webSubscriptionMethods).toEqual([PaymentProvider.STRIPE]);
  });
});

// Export for manual testing
export const testPaymentSystem = () => {
  console.log('Testing Payment System Configuration...');

  console.log('PaymentType:', PaymentType);
  console.log('PaymentProvider:', PaymentProvider);

  console.log('Available methods for COMMIT (web):', getAvailablePaymentMethods(PaymentType.COMMIT, 'web'));
  console.log('Available methods for PREMIUM_SUBSCRIPTION (ios):', getAvailablePaymentMethods(PaymentType.PREMIUM_SUBSCRIPTION, 'ios'));
  console.log('Available methods for PREMIUM_SUBSCRIPTION (android):', getAvailablePaymentMethods(PaymentType.PREMIUM_SUBSCRIPTION, 'android'));
  console.log('Available methods for PREMIUM_SUBSCRIPTION (web):', getAvailablePaymentMethods(PaymentType.PREMIUM_SUBSCRIPTION, 'web'));

  console.log('Primary provider for COMMIT (web):', getPrimaryPaymentProvider(PaymentType.COMMIT, 'web'));
  console.log('Primary provider for PREMIUM_SUBSCRIPTION (ios):', getPrimaryPaymentProvider(PaymentType.PREMIUM_SUBSCRIPTION, 'ios'));

  console.log('Payment System Configuration Test Complete ✅');
};
