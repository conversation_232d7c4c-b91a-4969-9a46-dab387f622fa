// Commit Payment Service
// Handles commit and pool payments using Stripe exclusively
// Provides backward compatibility while using the new payment router

import { PaymentType, PaymentProvider } from '../config/payments';
import {
  StripePaymentData,
  PaymentResult,
  LegacyPaymentData,
  LegacyPaymentResult,
  convertLegacyPaymentData,
  convertToLegacyPaymentResult,
  isStripePaymentResult,
} from '../../shared/types/payment';
import { paymentRouter } from './paymentRouter';

// Re-export legacy interfaces for backward compatibility
export type { LegacyPaymentData as PaymentData, LegacyPaymentResult as PaymentResult };

class CommitPaymentService {
  /**
   * Process commit payment (program enrollment)
   */
  async processCommitPayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
    try {
      console.log('Processing commit payment:', {
        programId: paymentData.programId,
        amount: paymentData.amount,
        userId: paymentData.userId,
      });

      // Convert legacy data to new format
      const stripePaymentData = convertLegacyPaymentData(paymentData, PaymentType.COMMIT);

      // Process through payment router (will use Stripe)
      const result = await paymentRouter.processPayment(stripePaymentData);

      // Validate that Stripe was used
      if (result.provider !== PaymentProvider.STRIPE) {
        console.warn(`Expected Stripe payment but got ${result.provider}`);
      }

      // Convert back to legacy format
      return convertToLegacyPaymentResult(result);
    } catch (error) {
      console.error('Commit payment failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Commit payment failed',
      };
    }
  }

  /**
   * Process pool payment (joining a pool program)
   */
  async processPoolPayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
    try {
      console.log('Processing pool payment:', {
        programId: paymentData.programId,
        amount: paymentData.amount,
        userId: paymentData.userId,
      });

      // Convert legacy data to new format
      const stripePaymentData = convertLegacyPaymentData(paymentData, PaymentType.POOL);

      // Process through payment router (will use Stripe)
      const result = await paymentRouter.processPayment(stripePaymentData);

      // Validate that Stripe was used
      if (result.provider !== PaymentProvider.STRIPE) {
        console.warn(`Expected Stripe payment but got ${result.provider}`);
      }

      // Convert back to legacy format
      return convertToLegacyPaymentResult(result);
    } catch (error) {
      console.error('Pool payment failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Pool payment failed',
      };
    }
  }

  /**
   * Process Stripe payment (generic - determines type based on context)
   */
  async processStripePayment(
    paymentData: LegacyPaymentData,
    paymentType: PaymentType.COMMIT | PaymentType.POOL = PaymentType.COMMIT
  ): Promise<LegacyPaymentResult> {
    if (paymentType === PaymentType.COMMIT) {
      return this.processCommitPayment(paymentData);
    } else {
      return this.processPoolPayment(paymentData);
    }
  }

  /**
   * Process Google Pay payment for commits/pools (uses Stripe backend)
   */
  async processGooglePayPayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
    // Google Pay for commits/pools still uses Stripe backend
    // This maintains the existing behavior
    return this.processCommitPayment(paymentData);
  }

  /**
   * Process Apple Pay payment for commits/pools (uses Stripe backend)
   */
  async processApplePayPayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
    // Apple Pay for commits/pools still uses Stripe backend
    // This maintains the existing behavior
    return this.processCommitPayment(paymentData);
  }

  /**
   * Validate payment data for commit/pool payments
   */
  validatePaymentData(paymentData: LegacyPaymentData): { valid: boolean; error?: string } {
    if (!paymentData.userId) {
      return { valid: false, error: 'User ID is required' };
    }

    if (!paymentData.programId) {
      return { valid: false, error: 'Program ID is required' };
    }

    if (!paymentData.amount || paymentData.amount <= 0) {
      return { valid: false, error: 'Valid amount is required' };
    }

    if (!paymentData.currency) {
      return { valid: false, error: 'Currency is required' };
    }

    if (!paymentData.description) {
      return { valid: false, error: 'Description is required' };
    }

    return { valid: true };
  }

  /**
   * Get available payment methods for commits/pools
   */
  getAvailablePaymentMethods(): string[] {
    return ['stripe', 'apple_pay', 'google_pay'];
  }

  /**
   * Check if payment method is supported for commits/pools
   */
  isPaymentMethodSupported(method: string): boolean {
    const availableMethods = this.getAvailablePaymentMethods();
    return availableMethods.includes(method.toLowerCase());
  }

  /**
   * Get payment provider information
   */
  getPaymentProviderInfo() {
    return {
      provider: PaymentProvider.STRIPE,
      supportedMethods: this.getAvailablePaymentMethods(),
      description: 'All commit and pool payments are processed through Stripe',
    };
  }
}

// Export singleton instance
export const commitPaymentService = new CommitPaymentService();

// Export convenience functions for backward compatibility
export async function processStripePayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
  return commitPaymentService.processStripePayment(paymentData);
}

export async function processGooglePayPayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
  return commitPaymentService.processGooglePayPayment(paymentData);
}

export async function processApplePayPayment(paymentData: LegacyPaymentData): Promise<LegacyPaymentResult> {
  return commitPaymentService.processApplePayPayment(paymentData);
}

// Legacy exports for existing code
export { commitPaymentService as paymentService };

// Type guards for payment results
export function isSuccessfulPayment(result: LegacyPaymentResult): boolean {
  return result.success && !!result.paymentIntentId;
}

export function getPaymentError(result: LegacyPaymentResult): string {
  return result.error || 'Unknown payment error';
}

// Helper functions
export function formatPaymentAmount(amountInCents: number, currency: string = 'USD'): string {
  const amount = amountInCents / 100;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount);
}

export function validatePaymentAmount(amountInCents: number): boolean {
  return amountInCents > 0 && amountInCents <= 999999; // Max $9,999.99
}

export function createPaymentDescription(programName: string, paymentType: 'commit' | 'pool' = 'commit'): string {
  const typeLabel = paymentType === 'commit' ? 'Commitment' : 'Pool Entry';
  return `Accustom ${typeLabel} - ${programName}`;
}
