import * as Calendar from 'expo-calendar';
import { Platform } from 'react-native';

export interface CalendarEvent {
  title: string;
  startDate: Date;
  endDate: Date;
  notes?: string;
  location?: string;
  alarms?: Calendar.Alarm[];
  recurrenceRule?: Calendar.RecurrenceRule;
}

export interface CalendarServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CalendarPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Calendar.CalendarPermissionResponse['status'];
}

class CalendarService {
  private defaultCalendarId: string | null = null;

  /**
   * Request calendar permissions
   */
  async requestCalendarPermissions(): Promise<CalendarServiceResponse<CalendarPermissionStatus>> {
    try {
      // Web doesn't support calendar integration
      if (Platform.OS === 'web') {
        return {
          success: false,
          error: 'Calendar integration is not supported on web platform'
        };
      }

      const { status, canAskAgain, granted } = await Calendar.requestCalendarPermissionsAsync();
      
      return {
        success: true,
        data: {
          granted,
          canAskAgain,
          status
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to request calendar permissions'
      };
    }
  }

  /**
   * Get calendar permissions status
   */
  async getCalendarPermissions(): Promise<CalendarServiceResponse<CalendarPermissionStatus>> {
    try {
      if (Platform.OS === 'web') {
        return {
          success: false,
          error: 'Calendar integration is not supported on web platform'
        };
      }

      const { status, canAskAgain, granted } = await Calendar.getCalendarPermissionsAsync();
      
      return {
        success: true,
        data: {
          granted,
          canAskAgain,
          status
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get calendar permissions'
      };
    }
  }

  /**
   * Get or create default calendar for the app
   */
  async getDefaultCalendar(): Promise<CalendarServiceResponse<string>> {
    try {
      if (Platform.OS === 'web') {
        return {
          success: false,
          error: 'Calendar integration is not supported on web platform'
        };
      }

      // Check permissions first
      const permissionResult = await this.getCalendarPermissions();
      if (!permissionResult.success || !permissionResult.data?.granted) {
        return {
          success: false,
          error: 'Calendar permissions not granted'
        };
      }

      // Return cached calendar ID if available
      if (this.defaultCalendarId) {
        return {
          success: true,
          data: this.defaultCalendarId
        };
      }

      // Get all calendars
      const calendars = await Calendar.getCalendarsAsync(Calendar.EntityTypes.EVENT);
      
      // Look for existing Accustom calendar
      let accustomCalendar = calendars.find(cal => 
        cal.title === 'Accustom' && cal.allowsModifications
      );

      if (accustomCalendar) {
        this.defaultCalendarId = accustomCalendar.id;
        return {
          success: true,
          data: accustomCalendar.id
        };
      }

      // Find a writable calendar to use as default
      const writableCalendar = calendars.find(cal => 
        cal.allowsModifications && cal.accessLevel === Calendar.CalendarAccessLevel.OWNER
      );

      if (writableCalendar) {
        this.defaultCalendarId = writableCalendar.id;
        return {
          success: true,
          data: writableCalendar.id
        };
      }

      // Try to create a new calendar if no suitable one exists
      try {
        const defaultCalendarSource = Platform.OS === 'ios' 
          ? await Calendar.getDefaultCalendarAsync()
          : { isLocalAccount: true, name: 'Accustom' };

        const calendarId = await Calendar.createCalendarAsync({
          title: 'Accustom',
          color: '#FFCC00', // Accustom brand color
          entityType: Calendar.EntityTypes.EVENT,
          sourceId: Platform.OS === 'ios' ? defaultCalendarSource.source.id : undefined,
          source: Platform.OS === 'android' ? {
            isLocalAccount: true,
            name: 'Accustom'
          } : undefined,
          name: 'Accustom',
          ownerAccount: 'Accustom',
          accessLevel: Calendar.CalendarAccessLevel.OWNER,
        });

        this.defaultCalendarId = calendarId;
        return {
          success: true,
          data: calendarId
        };
      } catch (createError) {
        // If we can't create a calendar, fall back to the first writable one
        const fallbackCalendar = calendars.find(cal => cal.allowsModifications);
        if (fallbackCalendar) {
          this.defaultCalendarId = fallbackCalendar.id;
          return {
            success: true,
            data: fallbackCalendar.id
          };
        }

        return {
          success: false,
          error: 'No writable calendar found and unable to create new calendar'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get default calendar'
      };
    }
  }

  /**
   * Create a calendar event
   */
  async createEvent(event: CalendarEvent): Promise<CalendarServiceResponse<string>> {
    try {
      if (Platform.OS === 'web') {
        return {
          success: false,
          error: 'Calendar integration is not supported on web platform'
        };
      }

      // Get default calendar
      const calendarResult = await this.getDefaultCalendar();
      if (!calendarResult.success || !calendarResult.data) {
        return {
          success: false,
          error: calendarResult.error || 'Failed to get calendar'
        };
      }

      // Create the event
      const eventId = await Calendar.createEventAsync(calendarResult.data, {
        title: event.title,
        startDate: event.startDate,
        endDate: event.endDate,
        notes: event.notes,
        location: event.location,
        alarms: event.alarms,
        recurrenceRule: event.recurrenceRule,
      });

      return {
        success: true,
        data: eventId
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create calendar event'
      };
    }
  }

  /**
   * Update a calendar event
   */
  async updateEvent(eventId: string, event: Partial<CalendarEvent>): Promise<CalendarServiceResponse<void>> {
    try {
      if (Platform.OS === 'web') {
        return {
          success: false,
          error: 'Calendar integration is not supported on web platform'
        };
      }

      await Calendar.updateEventAsync(eventId, {
        title: event.title,
        startDate: event.startDate,
        endDate: event.endDate,
        notes: event.notes,
        location: event.location,
        alarms: event.alarms,
        recurrenceRule: event.recurrenceRule,
      });

      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update calendar event'
      };
    }
  }

  /**
   * Delete a calendar event
   */
  async deleteEvent(eventId: string): Promise<CalendarServiceResponse<void>> {
    try {
      if (Platform.OS === 'web') {
        return {
          success: false,
          error: 'Calendar integration is not supported on web platform'
        };
      }

      await Calendar.deleteEventAsync(eventId);

      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete calendar event'
      };
    }
  }

  /**
   * Check if calendar integration is supported on current platform
   */
  isSupported(): boolean {
    return Platform.OS !== 'web';
  }
}

export const calendarService = new CalendarService();
