// In-App Purchase Service for Google Play Store and Apple App Store
// Handles premium subscriptions and grace day purchases

import { Platform } from 'react-native';
import { PaymentType, PaymentProvider, IN_APP_PRODUCTS } from '../config/payments';
import {
  InAppPurchaseData,
  InAppPurchaseResult,
  SubscriptionPurchaseData,
  GraceDayPurchaseData,
} from '../../shared/types/payment';

// Types for in-app purchase libraries (will be imported dynamically)
interface Purchase {
  productId: string;
  transactionId: string;
  transactionDate: number;
  transactionReceipt: string;
  purchaseToken?: string; // Android
  originalTransactionId?: string; // iOS subscriptions
}

interface Product {
  productId: string;
  price: string;
  currency: string;
  title: string;
  description: string;
  type: 'inapp' | 'subs';
}

interface PurchaseError {
  code: string;
  message: string;
  debugMessage?: string;
}

class InAppPurchaseService {
  private isInitialized = false;
  private availableProducts: Product[] = [];
  private purchaseUpdateSubscription: any = null;
  private purchaseErrorSubscription: any = null;

  /**
   * Initialize the in-app purchase service
   */
  async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) {
        return true;
      }

      if (Platform.OS === 'ios') {
        return await this.initializeIOS();
      } else if (Platform.OS === 'android') {
        return await this.initializeAndroid();
      } else {
        console.warn('In-app purchases not supported on web platform');
        return false;
      }
    } catch (error) {
      console.error('Failed to initialize in-app purchase service:', error);
      return false;
    }
  }

  /**
   * Initialize iOS in-app purchases
   */
  private async initializeIOS(): Promise<boolean> {
    try {
      // Dynamically import react-native-iap for iOS
      const RNIap = await import('react-native-iap');
      
      // Initialize connection
      await RNIap.initConnection();
      
      // Set up purchase listeners
      this.purchaseUpdateSubscription = RNIap.purchaseUpdatedListener((purchase: Purchase) => {
        this.handlePurchaseUpdate(purchase);
      });

      this.purchaseErrorSubscription = RNIap.purchaseErrorListener((error: PurchaseError) => {
        this.handlePurchaseError(error);
      });

      // Load available products
      await this.loadProducts();

      this.isInitialized = true;
      console.log('iOS in-app purchase service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize iOS in-app purchases:', error);
      return false;
    }
  }

  /**
   * Initialize Android in-app purchases
   */
  private async initializeAndroid(): Promise<boolean> {
    try {
      // Dynamically import react-native-iap for Android
      const RNIap = await import('react-native-iap');
      
      // Initialize connection
      await RNIap.initConnection();
      
      // Set up purchase listeners
      this.purchaseUpdateSubscription = RNIap.purchaseUpdatedListener((purchase: Purchase) => {
        this.handlePurchaseUpdate(purchase);
      });

      this.purchaseErrorSubscription = RNIap.purchaseErrorListener((error: PurchaseError) => {
        this.handlePurchaseError(error);
      });

      // Load available products
      await this.loadProducts();

      this.isInitialized = true;
      console.log('Android in-app purchase service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize Android in-app purchases:', error);
      return false;
    }
  }

  /**
   * Load available products from the app stores
   */
  private async loadProducts(): Promise<void> {
    try {
      const RNIap = await import('react-native-iap');
      
      const productIds = Object.values(IN_APP_PRODUCTS);
      const subscriptionIds = [IN_APP_PRODUCTS.PREMIUM_MONTHLY, IN_APP_PRODUCTS.PREMIUM_YEARLY];
      const consumableIds = [
        IN_APP_PRODUCTS.GRACE_DAY_SINGLE,
        IN_APP_PRODUCTS.GRACE_DAY_PACK_3,
        IN_APP_PRODUCTS.GRACE_DAY_PACK_5,
      ];

      // Load subscriptions
      const subscriptions = await RNIap.getSubscriptions({ skus: subscriptionIds });
      
      // Load consumable products
      const products = await RNIap.getProducts({ skus: consumableIds });

      this.availableProducts = [...subscriptions, ...products];
      console.log('Loaded products:', this.availableProducts.length);
    } catch (error) {
      console.error('Failed to load products:', error);
    }
  }

  /**
   * Process premium subscription purchase
   */
  async processPremiumSubscription(data: SubscriptionPurchaseData): Promise<InAppPurchaseResult> {
    try {
      if (!this.isInitialized) {
        const initialized = await this.initialize();
        if (!initialized) {
          return {
            success: false,
            error: 'In-app purchase service not available',
            provider: Platform.OS === 'ios' ? PaymentProvider.APPLE_APP_STORE : PaymentProvider.GOOGLE_PLAY,
          };
        }
      }

      const RNIap = await import('react-native-iap');
      
      const productId = data.subscriptionPlan === 'monthly' 
        ? IN_APP_PRODUCTS.PREMIUM_MONTHLY 
        : IN_APP_PRODUCTS.PREMIUM_YEARLY;

      // Request subscription purchase
      const purchase = await RNIap.requestSubscription({ sku: productId });

      return {
        success: true,
        provider: Platform.OS === 'ios' ? PaymentProvider.APPLE_APP_STORE : PaymentProvider.GOOGLE_PLAY,
        transactionId: purchase.transactionId,
        purchaseToken: purchase.purchaseToken,
        receipt: purchase.transactionReceipt,
        originalTransactionId: purchase.originalTransactionId,
      };
    } catch (error) {
      console.error('Premium subscription purchase failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Subscription purchase failed',
        provider: Platform.OS === 'ios' ? PaymentProvider.APPLE_APP_STORE : PaymentProvider.GOOGLE_PLAY,
      };
    }
  }

  /**
   * Process grace day purchase
   */
  async processGraceDayPurchase(data: GraceDayPurchaseData): Promise<InAppPurchaseResult> {
    try {
      if (!this.isInitialized) {
        const initialized = await this.initialize();
        if (!initialized) {
          return {
            success: false,
            error: 'In-app purchase service not available',
            provider: Platform.OS === 'ios' ? PaymentProvider.APPLE_APP_STORE : PaymentProvider.GOOGLE_PLAY,
          };
        }
      }

      const RNIap = await import('react-native-iap');
      
      // Determine product ID based on quantity
      let productId: string;
      switch (data.quantity) {
        case 1:
          productId = IN_APP_PRODUCTS.GRACE_DAY_SINGLE;
          break;
        case 3:
          productId = IN_APP_PRODUCTS.GRACE_DAY_PACK_3;
          break;
        case 5:
          productId = IN_APP_PRODUCTS.GRACE_DAY_PACK_5;
          break;
        default:
          productId = IN_APP_PRODUCTS.GRACE_DAY_SINGLE;
      }

      // Request product purchase
      const purchase = await RNIap.requestPurchase({ sku: productId });

      // Acknowledge/consume the purchase (required for consumables)
      if (Platform.OS === 'android') {
        await RNIap.acknowledgePurchaseAndroid({ token: purchase.purchaseToken!, developerPayload: '' });
      } else {
        await RNIap.finishTransaction({ purchase });
      }

      return {
        success: true,
        provider: Platform.OS === 'ios' ? PaymentProvider.APPLE_APP_STORE : PaymentProvider.GOOGLE_PLAY,
        transactionId: purchase.transactionId,
        purchaseToken: purchase.purchaseToken,
        receipt: purchase.transactionReceipt,
      };
    } catch (error) {
      console.error('Grace day purchase failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Grace day purchase failed',
        provider: Platform.OS === 'ios' ? PaymentProvider.APPLE_APP_STORE : PaymentProvider.GOOGLE_PLAY,
      };
    }
  }

  /**
   * Get available products
   */
  getAvailableProducts(): Product[] {
    return this.availableProducts;
  }

  /**
   * Handle purchase updates
   */
  private handlePurchaseUpdate(purchase: Purchase): void {
    console.log('Purchase updated:', purchase);
    // Handle purchase verification and fulfillment here
  }

  /**
   * Handle purchase errors
   */
  private handlePurchaseError(error: PurchaseError): void {
    console.error('Purchase error:', error);
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.purchaseUpdateSubscription) {
        this.purchaseUpdateSubscription.remove();
        this.purchaseUpdateSubscription = null;
      }

      if (this.purchaseErrorSubscription) {
        this.purchaseErrorSubscription.remove();
        this.purchaseErrorSubscription = null;
      }

      if (this.isInitialized) {
        const RNIap = await import('react-native-iap');
        await RNIap.endConnection();
        this.isInitialized = false;
      }
    } catch (error) {
      console.error('Failed to cleanup in-app purchase service:', error);
    }
  }
}

// Export singleton instance
export const inAppPurchaseService = new InAppPurchaseService();
