// Payment Error Handler Service
// Provides comprehensive error handling and fallback mechanisms for payment failures

import { Platform } from 'react-native';
import { PaymentType, PaymentProvider } from '../config/payments';
import {
  PaymentData,
  PaymentResult,
  isStripePaymentData,
  isInAppPurchaseData,
} from '../../shared/types/payment';

export enum PaymentErrorType {
  NETWORK_ERROR = 'network_error',
  PAYMENT_DECLINED = 'payment_declined',
  INSUFFICIENT_FUNDS = 'insufficient_funds',
  INVALID_PAYMENT_METHOD = 'invalid_payment_method',
  SUBSCRIPTION_ERROR = 'subscription_error',
  IN_APP_PURCHASE_ERROR = 'in_app_purchase_error',
  STRIPE_ERROR = 'stripe_error',
  USER_CANCELLED = 'user_cancelled',
  CONFIGURATION_ERROR = 'configuration_error',
  UNKNOWN_ERROR = 'unknown_error',
}

export interface PaymentError {
  type: PaymentErrorType;
  message: string;
  originalError?: any;
  provider: PaymentProvider;
  paymentType: PaymentType;
  retryable: boolean;
  suggestedAction?: string;
}

export interface PaymentRetryOptions {
  maxRetries: number;
  retryDelay: number; // milliseconds
  exponentialBackoff: boolean;
  fallbackProvider?: PaymentProvider;
}

class PaymentErrorHandler {
  private readonly defaultRetryOptions: PaymentRetryOptions = {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    fallbackProvider: PaymentProvider.STRIPE,
  };

  /**
   * Parse and categorize payment errors
   */
  parsePaymentError(
    error: any,
    provider: PaymentProvider,
    paymentType: PaymentType
  ): PaymentError {
    // Handle Stripe errors
    if (provider === PaymentProvider.STRIPE) {
      return this.parseStripeError(error, paymentType);
    }

    // Handle In-App Purchase errors
    if (provider === PaymentProvider.GOOGLE_PLAY || provider === PaymentProvider.APPLE_APP_STORE) {
      return this.parseInAppPurchaseError(error, provider, paymentType);
    }

    // Generic error handling
    return this.parseGenericError(error, provider, paymentType);
  }

  /**
   * Parse Stripe-specific errors
   */
  private parseStripeError(error: any, paymentType: PaymentType): PaymentError {
    const baseError: Partial<PaymentError> = {
      provider: PaymentProvider.STRIPE,
      paymentType,
    };

    if (error?.code) {
      switch (error.code) {
        case 'card_declined':
          return {
            ...baseError,
            type: PaymentErrorType.PAYMENT_DECLINED,
            message: 'Your card was declined. Please try a different payment method.',
            retryable: false,
            suggestedAction: 'Try a different card or payment method',
          } as PaymentError;

        case 'insufficient_funds':
          return {
            ...baseError,
            type: PaymentErrorType.INSUFFICIENT_FUNDS,
            message: 'Insufficient funds. Please check your account balance.',
            retryable: false,
            suggestedAction: 'Add funds to your account or use a different payment method',
          } as PaymentError;

        case 'expired_card':
        case 'invalid_expiry_month':
        case 'invalid_expiry_year':
        case 'invalid_cvc':
          return {
            ...baseError,
            type: PaymentErrorType.INVALID_PAYMENT_METHOD,
            message: 'Invalid card details. Please check your card information.',
            retryable: false,
            suggestedAction: 'Update your card details or use a different card',
          } as PaymentError;

        case 'processing_error':
        case 'issuer_not_available':
          return {
            ...baseError,
            type: PaymentErrorType.NETWORK_ERROR,
            message: 'Payment processing temporarily unavailable. Please try again.',
            retryable: true,
            suggestedAction: 'Try again in a few moments',
          } as PaymentError;

        default:
          return {
            ...baseError,
            type: PaymentErrorType.STRIPE_ERROR,
            message: error.message || 'Payment failed. Please try again.',
            retryable: true,
            originalError: error,
          } as PaymentError;
      }
    }

    return {
      ...baseError,
      type: PaymentErrorType.STRIPE_ERROR,
      message: error.message || 'Payment processing failed',
      retryable: true,
      originalError: error,
    } as PaymentError;
  }

  /**
   * Parse In-App Purchase errors
   */
  private parseInAppPurchaseError(
    error: any,
    provider: PaymentProvider,
    paymentType: PaymentType
  ): PaymentError {
    const baseError: Partial<PaymentError> = {
      provider,
      paymentType,
    };

    if (error?.code) {
      switch (error.code) {
        case 'E_USER_CANCELLED':
        case 'UserCancel':
          return {
            ...baseError,
            type: PaymentErrorType.USER_CANCELLED,
            message: 'Purchase was cancelled by user.',
            retryable: false,
          } as PaymentError;

        case 'E_NETWORK_ERROR':
        case 'NetworkError':
          return {
            ...baseError,
            type: PaymentErrorType.NETWORK_ERROR,
            message: 'Network error. Please check your connection and try again.',
            retryable: true,
            suggestedAction: 'Check your internet connection and try again',
          } as PaymentError;

        case 'E_SERVICE_ERROR':
        case 'ServiceUnavailable':
          return {
            ...baseError,
            type: PaymentErrorType.IN_APP_PURCHASE_ERROR,
            message: `${provider === PaymentProvider.APPLE_APP_STORE ? 'App Store' : 'Google Play'} is temporarily unavailable.`,
            retryable: true,
            suggestedAction: 'Try again in a few moments',
          } as PaymentError;

        case 'E_ITEM_UNAVAILABLE':
        case 'ProductNotAvailable':
          return {
            ...baseError,
            type: PaymentErrorType.CONFIGURATION_ERROR,
            message: 'This subscription is not available. Please contact support.',
            retryable: false,
            suggestedAction: 'Contact customer support',
          } as PaymentError;

        default:
          return {
            ...baseError,
            type: PaymentErrorType.IN_APP_PURCHASE_ERROR,
            message: error.message || 'In-app purchase failed',
            retryable: true,
            originalError: error,
          } as PaymentError;
      }
    }

    return {
      ...baseError,
      type: PaymentErrorType.IN_APP_PURCHASE_ERROR,
      message: error.message || 'In-app purchase failed',
      retryable: true,
      originalError: error,
    } as PaymentError;
  }

  /**
   * Parse generic errors
   */
  private parseGenericError(
    error: any,
    provider: PaymentProvider,
    paymentType: PaymentType
  ): PaymentError {
    return {
      type: PaymentErrorType.UNKNOWN_ERROR,
      message: error.message || 'An unexpected error occurred',
      provider,
      paymentType,
      retryable: true,
      originalError: error,
    };
  }

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage(error: PaymentError): string {
    switch (error.type) {
      case PaymentErrorType.NETWORK_ERROR:
        return 'Connection issue. Please check your internet and try again.';
      
      case PaymentErrorType.PAYMENT_DECLINED:
        return 'Payment was declined. Please try a different payment method.';
      
      case PaymentErrorType.INSUFFICIENT_FUNDS:
        return 'Insufficient funds. Please add money to your account or use a different card.';
      
      case PaymentErrorType.INVALID_PAYMENT_METHOD:
        return 'Invalid payment details. Please check your information and try again.';
      
      case PaymentErrorType.USER_CANCELLED:
        return 'Payment was cancelled.';
      
      case PaymentErrorType.SUBSCRIPTION_ERROR:
        return 'Subscription error. Please try again or contact support.';
      
      case PaymentErrorType.CONFIGURATION_ERROR:
        return 'Service configuration error. Please contact support.';
      
      default:
        return error.message || 'Payment failed. Please try again.';
    }
  }

  /**
   * Get suggested actions for error recovery
   */
  getSuggestedActions(error: PaymentError): string[] {
    const actions: string[] = [];

    switch (error.type) {
      case PaymentErrorType.NETWORK_ERROR:
        actions.push('Check your internet connection');
        actions.push('Try again in a few moments');
        break;
      
      case PaymentErrorType.PAYMENT_DECLINED:
      case PaymentErrorType.INSUFFICIENT_FUNDS:
        actions.push('Try a different payment method');
        actions.push('Contact your bank if the issue persists');
        break;
      
      case PaymentErrorType.INVALID_PAYMENT_METHOD:
        actions.push('Check your card details');
        actions.push('Try a different card');
        break;
      
      case PaymentErrorType.SUBSCRIPTION_ERROR:
      case PaymentErrorType.IN_APP_PURCHASE_ERROR:
        actions.push('Try again in a few moments');
        if (Platform.OS === 'ios') {
          actions.push('Check your Apple ID payment settings');
        } else if (Platform.OS === 'android') {
          actions.push('Check your Google Play payment settings');
        }
        break;
      
      case PaymentErrorType.CONFIGURATION_ERROR:
        actions.push('Contact customer support');
        break;
      
      default:
        if (error.retryable) {
          actions.push('Try again');
        }
        actions.push('Contact support if the problem continues');
    }

    return actions;
  }

  /**
   * Determine if error should trigger fallback payment method
   */
  shouldUseFallback(error: PaymentError): boolean {
    const fallbackTriggers = [
      PaymentErrorType.IN_APP_PURCHASE_ERROR,
      PaymentErrorType.SUBSCRIPTION_ERROR,
      PaymentErrorType.CONFIGURATION_ERROR,
    ];

    return fallbackTriggers.includes(error.type) && error.retryable;
  }

  /**
   * Get fallback payment provider
   */
  getFallbackProvider(
    originalProvider: PaymentProvider,
    paymentType: PaymentType
  ): PaymentProvider | null {
    // For premium subscriptions and grace days, fallback to Stripe if app store fails
    if (
      (paymentType === PaymentType.PREMIUM_SUBSCRIPTION || paymentType === PaymentType.GRACE_DAY) &&
      (originalProvider === PaymentProvider.APPLE_APP_STORE || originalProvider === PaymentProvider.GOOGLE_PLAY)
    ) {
      return PaymentProvider.STRIPE;
    }

    // For commits and pools, no fallback (Stripe only)
    return null;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempt: number, options: PaymentRetryOptions): number {
    if (!options.exponentialBackoff) {
      return options.retryDelay;
    }

    return options.retryDelay * Math.pow(2, attempt - 1);
  }

  /**
   * Check if error is retryable
   */
  isRetryable(error: PaymentError): boolean {
    return error.retryable && error.type !== PaymentErrorType.USER_CANCELLED;
  }

  /**
   * Log payment error for analytics
   */
  logPaymentError(error: PaymentError, paymentData: PaymentData): void {
    console.error('Payment Error:', {
      type: error.type,
      provider: error.provider,
      paymentType: error.paymentType,
      message: error.message,
      retryable: error.retryable,
      amount: paymentData.amount,
      currency: paymentData.currency,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });

    // TODO: Send to analytics service
  }
}

// Export singleton instance
export const paymentErrorHandler = new PaymentErrorHandler();
