/**
 * Commit Scheduling Service
 * Frontend service for managing commit scheduling operations
 */

import { getFunctions, httpsCallable } from 'firebase/functions';

export interface CommitSchedulingConfig {
  commitId: string;
  userId: string;
  timezone: string;
  startDate: Date;
  endDate?: Date;
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
  deadlineType: 'before' | 'after' | 'between' | 'midnight';
  deadlineTime?: string; // HH:MM format
  deadlineEndTime?: string; // For 'between' type
  autoVerificationEnabled: boolean;
  verificationType: string;
  notificationPreferences?: {
    reminderTime: string;
    urgentRemindersEnabled: boolean;
  };
}

export interface CommitSchedulingResult {
  success: boolean;
  message: string;
  commitId: string;
  userId: string;
  jobsCreated?: number;
  jobsDeleted?: number;
  errors?: string[];
}

export interface CommitSchedulingStatus {
  isScheduled: boolean;
  nextVerificationTime?: string;
  lastVerificationTime?: string;
  schedulerJobIds: string[];
  autoVerificationEnabled: boolean;
}

/**
 * Service class for managing commit scheduling
 */
export class CommitSchedulingService {
  private functions = getFunctions();

  /**
   * Set up commit scheduling when a commit is created
   */
  async setupCommitScheduling(config: CommitSchedulingConfig): Promise<CommitSchedulingResult> {
    try {
      const setupScheduling = httpsCallable(this.functions, 'setupCommitScheduling');
      const result = await setupScheduling({
        commitId: config.commitId,
        userId: config.userId,
        schedulingConfig: {
          ...config,
          startDate: config.startDate.toISOString(),
          endDate: config.endDate?.toISOString()
        }
      });
      
      return {
        success: true,
        message: 'Commit scheduling set up successfully',
        commitId: config.commitId,
        userId: config.userId,
        ...(result.data as any)
      };
    } catch (error) {
      console.error('Error setting up commit scheduling:', error);
      return {
        success: false,
        message: `Failed to set up commit scheduling: ${error}`,
        commitId: config.commitId,
        userId: config.userId
      };
    }
  }

  /**
   * Delete commit scheduling when a commit is cancelled or completed
   */
  async deleteCommitScheduling(commitId: string, userId: string): Promise<CommitSchedulingResult> {
    try {
      const deleteScheduling = httpsCallable(this.functions, 'deleteCommitScheduling');
      const result = await deleteScheduling({ commitId, userId });
      
      return {
        success: true,
        message: 'Commit scheduling deleted successfully',
        commitId,
        userId,
        ...(result.data as any)
      };
    } catch (error) {
      console.error('Error deleting commit scheduling:', error);
      return {
        success: false,
        message: `Failed to delete commit scheduling: ${error}`,
        commitId,
        userId
      };
    }
  }

  /**
   * Update commit scheduling configuration
   */
  async updateCommitScheduling(
    commitId: string, 
    userId: string, 
    newConfig: Partial<CommitSchedulingConfig>
  ): Promise<CommitSchedulingResult> {
    try {
      const updateScheduling = httpsCallable(this.functions, 'updateCommitScheduling');
      const result = await updateScheduling({
        commitId,
        userId,
        newConfig: {
          ...newConfig,
          startDate: newConfig.startDate?.toISOString(),
          endDate: newConfig.endDate?.toISOString()
        }
      });
      
      return {
        success: true,
        message: 'Commit scheduling updated successfully',
        commitId,
        userId,
        ...(result.data as any)
      };
    } catch (error) {
      console.error('Error updating commit scheduling:', error);
      return {
        success: false,
        message: `Failed to update commit scheduling: ${error}`,
        commitId,
        userId
      };
    }
  }

  /**
   * Get commit scheduling status
   */
  async getCommitSchedulingStatus(commitId: string, userId: string): Promise<CommitSchedulingStatus | null> {
    try {
      // Get commit data from Firestore to check scheduling status
      const { getFirestore, doc, getDoc } = await import('firebase/firestore');
      const db = getFirestore();

      const commitDoc = await getDoc(doc(db, 'commits', commitId));
      if (!commitDoc.exists()) {
        return null;
      }

      const commitData = commitDoc.data();
      const scheduling = commitData.scheduling || {};

      return {
        isScheduled: scheduling.enabled || false,
        nextVerificationTime: scheduling.nextVerificationTime,
        lastVerificationTime: scheduling.lastVerificationTime,
        schedulerJobIds: scheduling.schedulerJobIds || [],
        autoVerificationEnabled: commitData.verification?.autoVerificationEnabled || false
      };
    } catch (error) {
      console.error('Error getting commit scheduling status:', error);
      return null;
    }
  }

  /**
   * Pause commit scheduling
   */
  async pauseCommitScheduling(commitId: string, userId: string): Promise<CommitSchedulingResult> {
    try {
      // This would call a cloud function to pause scheduling
      // For now, we'll use delete as a placeholder
      return await this.deleteCommitScheduling(commitId, userId);
    } catch (error) {
      console.error('Error pausing commit scheduling:', error);
      return {
        success: false,
        message: `Failed to pause commit scheduling: ${error}`,
        commitId,
        userId
      };
    }
  }

  /**
   * Resume commit scheduling
   */
  async resumeCommitScheduling(config: CommitSchedulingConfig): Promise<CommitSchedulingResult> {
    try {
      // This would call a cloud function to resume scheduling
      // For now, we'll use setup as a placeholder
      return await this.setupCommitScheduling(config);
    } catch (error) {
      console.error('Error resuming commit scheduling:', error);
      return {
        success: false,
        message: `Failed to resume commit scheduling: ${error}`,
        commitId: config.commitId,
        userId: config.userId
      };
    }
  }

  /**
   * Trigger manual verification for a commit
   */
  async triggerManualVerification(
    commitId: string, 
    userId: string, 
    verificationType: string,
    verificationData?: any
  ): Promise<{ success: boolean; message: string; verified?: boolean }> {
    try {
      // This would call a cloud function to trigger manual verification
      // For now, return a placeholder
      return {
        success: true,
        message: 'Manual verification triggered successfully',
        verified: true
      };
    } catch (error) {
      console.error('Error triggering manual verification:', error);
      return {
        success: false,
        message: `Failed to trigger manual verification: ${error}`
      };
    }
  }

  /**
   * Get commit verification history
   */
  async getVerificationHistory(commitId: string, userId: string): Promise<any[]> {
    try {
      // This would call a cloud function to get verification history
      // For now, return empty array
      return [];
    } catch (error) {
      console.error('Error getting verification history:', error);
      return [];
    }
  }

  /**
   * Update notification preferences for a commit
   */
  async updateNotificationPreferences(
    commitId: string,
    userId: string,
    preferences: {
      reminderTime: string;
      urgentRemindersEnabled: boolean;
    }
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Update the commit document with new notification preferences
      const { getFirestore, doc, updateDoc } = await import('firebase/firestore');
      const db = getFirestore();

      await updateDoc(doc(db, 'commits', commitId), {
        'notificationPreferences': preferences,
        'notificationPreferences.updatedAt': new Date().toISOString()
      });

      // Also update the user's commit document
      await updateDoc(doc(db, 'users', userId, 'commits', commitId), {
        'notificationPreferences': preferences,
        'notificationPreferences.updatedAt': new Date().toISOString()
      });

      // If this was a real implementation, we would also call a cloud function
      // to update the actual scheduler jobs with new timing
      // const updateScheduling = httpsCallable(this.functions, 'updateCommitScheduling');
      // await updateScheduling({ commitId, userId, newConfig: { notificationPreferences: preferences } });

      return {
        success: true,
        message: 'Notification preferences updated successfully'
      };
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      return {
        success: false,
        message: `Failed to update notification preferences: ${error}`
      };
    }
  }

  /**
   * Calculate next deadline for a commit
   */
  calculateNextDeadline(
    deadlineType: 'before' | 'after' | 'between' | 'midnight',
    deadlineTime?: string,
    deadlineEndTime?: string,
    timezone: string = 'UTC'
  ): Date {
    const now = new Date();
    const userNow = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
    
    let deadline: Date;
    
    switch (deadlineType) {
      case 'before':
        if (deadlineTime) {
          const [hour, minute] = deadlineTime.split(':').map(Number);
          deadline = new Date(userNow);
          deadline.setHours(hour, minute, 0, 0);
          
          // If time has passed today, set for tomorrow
          if (deadline <= userNow) {
            deadline.setDate(deadline.getDate() + 1);
          }
        } else {
          deadline = new Date(userNow);
          deadline.setHours(23, 59, 59, 999);
        }
        break;
        
      case 'after':
        if (deadlineTime) {
          const [hour, minute] = deadlineTime.split(':').map(Number);
          deadline = new Date(userNow);
          deadline.setHours(hour, minute, 0, 0);
          deadline.setDate(deadline.getDate() + 1); // Next day
        } else {
          deadline = new Date(userNow);
          deadline.setHours(23, 59, 59, 999);
        }
        break;
        
      case 'between':
        if (deadlineEndTime) {
          const [hour, minute] = deadlineEndTime.split(':').map(Number);
          deadline = new Date(userNow);
          deadline.setHours(hour, minute, 0, 0);
          
          if (deadline <= userNow) {
            deadline.setDate(deadline.getDate() + 1);
          }
        } else {
          deadline = new Date(userNow);
          deadline.setHours(23, 59, 59, 999);
        }
        break;
        
      case 'midnight':
      default:
        deadline = new Date(userNow);
        deadline.setHours(23, 59, 59, 999);
        break;
    }
    
    return deadline;
  }

  /**
   * Format time until deadline for display
   */
  formatTimeUntilDeadline(deadline: Date): string {
    const now = new Date();
    const diff = deadline.getTime() - now.getTime();
    
    if (diff <= 0) {
      return "Deadline passed";
    }
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days} day${days === 1 ? '' : 's'} remaining`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else {
      return `${minutes}m remaining`;
    }
  }
}

// Export singleton instance
export const commitSchedulingService = new CommitSchedulingService();

/**
 * Utility functions for direct use
 */

/**
 * Set up commit scheduling when a commit is created
 */
export const setupCommitScheduling = async (config: CommitSchedulingConfig): Promise<boolean> => {
  try {
    const result = await commitSchedulingService.setupCommitScheduling(config);
    return result.success;
  } catch (error) {
    console.error('Error setting up commit scheduling:', error);
    return false;
  }
};

/**
 * Delete commit scheduling when a commit is cancelled
 */
export const deleteCommitScheduling = async (commitId: string, userId: string): Promise<boolean> => {
  try {
    const result = await commitSchedulingService.deleteCommitScheduling(commitId, userId);
    return result.success;
  } catch (error) {
    console.error('Error deleting commit scheduling:', error);
    return false;
  }
};

/**
 * Calculate next deadline for display
 */
export const getNextDeadline = (
  deadlineType: 'before' | 'after' | 'between' | 'midnight',
  deadlineTime?: string,
  deadlineEndTime?: string,
  timezone: string = 'UTC'
): Date => {
  return commitSchedulingService.calculateNextDeadline(deadlineType, deadlineTime, deadlineEndTime, timezone);
};
