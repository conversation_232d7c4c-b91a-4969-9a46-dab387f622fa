// Grace Day Purchase Service
// Handles grace day purchases using platform-specific payments

import { Platform } from 'react-native';
import { PaymentType, PaymentProvider } from '../config/payments';
import {
  GraceDayPurchaseData,
  PaymentResult,
  isInAppPurchaseResult,
} from '../../shared/types/payment';
import { paymentRouter } from './paymentRouter';
import { firestoreService } from './database';
import { getId } from '../utils/variables';
import { GRACE_DAY_PRICING } from '../../shared/constants/premiumFeatures';
import { IN_APP_PURCHASE_CONFIG } from '../config/payments';

export interface GraceDayPurchaseOptions {
  programId: string;
  quantity: number;
  userId?: string;
}

export interface GraceDayPurchaseResult {
  success: boolean;
  error?: string;
  transactionId?: string;
  provider?: PaymentProvider;
  graceDaysAdded?: number;
}

class GraceDayPurchaseService {
  /**
   * Purchase grace days using platform-specific payment
   */
  async purchaseGraceDays(options: GraceDayPurchaseOptions): Promise<GraceDayPurchaseResult> {
    try {
      const userId = options.userId || await getId();
      if (!userId) {
        return {
          success: false,
          error: 'User ID is required for grace day purchase',
        };
      }

      // Validate quantity
      if (options.quantity < 1 || options.quantity > GRACE_DAY_PRICING.maxPerProgram) {
        return {
          success: false,
          error: `Invalid quantity. Must be between 1 and ${GRACE_DAY_PRICING.maxPerProgram}`,
        };
      }

      // Check if user can purchase more grace days for this program
      const canPurchase = await this.canPurchaseGraceDays(userId, options.programId, options.quantity);
      if (!canPurchase.allowed) {
        return {
          success: false,
          error: canPurchase.reason,
        };
      }

      // Determine product ID and pricing based on quantity
      const { productId, totalPrice } = this.getProductDetails(options.quantity);

      // Create grace day purchase data
      const graceDayData: GraceDayPurchaseData = {
        amount: totalPrice * 100, // Convert to cents
        currency: GRACE_DAY_PRICING.currency.toLowerCase(),
        description: `${options.quantity} Grace Day${options.quantity > 1 ? 's' : ''} for program`,
        userId,
        paymentType: PaymentType.GRACE_DAY,
        productId,
        programId: options.programId,
        quantity: options.quantity,
      };

      console.log('Processing grace day purchase:', {
        programId: options.programId,
        quantity: options.quantity,
        totalPrice,
        productId,
        platform: Platform.OS,
      });

      // Process payment through router
      const paymentResult = await paymentRouter.processPayment(graceDayData);

      if (!paymentResult.success) {
        return {
          success: false,
          error: paymentResult.error || 'Grace day purchase failed',
          provider: paymentResult.provider,
        };
      }

      // Handle successful payment - add grace days to user's program
      const graceDaysAdded = await this.addGraceDaysToProgram(
        userId,
        options.programId,
        options.quantity,
        paymentResult
      );

      if (graceDaysAdded === 0) {
        console.error('Failed to add grace days after successful payment');
        // Note: Payment was successful but database update failed
        // This should be handled by webhook or retry mechanism
      }

      return {
        success: true,
        transactionId: this.extractTransactionId(paymentResult),
        provider: paymentResult.provider,
        graceDaysAdded,
      };

    } catch (error) {
      console.error('Grace day purchase failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Check if user can purchase grace days for a program
   */
  async canPurchaseGraceDays(
    userId: string,
    programId: string,
    quantity: number
  ): Promise<{ allowed: boolean; reason?: string }> {
    try {
      // Get participant data
      const participantResult = await firestoreService.participants.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return {
          allowed: false,
          reason: 'You are not enrolled in this program',
        };
      }

      const participant = participantResult.data;
      
      // Check if user has already purchased maximum grace days
      const currentGraceDays = participant.livesPurchaseLeft || 0;
      const totalAfterPurchase = currentGraceDays + quantity;
      
      if (totalAfterPurchase > GRACE_DAY_PRICING.maxPerProgram) {
        return {
          allowed: false,
          reason: `Cannot exceed ${GRACE_DAY_PRICING.maxPerProgram} grace days per program. You currently have ${currentGraceDays}.`,
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error checking grace day purchase eligibility:', error);
      return {
        allowed: false,
        reason: 'Unable to verify purchase eligibility',
      };
    }
  }

  /**
   * Get product details based on quantity
   */
  private getProductDetails(quantity: number): { productId: string; totalPrice: number } {
    switch (quantity) {
      case 1:
        return {
          productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.id,
          totalPrice: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.price,
        };
      case 3:
        return {
          productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.id,
          totalPrice: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.price,
        };
      case 5:
        return {
          productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.id,
          totalPrice: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.price,
        };
      default:
        // For other quantities, use single grace day pricing
        return {
          productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.id,
          totalPrice: quantity * GRACE_DAY_PRICING.amount,
        };
    }
  }

  /**
   * Add grace days to user's program after successful payment
   */
  private async addGraceDaysToProgram(
    userId: string,
    programId: string,
    quantity: number,
    paymentResult: PaymentResult
  ): Promise<number> {
    try {
      // Use existing service to purchase lives (grace days)
      const result = await firestoreService.participants.purchaseLives(programId, userId, quantity);
      
      if (result.success) {
        console.log(`Successfully added ${quantity} grace days to program ${programId} for user ${userId}`);
        return quantity;
      } else {
        console.error('Failed to add grace days:', result.error);
        return 0;
      }
    } catch (error) {
      console.error('Error adding grace days to program:', error);
      return 0;
    }
  }

  /**
   * Extract transaction ID from payment result
   */
  private extractTransactionId(paymentResult: PaymentResult): string {
    if (isInAppPurchaseResult(paymentResult)) {
      return paymentResult.transactionId || 'unknown';
    } else {
      return paymentResult.paymentIntentId || 'unknown';
    }
  }

  /**
   * Get available grace day packages
   */
  getAvailableGraceDayPackages() {
    return [
      {
        id: 'single',
        quantity: 1,
        price: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.price,
        currency: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.currency,
        title: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.title,
        description: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.description,
        productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_SINGLE.id,
        savings: 0,
      },
      {
        id: 'pack_3',
        quantity: 3,
        price: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.price,
        currency: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.currency,
        title: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.title,
        description: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.description,
        productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.id,
        savings: (3 * GRACE_DAY_PRICING.amount) - IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_3.price,
      },
      {
        id: 'pack_5',
        quantity: 5,
        price: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.price,
        currency: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.currency,
        title: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.title,
        description: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.description,
        productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.id,
        savings: (5 * GRACE_DAY_PRICING.amount) - IN_APP_PURCHASE_CONFIG.PRODUCTS.GRACE_DAY_PACK_5.price,
      },
    ];
  }

  /**
   * Get recommended payment provider for current platform
   */
  getRecommendedPaymentProvider(): PaymentProvider {
    if (Platform.OS === 'ios') {
      return PaymentProvider.APPLE_APP_STORE;
    } else if (Platform.OS === 'android') {
      return PaymentProvider.GOOGLE_PLAY;
    } else {
      return PaymentProvider.STRIPE;
    }
  }

  /**
   * Get grace day pricing information
   */
  getGraceDayPricing() {
    return {
      basePrice: GRACE_DAY_PRICING.amount,
      currency: GRACE_DAY_PRICING.currency,
      maxPerProgram: GRACE_DAY_PRICING.maxPerProgram,
      description: GRACE_DAY_PRICING.description,
    };
  }
}

// Export singleton instance
export const graceDayPurchaseService = new GraceDayPurchaseService();
