import AsyncStorage from '@react-native-async-storage/async-storage';
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { Platform } from 'react-native';
import { GITHUB_CONFIG } from "../config/github";
import { getFunctions, httpsCallable } from 'firebase/functions';
import { firestoreService } from "./database";
import { getId } from "../utils/variables";

/**
 * Unified GitHub Service
 *
 * This service provides GitHub OAuth authentication and API access.
 * It handles both web and mobile authentication flows, integrates with
 * Firestore for persistent storage, and provides methods for repository
 * and commit management.
 *
 * Features:
 * - Cross-platform OAuth authentication (web popup, mobile WebView/WebBrowser)
 * - Firestore integration for persistent token storage
 * - Repository and commit fetching
 * - Commit verification for habit tracking
 * - Automatic token refresh and sync time tracking
 */

// GitHub OAuth configuration with redirect URI
const OAUTH_CONFIG = {
  ...GITHUB_CONFIG,
  REDIRECT_URI: Platform.OS === 'web'
    ? 'https://githuboauthcallback-5zdo6ysy2a-uc.a.run.app'
    : Linking.createURL('github-auth'),
};

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'github_access_token',
  USER_DATA: 'github_user_data',
  REPOSITORIES: 'github_repositories',
};

export interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  private: boolean;
  html_url: string;
  updated_at: string;
  language?: string;
}

export interface GitHubCommit {
  sha: string;
  commit: {
    author: {
      name: string;
      email: string;
      date: string;
    };
    message: string;
  };
  html_url: string;
}

export class GitHubService {
  private static instance: GitHubService;
  private accessToken: string | null = null;

  private constructor() {}

  public static getInstance(): GitHubService {
    if (!GitHubService.instance) {
      GitHubService.instance = new GitHubService();
    }
    return GitHubService.instance;
  }

  // Initialize the service and load stored token
  public async initialize(): Promise<void> {
    try {
      // First try to load from local storage
      this.accessToken = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      // If no local token, try to load from Firestore
      if (!this.accessToken) {
        const userId = await getId();
        if (userId) {
          try {
            const integrationResult = await firestoreService.integrations.getIntegrationByProvider(userId, 'github');
            if (integrationResult.success && integrationResult.data && integrationResult.data.isActive) {
              this.accessToken = integrationResult.data.accessToken;
              // Store locally for faster access
              await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, this.accessToken);
              // Store user data locally if available
              if (integrationResult.data.user) {
                await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(integrationResult.data.user));
              }
            }
          } catch (firestoreError) {
            console.warn('Failed to load GitHub integration from Firestore:', firestoreError);
          }
        }
      }
    } catch (error) {
      console.error('Failed to initialize GitHub service:', error);
    }
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  // Start OAuth flow
  public async authenticate(): Promise<{ success: boolean; error?: string }> {
    try {
      const authUrl = this.buildAuthUrl();
      
      if (Platform.OS === 'web') {
        // For web, use popup window
        const result = await this.authenticateWeb(authUrl);
        return result;
      } else {
        // For mobile, use WebBrowser
        const result = await this.authenticateMobile(authUrl);
        return result;
      }
    } catch (error) {
      console.error('GitHub authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  // Build GitHub OAuth URL
  private buildAuthUrl(state?: string): string {
    const authState = state || Math.random().toString(36).substring(7);

    // Always use cloud function callback for WebView modal
    // Only use mobile redirect URI for WebBrowser fallback
    const redirectUri = 'https://githuboauthcallback-5zdo6ysy2a-uc.a.run.app';

    const params = new URLSearchParams({
      client_id: OAUTH_CONFIG.CLIENT_ID,
      redirect_uri: redirectUri,
      scope: OAUTH_CONFIG.SCOPES.join(' '),
      state: authState,
    });

    return `https://github.com/login/oauth/authorize?${params.toString()}`;
  }

  // Build GitHub OAuth URL for WebBrowser fallback (uses mobile redirect URI)
  private buildFallbackAuthUrl(state?: string): string {
    const authState = state || Math.random().toString(36).substring(7);

    const params = new URLSearchParams({
      client_id: OAUTH_CONFIG.CLIENT_ID,
      redirect_uri: OAUTH_CONFIG.REDIRECT_URI, // Use mobile redirect URI for fallback
      scope: OAUTH_CONFIG.SCOPES.join(' '),
      state: authState,
    });

    return `https://github.com/login/oauth/authorize?${params.toString()}`;
  }

  // Web authentication using popup and Cloud Function
  private async authenticateWeb(authUrl: string): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      const popup = window.open(authUrl, 'github-auth', 'width=600,height=700');

      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          resolve({ success: false, error: 'Authentication cancelled' });
        }
      }, 1000);

      // Listen for the success message from the Cloud Function callback page
      const handleMessage = async (event: MessageEvent) => {
        // Accept messages from the Cloud Function domain
        if (!event.origin.includes('githuboauthcallback-5zdo6ysy2a-uc.a.run.app') && event.origin !== window.location.origin) return;

        if (event.data.type === 'GITHUB_AUTH_SUCCESS') {
          clearInterval(checkClosed);
          popup?.close();
          window.removeEventListener('message', handleMessage);

          const { state, user } = event.data;

          // Retrieve the token from Cloud Function using the state
          const tokenResult = await this.retrieveTokenFromCloudFunction(state);
          if (tokenResult.success) {
            // Store user data
            await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
          }
          resolve(tokenResult);
        }
      };

      window.addEventListener('message', handleMessage);
    });
  }

  // Mobile authentication using WebView Modal or WebBrowser fallback
  private async authenticateMobile(authUrl: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Use the global modal function if available
      if ((global as any).openGitHubAuthModal) {
        const result = await (global as any).openGitHubAuthModal(authUrl);

        if (result.success && result.code) {
          // Exchange the authorization code directly for access token
          return await this.exchangeCodeForToken(result.code);
        } else {
          console.error('❌ Modal authentication failed:', result.error);
          return { success: false, error: result.error || 'Authentication cancelled' };
        }
      } else {
        console.warn('⚠️ GitHub auth modal not available, falling back to WebBrowser');
        // Fallback to WebBrowser if modal is not available
        // Build a new auth URL with mobile redirect URI for WebBrowser
        const fallbackAuthUrl = this.buildFallbackAuthUrl();

        const result = await WebBrowser.openAuthSessionAsync(
          fallbackAuthUrl,
          OAUTH_CONFIG.REDIRECT_URI,
          {
            showInRecents: true,
            createTask: false
          }
        );

        if (result.type === 'success' && result.url) {
          const url = new URL(result.url);
          const code = url.searchParams.get('code');

          if (code) {
            return await this.exchangeCodeForToken(code);
          }
        }

        return { success: false, error: 'Authentication cancelled or failed' };
      }
    } catch (error) {
      console.error('💥 Mobile authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  // Retrieve token from Cloud Function using state parameter
  private async retrieveTokenFromCloudFunction(state: string): Promise<{ success: boolean; error?: string }> {
    try {
      const functions = getFunctions();
      const getGitHubToken = httpsCallable(functions, 'getGitHubToken');

      const result = await getGitHubToken({ state });
      const data = result.data as any;

      if (data.success && data.accessToken) {
        // Save to Firestore first
        const userId = await getId();

        if (userId && data.user) {
          try {
            const firestoreResult = await firestoreService.integrations.saveGitHubIntegration(userId, {
              accessToken: data.accessToken,
              tokenType: data.tokenType || 'bearer',
              scope: data.scope,
              user: data.user,
            });

            if (!firestoreResult.success) {
              // Continue with local storage even if Firestore fails
              console.error('Failed to save GitHub integration to Firestore:', firestoreResult.error);
            }
          } catch (firestoreError) {
            console.error('💥 GitHubService: Exception saving GitHub integration to Firestore:', firestoreError);
            // Continue with local storage even if Firestore fails
          }
        } else {
          console.warn('⚠️ GitHubService: Missing user ID or user data, skipping Firestore save', {
            hasUserId: !!userId,
            hasUserData: !!data.user
          });
        }

        // Store locally after Firestore
        this.accessToken = data.accessToken;
        await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, this.accessToken!);

        // Store user data if provided
        if (data.user) {
          await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(data.user));
        }

        return { success: true };
      } else {
        return { success: false, error: 'Failed to retrieve access token from Cloud Function' };
      }
    } catch (error) {
      console.error('Cloud Function token retrieval error:', error);
      return { success: false, error: 'Failed to retrieve token from server' };
    }
  }

  // Exchange authorization code for access token (for mobile)
  private async exchangeCodeForToken(code: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch('https://github.com/login/oauth/access_token', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: OAUTH_CONFIG.CLIENT_ID,
          client_secret: OAUTH_CONFIG.CLIENT_SECRET,
          code,
        }),
      });

      const data = await response.json();

      if (data.access_token) {
        this.accessToken = data.access_token;

        // Fetch user data first
        const userData = await this.fetchUserDataFromAPI();

        // Save to Firestore first
        const userId = await getId();

        if (userId && userData) {
          try {
            const firestoreResult = await firestoreService.integrations.saveGitHubIntegration(userId, {
              accessToken: data.access_token,
              tokenType: data.token_type || 'bearer',
              scope: data.scope,
              user: userData,
            });

            if (!firestoreResult.success) {
              console.error('❌ GitHubService (mobile): Failed to save GitHub integration to Firestore:', firestoreResult.error);
              // Continue with local storage even if Firestore fails
            }
          } catch (firestoreError) {
            console.error('💥 GitHubService (mobile): Exception saving GitHub integration to Firestore:', firestoreError);
            // Continue with local storage even if Firestore fails
          }
        } else {
          console.warn('⚠️ GitHubService (mobile): Missing user ID or user data, skipping Firestore save', {
            hasUserId: !!userId,
            hasUserData: !!userData
          });
        }

        // Store locally after Firestore
        await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, this.accessToken!);

        // Store user data locally
        if (userData) {
          await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
        }

        return { success: true };
      } else {
        return { success: false, error: data.error_description || 'Failed to get access token' };
      }
    } catch (error) {
      console.error('Token exchange error:', error);
      return { success: false, error: 'Failed to exchange code for token' };
    }
  }

  // Fetch user data from GitHub API and return it
  private async fetchUserDataFromAPI(): Promise<GitHubUser | null> {
    if (!this.accessToken) return null;

    try {
      const response = await fetch('https://api.github.com/user', {
        headers: {
          'Authorization': `token ${this.accessToken}`,
          'Accept': 'application/vnd.github.v3+json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        return userData;
      }
      return null;
    } catch (error) {
      console.error('Failed to fetch user data:', error);
      return null;
    }
  }

  // Fetch user data from GitHub API and store locally
  private async fetchAndStoreUserData(): Promise<void> {
    const userData = await this.fetchUserDataFromAPI();
    if (userData) {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
    }
  }

  // Get stored user data
  public async getUserData(): Promise<GitHubUser | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to get user data:', error);
      return null;
    }
  }

  // Fetch user repositories
  public async getRepositories(): Promise<GitHubRepository[]> {
    if (!this.accessToken) {
      console.error('GitHub getRepositories: No access token available');
      throw new Error('Not authenticated');
    }

    try {
      const response = await fetch('https://api.github.com/user/repos?sort=updated&per_page=100', {
        headers: {
          'Authorization': `token ${this.accessToken}`,
          'Accept': 'application/vnd.github.v3+json',
        },
      });

      if (response.ok) {
        const repositories = await response.json();

        await AsyncStorage.setItem(STORAGE_KEYS.REPOSITORIES, JSON.stringify(repositories));
        return repositories;
      } else {
        const errorText = await response.text();
        console.error('GitHub getRepositories: API error:', response.status, errorText);
        throw new Error(`Failed to fetch repositories: ${response.status} ${errorText}`);
      }
    } catch (error) {
      console.error('Failed to fetch repositories:', error);
      // Try to return cached repositories
      const cached = await AsyncStorage.getItem(STORAGE_KEYS.REPOSITORIES);
      const cachedRepos = cached ? JSON.parse(cached) : [];
      return cachedRepos;
    }
  }

  // Get commits for a repository within a time window
  public async getRecentCommits(
    repoFullName: string, 
    since: Date, 
    author?: string
  ): Promise<GitHubCommit[]> {
    if (!this.accessToken) {
      throw new Error('Not authenticated');
    }

    try {
      const params = new URLSearchParams({
        since: since.toISOString(),
        per_page: '100',
      });

      if (author) {
        params.append('author', author);
      }

      const response = await fetch(
        `https://api.github.com/repos/${repoFullName}/commits?${params.toString()}`,
        {
          headers: {
            'Authorization': `token ${this.accessToken}`,
            'Accept': 'application/vnd.github.v3+json',
          },
        }
      );

      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Failed to fetch commits');
      }
    } catch (error) {
      console.error('Failed to fetch commits:', error);
      return [];
    }
  }

  // Verify if user has made minimum commits in the last 24 hours
  public async verifyCommits(
    repoFullName: string,
    minCommits: number = OAUTH_CONFIG.MIN_COMMITS,
    hoursBack: number = OAUTH_CONFIG.VERIFICATION_WINDOW_HOURS
  ): Promise<{ verified: boolean; commitCount: number; commits: GitHubCommit[] }> {
    try {
      const since = new Date(Date.now() - hoursBack * 60 * 60 * 1000);
      const userData = await this.getUserData();
      const commits = await this.getRecentCommits(repoFullName, since, userData?.login);

      // Update sync time after successful verification
      await this.updateSyncTime();

      return {
        verified: commits.length >= minCommits,
        commitCount: commits.length,
        commits,
      };
    } catch (error) {
      console.error('Failed to verify commits:', error);
      return { verified: false, commitCount: 0, commits: [] };
    }
  }

  // Update sync time in Firestore integration
  public async updateSyncTime(): Promise<void> {
    const userId = await getId();
    if (userId) {
      try {
        const integrationResult = await firestoreService.integrations.getIntegrationByProvider(userId, 'github');
        if (integrationResult.success && integrationResult.data) {
          await firestoreService.integrations.updateIntegrationSyncTime(userId, integrationResult.data.id!);
        }
      } catch (error) {
        console.warn('Failed to update sync time in Firestore:', error);
      }
    }
  }

  // Get GitHub integration from Firestore
  public async getIntegrationFromFirestore(): Promise<any> {
    const userId = await getId();
    if (userId) {
      try {
        const integrationResult = await firestoreService.integrations.getIntegrationByProvider(userId, 'github');
        if (integrationResult.success && integrationResult.data) {
          return integrationResult.data;
        }
      } catch (error) {
        console.warn('Failed to get GitHub integration from Firestore:', error);
      }
    }
    return null;
  }

  // Sign out and clear stored data
  public async signOut(): Promise<void> {
    try {
      // Deactivate integration in Firestore
      const userId = await getId();
      if (userId) {
        try {
          const integrationResult = await firestoreService.integrations.getIntegrationByProvider(userId, 'github');
          if (integrationResult.success && integrationResult.data) {
            await firestoreService.integrations.deactivateIntegration(userId, integrationResult.data.id!);
          }
        } catch (firestoreError) {
          console.warn('Failed to deactivate GitHub integration in Firestore:', firestoreError);
        }
      }

      // Clear local data
      this.accessToken = null;
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.ACCESS_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.REPOSITORIES,
      ]);
    } catch (error) {
      console.error('Failed to sign out:', error);
    }
  }

  // Clear authentication (alias for signOut)
  public async clearAuthentication(): Promise<void> {
    return this.signOut();
  }
}

// Export singleton instance
export const githubService = GitHubService.getInstance();
