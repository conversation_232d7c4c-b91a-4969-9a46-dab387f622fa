// Premium Subscription Service
// Handles premium subscription purchases using platform-specific payments

import { Platform } from 'react-native';
import { PaymentType, PaymentProvider } from '../config/payments';
import {
  SubscriptionPurchaseData,
  PaymentResult,
  isInAppPurchaseResult,
} from '../../shared/types/payment';
import { paymentRouter } from './paymentRouter';
import { subscriptionService } from './subscriptionService';
import { getId } from '../utils/variables';
import { SUBSCRIPTION_PRICING } from '../../shared/constants/premiumFeatures';
import { IN_APP_PURCHASE_CONFIG } from '../config/payments';

export interface PremiumSubscriptionOptions {
  plan: 'monthly' | 'yearly';
  userId?: string;
}

export interface PremiumSubscriptionResult {
  success: boolean;
  error?: string;
  subscriptionId?: string;
  provider?: PaymentProvider;
  transactionId?: string;
}

class PremiumSubscriptionService {
  /**
   * Purchase premium subscription using platform-specific payment
   */
  async purchasePremiumSubscription(options: PremiumSubscriptionOptions): Promise<PremiumSubscriptionResult> {
    try {
      const userId = options.userId || await getId();
      if (!userId) {
        return {
          success: false,
          error: 'User ID is required for subscription purchase',
        };
      }

      // Get subscription pricing
      const pricing = SUBSCRIPTION_PRICING[options.plan];
      if (!pricing) {
        return {
          success: false,
          error: 'Invalid subscription plan',
        };
      }

      // Determine product ID based on plan
      const productId = options.plan === 'monthly' 
        ? IN_APP_PURCHASE_CONFIG.PRODUCTS.PREMIUM_MONTHLY.id
        : IN_APP_PURCHASE_CONFIG.PRODUCTS.PREMIUM_YEARLY.id;

      // Create subscription purchase data
      const subscriptionData: SubscriptionPurchaseData = {
        amount: pricing.amount * 100, // Convert to cents
        currency: pricing.currency.toLowerCase(),
        description: `Accustom Premium ${options.plan} subscription`,
        userId,
        paymentType: PaymentType.PREMIUM_SUBSCRIPTION,
        productId,
        subscriptionPlan: options.plan,
      };

      console.log('Processing premium subscription purchase:', {
        plan: options.plan,
        amount: subscriptionData.amount,
        productId,
        platform: Platform.OS,
      });

      // Process payment through router
      const paymentResult = await paymentRouter.processPayment(subscriptionData);

      if (!paymentResult.success) {
        return {
          success: false,
          error: paymentResult.error || 'Subscription purchase failed',
          provider: paymentResult.provider,
        };
      }

      // Handle successful payment
      const subscriptionId = this.extractSubscriptionId(paymentResult);
      
      // Update user subscription status in database
      const updateSuccess = await this.updateUserSubscriptionStatus(
        userId,
        subscriptionId,
        options.plan,
        paymentResult
      );

      if (!updateSuccess) {
        console.error('Failed to update user subscription status after successful payment');
        // Note: Payment was successful but database update failed
        // This should be handled by webhook or retry mechanism
      }

      return {
        success: true,
        subscriptionId,
        provider: paymentResult.provider,
        transactionId: this.extractTransactionId(paymentResult),
      };

    } catch (error) {
      console.error('Premium subscription purchase failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Cancel premium subscription
   */
  async cancelPremiumSubscription(): Promise<{ success: boolean; error?: string }> {
    try {
      const userId = await getId();
      if (!userId) {
        return {
          success: false,
          error: 'User ID is required for subscription cancellation',
        };
      }

      // Cancel subscription in database
      const success = await subscriptionService.cancelUserSubscription();
      
      if (success) {
        console.log('Premium subscription cancelled successfully');
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Failed to cancel subscription',
        };
      }
    } catch (error) {
      console.error('Subscription cancellation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Restore premium subscription (for iOS)
   */
  async restorePremiumSubscription(): Promise<{ success: boolean; error?: string }> {
    try {
      if (Platform.OS !== 'ios') {
        return {
          success: false,
          error: 'Subscription restore is only available on iOS',
        };
      }

      // This would typically involve calling the in-app purchase service
      // to restore purchases and then updating the user's subscription status
      console.log('Restoring premium subscription...');
      
      // TODO: Implement actual restore logic with react-native-iap
      // For now, just refresh the subscription status
      await subscriptionService.checkUserPremiumStatus(true);

      return { success: true };
    } catch (error) {
      console.error('Subscription restore failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get subscription status
   */
  async getSubscriptionStatus() {
    return await subscriptionService.checkUserPremiumStatus();
  }

  /**
   * Check if user has active premium subscription
   */
  async isPremiumUser(): Promise<boolean> {
    const status = await subscriptionService.checkUserPremiumStatus();
    return status.isPremium;
  }

  /**
   * Extract subscription ID from payment result
   */
  private extractSubscriptionId(paymentResult: PaymentResult): string {
    if (isInAppPurchaseResult(paymentResult)) {
      return paymentResult.originalTransactionId || paymentResult.transactionId || 'unknown';
    } else {
      return paymentResult.paymentIntentId || 'unknown';
    }
  }

  /**
   * Extract transaction ID from payment result
   */
  private extractTransactionId(paymentResult: PaymentResult): string {
    if (isInAppPurchaseResult(paymentResult)) {
      return paymentResult.transactionId || 'unknown';
    } else {
      return paymentResult.paymentIntentId || 'unknown';
    }
  }

  /**
   * Update user subscription status in database
   */
  private async updateUserSubscriptionStatus(
    userId: string,
    subscriptionId: string,
    plan: 'monthly' | 'yearly',
    paymentResult: PaymentResult
  ): Promise<boolean> {
    try {
      const now = new Date();
      const startDate = now.toISOString();
      
      // Calculate end date based on plan
      const endDate = new Date(now);
      if (plan === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }

      // Update subscription status
      return await subscriptionService.updateUserSubscription(
        subscriptionId,
        'active',
        startDate,
        endDate.toISOString()
      );
    } catch (error) {
      console.error('Failed to update user subscription status:', error);
      return false;
    }
  }

  /**
   * Get available subscription plans
   */
  getAvailableSubscriptionPlans() {
    return [
      {
        id: 'monthly',
        name: 'Monthly Premium',
        price: SUBSCRIPTION_PRICING.monthly.amount,
        currency: SUBSCRIPTION_PRICING.monthly.currency,
        interval: SUBSCRIPTION_PRICING.monthly.interval,
        productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.PREMIUM_MONTHLY.id,
      },
      {
        id: 'yearly',
        name: 'Yearly Premium',
        price: SUBSCRIPTION_PRICING.yearly.amount,
        currency: SUBSCRIPTION_PRICING.yearly.currency,
        interval: SUBSCRIPTION_PRICING.yearly.interval,
        savings: SUBSCRIPTION_PRICING.yearly.savings,
        productId: IN_APP_PURCHASE_CONFIG.PRODUCTS.PREMIUM_YEARLY.id,
      },
    ];
  }

  /**
   * Get recommended payment provider for current platform
   */
  getRecommendedPaymentProvider(): PaymentProvider {
    if (Platform.OS === 'ios') {
      return PaymentProvider.APPLE_APP_STORE;
    } else if (Platform.OS === 'android') {
      return PaymentProvider.GOOGLE_PLAY;
    } else {
      return PaymentProvider.STRIPE;
    }
  }
}

// Export singleton instance
export const premiumSubscriptionService = new PremiumSubscriptionService();
