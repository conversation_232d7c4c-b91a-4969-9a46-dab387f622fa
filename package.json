{"name": "accustom", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "migrate:analytics": "tsx scripts/migrateAnalytics.ts", "migrate:analytics:dry-run": "tsx scripts/migrateAnalytics.ts --dry-run", "migrate:analytics:cleanup": "tsx scripts/migrateAnalytics.ts --cleanup"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "^1.7.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@stripe/stripe-react-native": "^0.45.0", "expo": "^53.0.0", "expo-blur": "~14.1.5", "expo-calendar": "^14.1.4", "expo-camera": "~16.1.11", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-maps": "^0.11.0", "expo-notifications": "^0.31.4", "expo-print": "~14.1.4", "expo-router": "~5.1.4", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "^0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "firebase": "^11.10.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-iap": "^13.0.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-signature-canvas": "^5.0.1", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "jest": "^29.2.1", "jest-expo": "~53.0.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}