import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import TimePicker from "../common/TimePicker";

interface TimeSelectorProps {
  visible: boolean;
  onClose: () => void;
  onTimeSelect: (time: string) => void;
  initialTime?: string;
  title: string;
}

export const TimeSelector: React.FC<TimeSelectorProps> = ({
  visible,
  onClose,
  onTimeSelect,
  initialTime,
  title,
}) => {
  return (
    <TimePicker
      visible={visible}
      onClose={onClose}
      onTimeSelect={onTimeSelect}
      initialTime={initialTime}
      title={title}
    />
  );
};

interface TimeButtonProps {
  selectedTime?: string;
  onPress: () => void;
  placeholder: string;
}

export const TimeButton: React.FC<TimeButtonProps> = ({
  selectedTime,
  onPress,
  placeholder,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <TouchableOpacity
      style={styles.timeButton}
      onPress={onPress}
      accessibilityLabel={selectedTime ? `Selected time: ${selectedTime}` : placeholder}
      accessibilityRole="button"
      accessibilityHint="Tap to select time"
    >
      <Text style={[styles.timeButtonText, { color: colors.text }]}>
        {selectedTime || placeholder}
      </Text>
      <MaterialIcons name="schedule" size={16} color={colors.textMuted} />
    </TouchableOpacity>
  );
};

interface ThemeColors {
  surface: string;
  text: string;
  textMuted: string;
  primary: string;
  background: string;
  border: string;
}

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
    minWidth: 100,
  },
  timeButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    marginRight: 8,
  },
});
