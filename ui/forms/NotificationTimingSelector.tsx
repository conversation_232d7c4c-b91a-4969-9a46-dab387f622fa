import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { NotificationTiming, NotificationTimingType, NOTIFICATION_TIMING_OPTIONS } from "../../shared/types/customStake";
import TimePicker from '../common/TimePicker';

interface NotificationTimingSelectorProps {
  notificationTiming: NotificationTiming;
  onUpdate: (timing: NotificationTiming) => void;
}

export const NotificationTimingSelector: React.FC<NotificationTimingSelectorProps> = ({
  notificationTiming,
  onUpdate,
}) => {
  const { colors } = useTheme();
  const [showTimePicker, setShowTimePicker] = useState(false);

  const selectTimingType = (type: NotificationTimingType) => {
    let updatedTiming: NotificationTiming = { type };
    
    switch (type) {
      case 'hours-before':
        updatedTiming.hoursBeforeDeadline = 2;
        break;
      case 'every-x-hours':
        updatedTiming.everyXHours = 4;
        break;
      case 'multiple-times':
        updatedTiming.multipleTimes = ['09:00'];
        break;
    }
    
    onUpdate(updatedTiming);
  };

  const updateHoursValue = (value: number, field: 'hoursBeforeDeadline' | 'everyXHours') => {
    onUpdate({
      ...notificationTiming,
      [field]: value
    });
  };

  const hourOptions = [1, 2, 3, 4, 6, 8];
  const timePresets = ['06:00', '07:00', '08:00', '09:00', '12:00', '18:00', '20:00', '21:00'];

  const getSelectedDescription = (type: NotificationTimingType): string => {
    switch (type) {
      case 'hours-before':
        const hoursBefore = notificationTiming.hoursBeforeDeadline || 2;
        return `Notify ${hoursBefore} hour${hoursBefore !== 1 ? 's' : ''} before deadline`;
      case 'every-x-hours':
        const everyHours = notificationTiming.everyXHours || 4;
        return `Remind every ${everyHours} hour${everyHours !== 1 ? 's' : ''}`;
      case 'multiple-times':
        const times = notificationTiming.multipleTimes || [];
        if (times.length === 0) return 'No times selected';
        if (times.length === 1) return `Notify at ${times[0]}`;
        return `Notify at ${times.length} selected times`;
      default:
        return '';
    }
  };



  const removeMultipleTime = (timeToRemove: string) => {
    const currentTimes = notificationTiming.multipleTimes || [];
    onUpdate({
      ...notificationTiming,
      multipleTimes: currentTimes.filter(time => time !== timeToRemove)
    });
  };



  const styles = {
    container: {
      marginTop: 16,
    },
    title: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: colors.text,
      marginBottom: 12,
    },
    optionsGrid: {
      gap: 12,
      marginBottom: 16,
    },
    option: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.surface,
      borderRadius: 8,
      padding: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    optionSelected: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '20',
    },
    optionContent: {
      flex: 1,
      marginLeft: 12,
    },
    optionHeader: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      marginBottom: 4,
    },
    optionTitle: {
      fontSize: 14,
      fontFamily: 'MontserratBold',
      color: colors.text,
    },
    optionTitleDisabled: {
      color: colors.textMuted,
    },
    optionDescription: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      lineHeight: 16,
    },
    optionDescriptionDisabled: {
      color: colors.textMuted,
    },
    optionDisabled: {
      opacity: 0.6,
      backgroundColor: colors.surface,
    },
    comingSoonBadge: {
      backgroundColor: colors.primary,
      borderRadius: 10,
      paddingHorizontal: 6,
      paddingVertical: 2,
    },
    comingSoonText: {
      fontSize: 10,
      fontFamily: 'MontserratBold',
      color: '#000000',
    },
    pickerContainer: {
      marginTop: 8,
      marginBottom: 4,
    },
    hourPicker: {
      maxHeight: 40,
    },
    hourOption: {
      backgroundColor: colors.background,
      borderRadius: 16,
      paddingHorizontal: 12,
      paddingVertical: 6,
      marginRight: 6,
      borderWidth: 1,
      borderColor: colors.border,
    },
    hourOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    hourOptionText: {
      fontSize: 11,
      fontFamily: 'MontserratMedium',
      color: colors.text,
    },
    hourOptionTextSelected: {
      color: '#000000',
      fontFamily: 'MontserratBold',
    },
    multipleTimesInline: {
      marginTop: 8,
    },
    presetLabel: {
      fontSize: 10,
      fontFamily: 'MontserratMedium',
      color: colors.textSecondary,
      marginBottom: 6,
    },
    timePresetPicker: {
      maxHeight: 40,
      marginBottom: 8,
    },
    timePresetOption: {
      backgroundColor: colors.background,
      borderRadius: 16,
      paddingHorizontal: 10,
      paddingVertical: 6,
      marginRight: 6,
      borderWidth: 1,
      borderColor: colors.border,
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      gap: 4,
    },
    timePresetOptionAdded: {
      backgroundColor: colors.primary + '30',
      borderColor: colors.primary,
    },
    timePresetText: {
      fontSize: 11,
      fontFamily: 'MontserratMedium',
      color: colors.text,
    },
    timePresetTextAdded: {
      fontFamily: 'MontserratBold',
      color: colors.primary,
    },
    selectedTimesContainer: {
      marginTop: 8,
    },
    selectedTimesLabel: {
      fontSize: 10,
      fontFamily: 'MontserratMedium',
      color: colors.textSecondary,
      marginBottom: 6,
    },
    timesContainerInline: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 4,
    },
    timeChipInline: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.primary + '30',
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
      gap: 4,
    },
    timeChipTextInline: {
      fontSize: 10,
      fontFamily: 'MontserratMedium',
      color: colors.text,
    },
    customTimeButton: {
      borderStyle: 'dashed' as const,
    },
    customTimeInputContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      gap: 8,
      marginTop: 8,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: colors.separator,
    },
    customTimeInput: {
      backgroundColor: colors.background,
      borderRadius: 6,
      padding: 8,
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
      flex: 1,
    },
    addCustomButton: {
      backgroundColor: colors.primary,
      borderRadius: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
    },
    addCustomButtonText: {
      fontSize: 10,
      fontFamily: 'MontserratBold',
      color: '#000000',
    },
    cancelCustomButton: {
      backgroundColor: colors.surface,
      borderRadius: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    cancelCustomButtonText: {
      fontSize: 10,
      fontFamily: 'MontserratMedium',
      color: colors.text,
    },

  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notification Timings</Text>
      
      <View style={styles.optionsGrid}>
        {NOTIFICATION_TIMING_OPTIONS.map((option) => {
          const isSelected = notificationTiming.type === option.key;
          const isDisabled = option.disabled || false;
          return (
            <TouchableOpacity
              key={option.key}
              style={[
                styles.option,
                isSelected && styles.optionSelected,
                isDisabled && styles.optionDisabled,
              ]}
              onPress={() => !isDisabled && selectTimingType(option.key)}
              disabled={isDisabled}
            >
              <MaterialIcons
                name={option.icon as any}
                size={20}
                color={isDisabled ? colors.textMuted : (isSelected ? colors.primary : colors.text)}
              />
              <View style={styles.optionContent}>
                <View style={styles.optionHeader}>
                  <Text style={[
                    styles.optionTitle,
                    isDisabled && styles.optionTitleDisabled
                  ]}>
                    {option.label}
                  </Text>
                  {option.comingSoon && (
                    <View style={styles.comingSoonBadge}>
                      <Text style={styles.comingSoonText}>Soon</Text>
                    </View>
                  )}
                </View>
                <Text style={[
                  styles.optionDescription,
                  isDisabled && styles.optionDescriptionDisabled
                ]}>
                  {isSelected ? getSelectedDescription(option.key) : option.description}
                </Text>

                {/* Hour Picker Under Title */}
                {isSelected && (option.key === 'hours-before' || option.key === 'every-x-hours') && (
                  <View style={styles.pickerContainer}>
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      style={styles.hourPicker}
                    >
                      {hourOptions.map((hour) => {
                        const isHourSelected = option.key === 'hours-before'
                          ? notificationTiming.hoursBeforeDeadline === hour
                          : notificationTiming.everyXHours === hour;

                        return (
                          <TouchableOpacity
                            key={hour}
                            style={[
                              styles.hourOption,
                              isHourSelected && styles.hourOptionSelected
                            ]}
                            onPress={() => updateHoursValue(hour, option.key === 'hours-before' ? 'hoursBeforeDeadline' : 'everyXHours')}
                          >
                            <Text style={[
                              styles.hourOptionText,
                              isHourSelected && styles.hourOptionTextSelected
                            ]}>
                              {hour}h
                            </Text>
                          </TouchableOpacity>
                        );
                      })}
                    </ScrollView>
                  </View>
                )}

                {/* Multiple Times Presets - Inline */}
                {isSelected && option.key === 'multiple-times' && (
                  <View style={styles.multipleTimesInline}>
                    <Text style={styles.presetLabel}>Quick Add:</Text>
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      style={styles.timePresetPicker}
                    >
                      {/* Custom Time Button - Moved to first position */}
                      <TouchableOpacity
                        style={[
                          styles.timePresetOption,
                          styles.customTimeButton
                        ]}
                        onPress={() => setShowTimePicker(true)}
                      >
                        <MaterialIcons name="add" size={12} color={colors.text} />
                        <Text style={styles.timePresetText}>Custom</Text>
                      </TouchableOpacity>

                      {timePresets.map((time) => {
                        const isAdded = notificationTiming.multipleTimes?.includes(time);
                        return (
                          <TouchableOpacity
                            key={time}
                            style={[
                              styles.timePresetOption,
                              isAdded && styles.timePresetOptionAdded
                            ]}
                            onPress={() => {
                              if (isAdded) {
                                removeMultipleTime(time);
                              } else {
                                const currentTimes = notificationTiming.multipleTimes || [];
                                onUpdate({
                                  ...notificationTiming,
                                  multipleTimes: [...currentTimes, time].sort()
                                });
                              }
                            }}
                          >
                            <Text style={[
                              styles.timePresetText,
                              isAdded && styles.timePresetTextAdded
                            ]}>
                              {time}
                            </Text>
                            {isAdded && (
                              <MaterialIcons name="check" size={12} color={colors.primary} />
                            )}
                          </TouchableOpacity>
                        );
                      })}
                    </ScrollView>

                    {/* Time Picker Modal */}
                    <TimePicker
                      visible={showTimePicker}
                      onClose={() => setShowTimePicker(false)}
                      onTimeSelect={(time) => {
                        const currentTimes = notificationTiming.multipleTimes || [];
                        if (!currentTimes.includes(time)) {
                          onUpdate({
                            ...notificationTiming,
                            multipleTimes: [...currentTimes, time].sort()
                          });
                        }
                        setShowTimePicker(false);
                      }}
                      title="Select Custom Time"
                    />

                    {notificationTiming.multipleTimes && notificationTiming.multipleTimes.length > 0 && (
                      <View style={styles.selectedTimesContainer}>
                        <Text style={styles.selectedTimesLabel}>Selected Times:</Text>
                        <View style={styles.timesContainerInline}>
                          {notificationTiming.multipleTimes.map((time) => (
                            <View key={time} style={styles.timeChipInline}>
                              <Text style={styles.timeChipTextInline}>{time}</Text>
                              <TouchableOpacity onPress={() => removeMultipleTime(time)}>
                                <MaterialIcons name="close" size={12} color={colors.text} />
                              </TouchableOpacity>
                            </View>
                          ))}
                        </View>
                      </View>
                    )}
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </View>


    </View>
  );
};
