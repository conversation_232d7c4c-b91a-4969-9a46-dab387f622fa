// Subscription Modal Demo Component
// For testing the updated SubscriptionModal with dual payment system

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { SubscriptionModal } from '../SubscriptionModal';

export const SubscriptionModalDemo: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleOpenModal = () => {
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  const handleSubscribe = () => {
    Alert.alert(
      'Subscription Success!',
      'Premium subscription has been activated.',
      [
        {
          text: 'OK',
          onPress: () => setModalVisible(false),
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Subscription Modal Demo</Text>
        <Text style={styles.description}>
          Test the updated subscription modal with dual payment system support.
        </Text>

        <TouchableOpacity style={styles.button} onPress={handleOpenModal}>
          <Text style={styles.buttonText}>Open Subscription Modal</Text>
        </TouchableOpacity>

        <View style={styles.features}>
          <Text style={styles.featuresTitle}>New Features:</Text>
          <Text style={styles.featureItem}>✅ Plan selection (Monthly/Yearly)</Text>
          <Text style={styles.featureItem}>✅ Platform-specific payment methods</Text>
          <Text style={styles.featureItem}>✅ Dark theme integration</Text>
          <Text style={styles.featureItem}>✅ Error handling</Text>
          <Text style={styles.featureItem}>✅ Loading states</Text>
          <Text style={styles.featureItem}>✅ Payment provider selection</Text>
        </View>
      </View>

      <SubscriptionModal
        visible={modalVisible}
        onClose={handleCloseModal}
        onSubscribe={handleSubscribe}
        featureName="Premium Features"
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    color: '#666',
    lineHeight: 24,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  features: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 8,
    color: '#555',
    lineHeight: 20,
  },
});

export default SubscriptionModalDemo;
