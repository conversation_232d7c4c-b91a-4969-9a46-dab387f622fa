import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  Platform,
  Image,
} from 'react-native';
import { MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { SUBSCRIPTION_PRICING } from "../../shared/constants/premiumFeatures";
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { VantaDotsBackground } from "../../shared/components/ui/VantaDots";
import { PaymentModal } from "./PaymentModal";
import { PaymentType } from "../../lib/config/payments";

// Platform-specific blur configuration
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 15,
      tint: 'dark' as const
    };
  } else {
    return {
      intensity: 30,
      tint: 'systemMaterialDark' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  }
};

// Enhanced Floating Particle System
const FloatingParticle: React.FC<{
  x: number;
  y: number;
  size: number;
  delay: number;
  color: string;
}> = ({ x, y, size, delay, color }) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.3)).current;
  const rotate = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      opacity.setValue(0);
      translateY.setValue(0);
      translateX.setValue(0);
      scale.setValue(0.3);
      rotate.setValue(0);

      Animated.sequence([
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0.9,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1.2,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(rotate, {
            toValue: 1,
            duration: 8000,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(translateY, {
            toValue: -30,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(translateX, {
            toValue: Math.random() > 0.5 ? 15 : -15,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0.2,
            duration: 3000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        setTimeout(animate, Math.random() * 2000 + 1000);
      });
    };

    setTimeout(animate, delay);
  }, [delay, opacity, translateY, translateX, scale, rotate]);

  const rotateInterpolate = rotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={{
        position: 'absolute',
        left: x,
        top: y,
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: color,
        opacity,
        transform: [
          { translateY },
          { translateX },
          { scale },
          { rotate: rotateInterpolate }
        ],
        shadowColor: color,
        shadowOpacity: 0.8,
        shadowRadius: 12,
        shadowOffset: { width: 0, height: 4 },
        elevation: 8,
      }}
    />
  );
};

// Pulsing Crown Animation
const PulsingCrown: React.FC = () => {
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(0.7)).current;

  useEffect(() => {
    const animate = () => {
      Animated.parallel([
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 1.15,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(opacity, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0.7,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => animate());
    };

    animate();
  }, [scale, opacity]);

  return (
    <Animated.View
      style={{
        transform: [{ scale }],
        opacity,
      }}
    >
      <MaterialIcons name="diamond" size={28} color="#FFD700" />
    </Animated.View>
  );
};

// Shimmer Effect for Feature Cards
const ShimmerEffect: React.FC = () => {
  const shimmer = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      Animated.sequence([
        Animated.timing(shimmer, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmer, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ]).start(() => animate());
    };

    animate();
  }, [shimmer]);

  const translateX = shimmer.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  return (
    <Animated.View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        transform: [{ translateX }],
      }}
    >
      <LinearGradient
        colors={[
          'transparent',
          'rgba(255, 255, 255, 0.1)',
          'rgba(255, 215, 0, 0.1)',
          'rgba(255, 255, 255, 0.1)',
          'transparent',
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{
          flex: 1,
          width: '200%',
        }}
      />
    </Animated.View>
  );
};

// Pulsing Button Animation
const PulsingButton: React.FC<{ children: React.ReactNode; onPress: () => void }> = ({ children, onPress }) => {
  const scale = useRef(new Animated.Value(1)).current;
  const shadowOpacity = useRef(new Animated.Value(0.4)).current;

  useEffect(() => {
    const animate = () => {
      Animated.parallel([
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(shadowOpacity, {
            toValue: 0.8,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shadowOpacity, {
            toValue: 0.4,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => animate());
    };

    animate();
  }, [scale, shadowOpacity]);

  return (
    <Animated.View
      style={{
        transform: [{ scale }],
        shadowOpacity,
      }}
    >
      <TouchableOpacity onPress={onPress}>
        {children}
      </TouchableOpacity>
    </Animated.View>
  );
};

interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubscribe: () => void;
  featureName: string;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  visible,
  onClose,
  onSubscribe,
  featureName,
}) => {
  const { designSystem } = useTheme();
  const [showContent, setShowContent] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly'>('monthly');
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  React.useEffect(() => {
    if (visible) {
      // Small delay to prevent glitchy opening
      const timer = setTimeout(() => setShowContent(true), 100);
      return () => clearTimeout(timer);
    } else {
      setShowContent(false);
      // Reset state when modal closes
      setSelectedPlan('monthly');
      setPaymentModalVisible(false);
    }
  }, [visible]);

  const handlePlanSelection = React.useCallback((plan: 'monthly' | 'yearly') => {
    setSelectedPlan(plan);
  }, []);

  const handleProceedToPayment = () => {
    setPaymentModalVisible(true);
  };

  const handlePaymentSuccess = () => {
    onSubscribe(); // Call the original callback for success handling
    onClose();
  };

  const handlePaymentModalClose = () => {
    setPaymentModalVisible(false);
  };

  // Memoized handlers to prevent re-renders
  const handleMonthlySelection = React.useCallback(() => {
    handlePlanSelection('monthly');
  }, [handlePlanSelection]);

  const handleYearlySelection = React.useCallback(() => {
    handlePlanSelection('yearly');
  }, [handlePlanSelection]);

  const premiumFeatures = [
    { icon: 'sms', title: 'SMS Reminders', description: 'Never miss a commitment' },
    { icon: 'whatsapp', title: 'WhatsApp Alerts', description: 'Get reminders instantly' },
    { icon: 'favorite', title: 'Grace Days', description: '3 grace days worth $6 free' },
    { icon: 'new-releases', title: 'Early Access', description: 'Be first to try new features' },
    { icon: 'support-agent', title: 'Priority Support', description: 'Get help faster' },
    { icon: 'event', title: 'Calendar Sync', description: 'Sync with your calendar' },
  ];

  const styles = {
    modalContainer: {
      flex: 1,
      backgroundColor: '#000000',
      position: 'relative' as const,
    },
    content: {
      flex: 1,
      paddingTop: 30,
      paddingBottom: 20,
      paddingHorizontal: 20,
      justifyContent: 'space-between' as const,
    },
    backgroundPattern: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: 0.03,
    },
    closeButton: {
      position: 'absolute' as const,
      top: 20,
      right: 20,
      padding: 12,
      borderRadius: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      zIndex: 1000,
    },
    header: {
      alignItems: 'center' as const,
    },
    brandBox: {
      backgroundColor: 'rgba(10, 10, 10, 0.9)',
      borderRadius: 16,
      padding: 12,
      marginHorizontal: 12,
      marginBottom: 8,
      alignItems: 'center' as const,
      overflow: 'hidden' as const,
      position: 'relative' as const,
    },
    premiumBox: {
      backgroundColor: 'rgba(10, 10, 10, 0.9)',
      borderRadius: 16,
      padding: 12,
      marginHorizontal: 12,
      marginBottom: 12,
      alignItems: 'center' as const,
      overflow: 'hidden' as const,
      position: 'relative' as const,
    },
    insetShadowTop: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      height: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.4)',
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
    },
    insetShadowLeft: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      bottom: 0,
      width: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.4)',
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    },
    insetHighlightBottom: {
      position: 'absolute' as const,
      bottom: 0,
      left: 0,
      right: 0,
      height: 2,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
    },
    insetHighlightRight: {
      position: 'absolute' as const,
      top: 0,
      right: 0,
      bottom: 0,
      width: 2,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderTopRightRadius: 16,
      borderBottomRightRadius: 16,
    },
    boxBlur: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 12,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    boxContent: {
      alignItems: 'center' as const,
      zIndex: 1,
      position: 'relative' as const,
    },
    brandContainer: {
      alignItems: 'center' as const,
      marginBottom: 12,
    },
    logoContainer: {
      position: 'relative' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginBottom: 4,
    },
    logo: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: 'transparent',
      padding: 6,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    crownContainer: {
      position: 'absolute' as const,
      top: -8,
      right: -8,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderRadius: 12,
      padding: 2,
      borderWidth: 1,
      borderColor: 'rgba(255, 215, 0, 0.3)',
    },
    brandName: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: '#FFFFFF',
      marginBottom: 2,
      textAlign: 'center' as const,
      letterSpacing: 1,
    },
    brandTagline: {
      fontSize: 11,
      fontFamily: 'MontserratMedium',
      color: 'rgba(255, 255, 255, 0.6)',
      textAlign: 'center' as const,
      marginBottom: 12,
    },
    titleContainer: {
      position: 'relative' as const,
      alignItems: 'center' as const,
      marginBottom: 4,
    },
    titleGlow: {
      position: 'absolute' as const,
      fontSize: 24,
      fontFamily: 'MontserratBold',
      color: '#FFD700',
      textAlign: 'center' as const,
      letterSpacing: 1.2,
      textShadowColor: '#FFD700',
      textShadowOffset: { width: 0, height: 0 },
      textShadowRadius: 20,
      opacity: 0.8,
    },
    title: {
      fontSize: 24,
      fontFamily: 'MontserratBold',
      color: '#FFD700',
      textAlign: 'center' as const,
      textShadowColor: 'rgba(255, 215, 0, 0.5)',
      textShadowOffset: { width: 0, height: 3 },
      textShadowRadius: 8,
      letterSpacing: 1.2,
    },
    subtitle: {
      fontSize: 13,
      fontFamily: 'MontserratRegular',
      color: 'rgba(255, 255, 255, 0.8)',
      textAlign: 'center' as const,
      marginBottom: 12,
      lineHeight: 18,
    },
    priceContainer: {
      alignItems: 'center' as const,
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      borderRadius: 12,
      padding: 12,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)',
      ...designSystem.shadows.xl,
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOpacity: 0.3,
      position: 'relative' as const,
      overflow: 'hidden' as const,
    },
    priceGradientOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: 0.1,
    },
    priceContent: {
      alignItems: 'center' as const,
      zIndex: 1,
    },
    priceText: {
      fontSize: 28,
      fontFamily: 'MontserratBold',
      color: '#FFD700',
      textShadowColor: 'rgba(255, 215, 0, 0.5)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 6,
    },
    priceSubtext: {
      fontSize: 12,
      fontFamily: 'MontserratMedium',
      color: 'rgba(255, 255, 255, 0.8)',
      marginTop: 2,
      letterSpacing: 0.5,
    },
    contentContainer: {
      paddingTop: 12,
    },
    featuresContainer: {
      paddingHorizontal: 20,
      paddingBottom: 12,
    },
    featuresTitle: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: '#FFFFFF',
      textAlign: 'center' as const,
      marginBottom: 8,
      textShadowColor: 'rgba(255, 255, 255, 0.1)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    featuresGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      justifyContent: 'space-between' as const,
    },
    featureCard: {
      width: '48%' as any,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 10,
      padding: 8,
      marginBottom: 6,
      alignItems: 'center' as const,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
      minHeight: 75,
      ...designSystem.shadows.lg,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
      shadowOpacity: 0.3,
      position: 'relative' as const,
      overflow: 'hidden' as const,
    },
    featureCardGlow: {
      position: 'absolute' as const,
      top: -50,
      left: -50,
      right: -50,
      bottom: -50,
      backgroundColor: 'rgba(255, 255, 255, 0.02)',
      borderRadius: 100,
    },
    featureIconContainer: {
      backgroundColor: 'rgba(255, 215, 0, 0.12)',
      borderRadius: 8,
      padding: 4,
      marginBottom: 6,
    },
    featureTitle: {
      fontSize: 13,
      fontFamily: 'MontserratSemiBold',
      color: '#FFFFFF',
      textAlign: 'center' as const,
      marginBottom: 4,
      letterSpacing: 0.3,
    },
    featureDescription: {
      fontSize: 10,
      fontFamily: 'MontserratRegular',
      color: 'rgba(255, 255, 255, 0.75)',
      textAlign: 'center' as const,
      lineHeight: 14,
    },
    ctaContainer: {
      alignItems: 'center' as const,
      paddingTop: 8,
    },
    upgradeButton: {
      marginBottom: 12,
      alignSelf: 'stretch' as const,
      borderRadius: designSystem.borderRadius.xl,
      overflow: 'hidden' as const,
      borderWidth: 2,
      borderColor: '#FFD700', // Golden border like create commit
      ...designSystem.shadows.xl,
      shadowColor: '#FFD700',
    },
    upgradeContainer: {
      paddingVertical: designSystem.spacing.xl,
      paddingHorizontal: designSystem.spacing.xl,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      position: 'relative' as const,
      backgroundColor: '#000000', // Black background like create commit
      minHeight: 60,
      width: '100%' as any,
      overflow: 'hidden' as const,
    },
    upgradeBlur: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 2,
      borderRadius: designSystem.borderRadius.xl,
      overflow: 'hidden' as const,
    },
    upgradeContent: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      zIndex: 30, // Above blur layer
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    upgradeText: {
      color: "#FFD700", // Gold text like create commit
      fontSize: designSystem.typography.fontSize.lg,
      fontFamily: 'MontserratBold',
      letterSpacing: designSystem.typography.letterSpacing.wide,
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
      textAlign: 'center' as const,
      marginHorizontal: designSystem.spacing.sm,
    },
    skipText: {
      fontSize: 16,
      fontFamily: 'MontserratMedium',
      color: 'rgba(255, 255, 255, 0.65)',
      textDecorationLine: 'underline' as const,
      textDecorationColor: 'rgba(255, 255, 255, 0.3)',
    },
    vantaDotsContainer: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 0, // Above background but below content
    },
    divider: {
      height: 1,
      backgroundColor: 'rgba(255, 215, 0, 0.2)',
      marginHorizontal: 40,
      marginVertical: 6,
    },
    // Enhanced styles for subscription plans
    plansContainer: {
      paddingHorizontal: 20,
      paddingTop: 6,
      paddingBottom: 12,
    },
    plansSectionTitle: {
      fontSize: 18,
      fontWeight: '700' as const,
      marginBottom: 12,
      textAlign: 'center' as const,
      color: '#FFFFFF',
      fontFamily: 'MontserratBold',
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    planOptions: {
      gap: 10,
      marginBottom: 16,
    },
    planOption: {
      borderWidth: 2,
      borderColor: 'rgba(255, 215, 0, 0.3)',
      borderRadius: 12,
      padding: 12,
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      position: 'relative' as const,
    },
    selectedPlanOption: {
      borderColor: '#FFD700',
      backgroundColor: 'rgba(255, 215, 0, 0.08)',
      borderWidth: 2,
    },
    planHeader: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'flex-start' as const,
      marginBottom: 8,
    },
    planName: {
      fontSize: 16,
      fontWeight: '700' as const,
      color: '#FFFFFF',
      fontFamily: 'MontserratBold',
    },
    planPrice: {
      fontSize: 16,
      fontWeight: '700' as const,
      color: '#FFD700',
      fontFamily: 'MontserratBold',
    },
    planTitleContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      gap: 6,
    },
    savingsBadge: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
      backgroundColor: '#4CAF50',
    },
    savingsBadgeBottomRight: {
      position: 'absolute' as const,
      bottom: 8,
      right: 8,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
      backgroundColor: '#4CAF50',
    },
    savingsText: {
      fontSize: 9,
      fontWeight: '700' as const,
      color: '#FFFFFF',
      fontFamily: 'MontserratBold',
    },
    planDescription: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      fontFamily: 'MontserratMedium',
      lineHeight: 16,
    },



  };

  // Pre-compute styles for better performance (after styles object is defined)
  const monthlyPlanStyle = React.useMemo(() => {
    return selectedPlan === 'monthly'
      ? { ...styles.planOption, ...styles.selectedPlanOption }
      : styles.planOption;
  }, [selectedPlan]);

  const yearlyPlanStyle = React.useMemo(() => {
    return selectedPlan === 'yearly'
      ? { ...styles.planOption, ...styles.selectedPlanOption }
      : styles.planOption;
  }, [selectedPlan]);

  // Memoized check icons to prevent re-renders
  const monthlyCheckIcon = React.useMemo(() => {
    return selectedPlan === 'monthly' ? (
      <MaterialIcons name="check-circle" size={16} color="#FFD700" />
    ) : null;
  }, [selectedPlan]);

  const yearlyCheckIcon = React.useMemo(() => {
    return selectedPlan === 'yearly' ? (
      <MaterialIcons name="check-circle" size={16} color="#FFD700" />
    ) : null;
  }, [selectedPlan]);

  if (!visible) {
    return null;
  }

  return (
    <>
      <Modal
        visible={visible}
        transparent={false}
        animationType="slide"
        onRequestClose={onClose}
        presentationStyle="fullScreen"
        statusBarTranslucent={true}
      >
        <View style={styles.modalContainer}>
          {/* VantaDots Background */}
          {showContent && (
            <View style={styles.vantaDotsContainer}>
              <VantaDotsBackground screenKey="subscription-modal" />
            </View>
          )}

          {/* Reduced Floating Particle System */}
          <FloatingParticle x={40} y={120} size={6} delay={500} color="#FFD700" />
          <FloatingParticle x={320} y={180} size={4} delay={1000} color="#FFD700" />
          <FloatingParticle x={120} y={250} size={5} delay={1500} color="#FFD700" />
          <FloatingParticle x={280} y={90} size={4} delay={2000} color="#FFD700" />

          {/* Subtle Background Pattern */}
          <LinearGradient
            colors={[
              'rgba(255, 215, 0, 0.02)',
              'rgba(255, 165, 0, 0.01)',
              'rgba(255, 215, 0, 0.02)',
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.backgroundPattern}
          />

          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color="#FFD700" />
          </TouchableOpacity>

          {/* Content */}
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              {/* Brand Box */}
              <View style={styles.brandBox}>
                <View style={styles.logoContainer}>
                  <View style={styles.logo}>
                    <Image
                      source={require("../../assets/images/new_logo.png")}
                      style={{ width: 48, height: 48 }}
                      resizeMode="contain"
                    />
                  </View>
                </View>
                <Text style={styles.brandName}>ACCUSTOM</Text>
                <Text style={styles.brandTagline}>Premium Habit Building</Text>
              </View>

              {/* Premium Box */}
              <View style={styles.premiumBox}>
                <View style={styles.titleContainer}>
                  <Text style={styles.titleGlow}>Unlock Premium</Text>
                  <Text style={styles.title}>Unlock Premium</Text>
                </View>
                <Text style={styles.subtitle}>Advanced tools for maximum success</Text>
              </View>
            </View>

            {/* Content Container with proper spacing */}
            <View style={styles.contentContainer}>
            {/* Features Grid */}
            <View style={styles.featuresContainer}>
              <Text style={styles.featuresTitle}>Everything Included</Text>
              <View style={styles.featuresGrid}>
                {premiumFeatures.map((feature, index) => (
                  <View key={index} style={styles.featureCard}>
                    <View style={styles.featureCardGlow} />
                    <View style={styles.featureIconContainer}>
                      {feature.icon === 'whatsapp' ? (
                        <FontAwesome name="whatsapp" size={20} color="#FFD700" />
                      ) : (
                        <MaterialIcons name={feature.icon as any} size={20} color="#FFD700" />
                      )}
                    </View>
                    <Text style={styles.featureTitle}>{feature.title}</Text>
                    <Text style={styles.featureDescription}>{feature.description}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Divider */}
            <View style={styles.divider} />

            {/* Subscription Plans */}
            <View style={styles.plansContainer}>
            <Text style={styles.plansSectionTitle}>Choose Your Plan</Text>

            {/* Plan Selection */}
            <View style={styles.planOptions}>
              <TouchableOpacity
                style={monthlyPlanStyle}
                onPress={handleMonthlySelection}
              >
                <View style={styles.planHeader}>
                  <View style={styles.planTitleContainer}>
                    <Text style={styles.planName}>Monthly</Text>
                    {monthlyCheckIcon}
                  </View>
                  <Text style={styles.planPrice}>
                    ${SUBSCRIPTION_PRICING.monthly.amount}/month
                  </Text>
                </View>
                <Text style={styles.planDescription}>
                  Flexible monthly billing
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={yearlyPlanStyle}
                onPress={handleYearlySelection}
              >
                <View style={styles.planHeader}>
                  <View style={styles.planTitleContainer}>
                    <Text style={styles.planName}>Yearly</Text>
                    {yearlyCheckIcon}
                  </View>
                  <Text style={styles.planPrice}>
                    ${SUBSCRIPTION_PRICING.yearly.amount}/year
                  </Text>
                </View>
                <Text style={styles.planDescription}>
                  Best value - 2 months free
                </Text>
                <View style={styles.savingsBadgeBottomRight}>
                  <Text style={styles.savingsText}>
                    Save ${SUBSCRIPTION_PRICING.yearly.savings}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* CTA */}
            <View style={styles.ctaContainer}>
              <TouchableOpacity
                style={styles.upgradeButton}
                onPress={handleProceedToPayment}
                activeOpacity={0.8}
              >
                <View style={styles.upgradeContainer}>
                  {/* Floating Dots Animation - Same as original */}
                  {[
                    { x: 20, y: 15, size: 16, delay: 0 },
                    { x: 60, y: 25, size: 20, delay: 200 },
                    { x: 40, y: 40, size: 14, delay: 400 },
                    { x: 100, y: 20, size: 18, delay: 600 },
                    { x: 130, y: 35, size: 15, delay: 800 },
                    { x: 80, y: 45, size: 19, delay: 1000 },
                    { x: 170, y: 25, size: 17, delay: 1200 },
                    { x: 200, y: 35, size: 22, delay: 1400 },
                    { x: 180, y: 10, size: 13, delay: 1600 },
                    { x: 240, y: 30, size: 18, delay: 1800 },
                    { x: 270, y: 15, size: 16, delay: 2000 },
                    { x: 250, y: 45, size: 15, delay: 2200 },
                    { x: 300, y: 25, size: 17, delay: 2400 },
                    { x: 320, y: 40, size: 14, delay: 2600 },
                  ].map((dot, index) => (
                    <FloatingParticle
                      key={index}
                      x={dot.x}
                      y={dot.y}
                      size={dot.size}
                      delay={dot.delay}
                      color="#FFD700"
                    />
                  ))}

                  <BlurView
                    intensity={getBlurConfig().intensity}
                    tint={getBlurConfig().tint}
                    {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
                    style={styles.upgradeBlur}
                  />
                  <View style={styles.upgradeContent}>
                    <MaterialIcons name="diamond" size={18} color="#FFD700" />
                    <Text style={styles.upgradeText}>
                      {selectedPlan === 'monthly' ? 'Subscribe Monthly' : 'Subscribe Yearly'}
                    </Text>
                    <MaterialIcons name="arrow-right" size={16} color="#FFD700" />
                  </View>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={onClose}>
                <Text style={styles.skipText}>Maybe Later</Text>
              </TouchableOpacity>
            </View>
          </View>
          </View>
          </View>
        </View>
      </Modal>

    {/* Payment Modal */}
    <PaymentModal
      visible={paymentModalVisible}
      onClose={handlePaymentModalClose}
      onSuccess={handlePaymentSuccess}
      selectedPlan={selectedPlan}
      paymentType={PaymentType.PREMIUM_SUBSCRIPTION}
    />
    </>
  );
};
