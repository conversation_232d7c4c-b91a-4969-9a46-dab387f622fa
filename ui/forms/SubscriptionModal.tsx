import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Platform,
  Image,
} from 'react-native';
import { MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { SUBSCRIPTION_PRICING } from "../../shared/constants/premiumFeatures";

import { PaymentModal } from "./PaymentModal";
import { PaymentType } from "../../lib/config/payments";
import { AccustomText } from "../../shared/components/AccustomText";
import { Button } from "../../shared/components/ui/Button";



interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubscribe: () => void;
  featureName: string;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  visible,
  onClose,
  onSubscribe,
  featureName, // Currently unused but kept for future feature-specific messaging
}) => {
  const { colors, designSystem } = useTheme();
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly'>('yearly'); // Default to yearly for better value
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  React.useEffect(() => {
    if (!visible) {
      // Reset state when modal closes
      setSelectedPlan('yearly');
      setPaymentModalVisible(false);
    }
  }, [visible]);

  const handlePlanSelection = React.useCallback((plan: 'monthly' | 'yearly') => {
    setSelectedPlan(plan);
  }, []);

  const handleProceedToPayment = () => {
    setPaymentModalVisible(true);
  };

  const handlePaymentSuccess = () => {
    onSubscribe(); // Call the original callback for success handling
    onClose();
  };

  const handlePaymentModalClose = () => {
    setPaymentModalVisible(false);
  };

  // Memoized handlers to prevent re-renders
  const handleMonthlySelection = React.useCallback(() => {
    handlePlanSelection('monthly');
  }, [handlePlanSelection]);

  const handleYearlySelection = React.useCallback(() => {
    handlePlanSelection('yearly');
  }, [handlePlanSelection]);

  const premiumFeatures = [
    { icon: 'sms', title: 'SMS Reminders', description: 'Never miss a commitment' },
    { icon: 'whatsapp', title: 'WhatsApp Alerts', description: 'Get reminders instantly' },
    { icon: 'favorite', title: 'Grace Days', description: '3 grace days worth $6 free' },
    { icon: 'new-releases', title: 'Early Access', description: 'Be first to try new features' },
    { icon: 'support-agent', title: 'Priority Support', description: 'Get help faster' },
    { icon: 'event', title: 'Calendar Sync', description: 'Sync with your calendar' },
  ];

  const styles = {
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
      justifyContent: 'space-between' as const,
    },
    content: {
      flex: 1,
      paddingHorizontal: designSystem.spacing.xl,
      paddingTop: Platform.OS === 'ios' ? 60 : 40,
      paddingBottom: designSystem.spacing.xl,
      justifyContent: 'space-between' as const,
    },
    closeButton: {
      position: 'absolute' as const,
      top: Platform.OS === 'ios' ? 50 : 30,
      right: designSystem.spacing.lg,
      padding: designSystem.spacing.sm,
      borderRadius: designSystem.borderRadius.round,
      backgroundColor: colors.surface,
      zIndex: 1000,
      ...designSystem.shadows.md,
    },
    topSection: {
      alignItems: 'center' as const,
      paddingBottom: designSystem.spacing.md,
    },
    logoContainer: {
      alignItems: 'center' as const,
      marginBottom: designSystem.spacing.sm,
    },
    logo: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.surface,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginBottom: designSystem.spacing.xs,
      ...designSystem.shadows.lg,
    },
    brandName: {
      fontSize: designSystem.typography.fontSize.lg,
      fontFamily: 'MontserratBold',
      color: colors.text,
      marginBottom: 2,
      textAlign: 'center' as const,
      letterSpacing: designSystem.typography.letterSpacing.wide,
    },
    brandTagline: {
      fontSize: designSystem.typography.fontSize.xs,
      fontFamily: 'MontserratMedium',
      color: colors.textSecondary,
      textAlign: 'center' as const,
    },
    title: {
      fontSize: designSystem.typography.fontSize.xxl,
      fontFamily: 'MontserratBold',
      color: colors.primary,
      textAlign: 'center' as const,
      marginBottom: designSystem.spacing.xs,
      letterSpacing: designSystem.typography.letterSpacing.wide,
    },
    subtitle: {
      fontSize: designSystem.typography.fontSize.sm,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      textAlign: 'center' as const,
      lineHeight: designSystem.typography.lineHeight.normal * designSystem.typography.fontSize.sm,
      paddingHorizontal: designSystem.spacing.sm,
    },
    middleSection: {
      paddingVertical: designSystem.spacing.md,
    },
    featuresContainer: {
      alignItems: 'center' as const,
    },
    featuresTitle: {
      fontSize: designSystem.typography.fontSize.lg,
      fontFamily: 'MontserratBold',
      color: colors.text,
      textAlign: 'center' as const,
      marginBottom: designSystem.spacing.md,
    },
    featuresGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      justifyContent: 'space-between' as const,
      gap: designSystem.spacing.xs,
    },
    featureCard: {
      width: '48%' as any,
      backgroundColor: colors.surface,
      borderRadius: designSystem.borderRadius.md,
      padding: designSystem.spacing.sm,
      alignItems: 'center' as const,
      borderWidth: 1,
      borderColor: colors.border,
      minHeight: 70,
      ...designSystem.shadows.sm,
    },
    featureIconContainer: {
      backgroundColor: colors.primary + '20',
      borderRadius: designSystem.borderRadius.sm,
      padding: designSystem.spacing.xs,
      marginBottom: designSystem.spacing.xs,
    },
    featureTitle: {
      fontSize: designSystem.typography.fontSize.xs,
      fontFamily: 'MontserratSemiBold',
      color: colors.text,
      textAlign: 'center' as const,
      marginBottom: 2,
    },
    featureDescription: {
      fontSize: 10,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      textAlign: 'center' as const,
      lineHeight: 12,
    },
    bottomSection: {
      paddingTop: designSystem.spacing.md,
    },
    plansContainer: {
      alignItems: 'center' as const,
      marginBottom: designSystem.spacing.lg,
    },
    plansSectionTitle: {
      fontSize: designSystem.typography.fontSize.lg,
      fontFamily: 'MontserratBold',
      marginBottom: designSystem.spacing.md,
      textAlign: 'center' as const,
      color: colors.text,
    },
    planOptions: {
      gap: designSystem.spacing.sm,
      width: '100%' as any,
    },
    planOption: {
      borderWidth: 2,
      borderColor: colors.border,
      borderRadius: designSystem.borderRadius.md,
      padding: designSystem.spacing.md,
      backgroundColor: colors.surface,
      ...designSystem.shadows.sm,
    },
    selectedPlanOption: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '10',
    },
    planHeader: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'flex-start' as const,
      marginBottom: designSystem.spacing.xs,
    },
    planName: {
      fontSize: designSystem.typography.fontSize.md,
      fontFamily: 'MontserratBold',
      color: colors.text,
    },
    planPrice: {
      fontSize: designSystem.typography.fontSize.md,
      fontFamily: 'MontserratBold',
      color: colors.primary,
    },
    planTitleContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      gap: designSystem.spacing.xs,
    },
    savingsBadge: {
      paddingHorizontal: designSystem.spacing.xs,
      paddingVertical: 2,
      borderRadius: designSystem.borderRadius.sm,
      backgroundColor: colors.success,
    },
    savingsBadgeBottomRight: {
      position: 'absolute' as const,
      bottom: designSystem.spacing.xs,
      right: designSystem.spacing.xs,
      paddingHorizontal: designSystem.spacing.xs,
      paddingVertical: 2,
      borderRadius: designSystem.borderRadius.sm,
      backgroundColor: colors.success,
    },
    savingsText: {
      fontSize: designSystem.typography.fontSize.xs,
      fontFamily: 'MontserratBold',
      color: '#FFFFFF',
    },
    planDescription: {
      fontSize: designSystem.typography.fontSize.xs,
      color: colors.textSecondary,
      fontFamily: 'MontserratMedium',
      lineHeight: designSystem.typography.lineHeight.normal * designSystem.typography.fontSize.xs,
    },
    ctaContainer: {
      alignItems: 'center' as const,
    },
    skipText: {
      fontSize: designSystem.typography.fontSize.sm,
      fontFamily: 'MontserratMedium',
      color: colors.textMuted,
      textDecorationLine: 'underline' as const,
      marginTop: designSystem.spacing.md,
    },
  };

  // Pre-compute styles for better performance (after styles object is defined)
  const monthlyPlanStyle = React.useMemo(() => {
    return selectedPlan === 'monthly'
      ? { ...styles.planOption, ...styles.selectedPlanOption }
      : styles.planOption;
  }, [selectedPlan, styles.planOption, styles.selectedPlanOption]);

  const yearlyPlanStyle = React.useMemo(() => {
    return selectedPlan === 'yearly'
      ? { ...styles.planOption, ...styles.selectedPlanOption }
      : styles.planOption;
  }, [selectedPlan, styles.planOption, styles.selectedPlanOption]);

  // Memoized check icons to prevent re-renders
  const monthlyCheckIcon = React.useMemo(() => {
    return selectedPlan === 'monthly' ? (
      <MaterialIcons name="check-circle" size={16} color={colors.primary} />
    ) : null;
  }, [selectedPlan, colors.primary]);

  const yearlyCheckIcon = React.useMemo(() => {
    return selectedPlan === 'yearly' ? (
      <MaterialIcons name="check-circle" size={16} color={colors.primary} />
    ) : null;
  }, [selectedPlan, colors.primary]);

  if (!visible) {
    return null;
  }

  return (
    <>
      <Modal
        visible={visible}
        transparent={false}
        animationType="slide"
        onRequestClose={onClose}
        presentationStyle="fullScreen"
        statusBarTranslucent={true}
      >
        <View style={styles.modalContainer}>
          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color={colors.textMuted} />
          </TouchableOpacity>

          <View style={styles.content}>
            {/* Top Section - Header */}
            <View style={styles.topSection}>
              <View style={styles.logoContainer}>
                <View style={styles.logo}>
                  <Image
                    source={require("../../assets/images/new_logo.png")}
                    style={{ width: 40, height: 40 }}
                    resizeMode="contain"
                  />
                </View>
                <AccustomText style={styles.brandName}>
                  ACCUSTOM
                </AccustomText>
                <Text style={styles.brandTagline}>Premium Habit Building</Text>
              </View>

              <Text style={styles.title}>Unlock Premium</Text>
              <Text style={styles.subtitle}>
                Get advanced tools and features to maximize your success with habit building
              </Text>
            </View>

            {/* Middle Section - Features */}
            <View style={styles.middleSection}>
              <View style={styles.featuresContainer}>
                <Text style={styles.featuresTitle}>Everything Included</Text>
                <View style={styles.featuresGrid}>
                  {premiumFeatures.map((feature, index) => (
                    <View key={index} style={styles.featureCard}>
                      <View style={styles.featureIconContainer}>
                        {feature.icon === 'whatsapp' ? (
                          <FontAwesome name="whatsapp" size={12} color={colors.primary} />
                        ) : (
                          <MaterialIcons name={feature.icon as any} size={12} color={colors.primary} />
                        )}
                      </View>
                      <Text style={styles.featureTitle}>{feature.title}</Text>
                      <Text style={styles.featureDescription}>{feature.description}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </View>

            {/* Bottom Section - Plans & CTA */}
            <View style={styles.bottomSection}>
              <View style={styles.plansContainer}>
                <Text style={styles.plansSectionTitle}>Choose Your Plan</Text>

                {/* Plan Selection */}
                <View style={styles.planOptions}>
                  <TouchableOpacity
                    style={monthlyPlanStyle}
                    onPress={handleMonthlySelection}
                  >
                    <View style={styles.planHeader}>
                      <View style={styles.planTitleContainer}>
                        <Text style={styles.planName}>Monthly</Text>
                        {monthlyCheckIcon}
                      </View>
                      <Text style={styles.planPrice}>
                        ${SUBSCRIPTION_PRICING.monthly.amount}/month
                      </Text>
                    </View>
                    <Text style={styles.planDescription}>
                      Flexible monthly billing
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={yearlyPlanStyle}
                    onPress={handleYearlySelection}
                  >
                    <View style={styles.planHeader}>
                      <View style={styles.planTitleContainer}>
                        <Text style={styles.planName}>Yearly</Text>
                        {yearlyCheckIcon}
                      </View>
                      <Text style={styles.planPrice}>
                        ${SUBSCRIPTION_PRICING.yearly.amount}/year
                      </Text>
                    </View>
                    <Text style={styles.planDescription}>
                      Best value - 2 months free
                    </Text>
                    <View style={styles.savingsBadgeBottomRight}>
                      <Text style={styles.savingsText}>
                        Save ${SUBSCRIPTION_PRICING.yearly.savings}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>

              {/* CTA */}
              <View style={styles.ctaContainer}>
                <Button
                  title={selectedPlan === 'monthly' ? 'Subscribe Monthly' : 'Subscribe Yearly'}
                  onPress={handleProceedToPayment}
                  variant="primary"
                  size="large"
                />
                <TouchableOpacity onPress={onClose}>
                  <Text style={styles.skipText}>Maybe Later</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Payment Modal */}
      <PaymentModal
        visible={paymentModalVisible}
        onClose={handlePaymentModalClose}
        onSuccess={handlePaymentSuccess}
        selectedPlan={selectedPlan}
        paymentType={PaymentType.PREMIUM_SUBSCRIPTION}
      />
    </>
  );
};
