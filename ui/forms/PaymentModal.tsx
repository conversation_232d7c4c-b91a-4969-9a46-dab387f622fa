// Payment Modal Component
// Dedicated modal for payment method selection and processing

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Platform,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { PaymentMethodSelector } from "../components/PaymentMethodSelector";
import { PaymentType, PaymentProvider } from "../../lib/config/payments";
import { premiumSubscriptionService } from "../../lib/services/premiumSubscriptionService";
import { SUBSCRIPTION_PRICING } from "../../shared/constants/premiumFeatures";

interface PaymentModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  selectedPlan: 'monthly' | 'yearly';
  paymentType: PaymentType;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  visible,
  onClose,
  onSuccess,
  selectedPlan,
  paymentType,
}) => {
  const { colors } = useTheme();
  const [selectedProvider, setSelectedProvider] = useState<PaymentProvider>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>();

  const handlePaymentProviderSelection = (provider: PaymentProvider) => {
    setSelectedProvider(provider);
    setError(undefined);
  };

  const handlePayment = async () => {
    if (!selectedProvider) {
      setError('Please select a payment method');
      return;
    }

    setIsProcessing(true);
    setError(undefined);

    try {
      if (paymentType === PaymentType.PREMIUM_SUBSCRIPTION) {
        const result = await premiumSubscriptionService.purchasePremiumSubscription({
          plan: selectedPlan,
        });

        if (result.success) {
          onSuccess();
          onClose();
        } else {
          setError(result.error || 'Payment failed');
        }
      }
      // Add other payment types here (grace days, etc.)
    } catch (error) {
      console.error('Payment error:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const getPaymentTitle = () => {
    switch (paymentType) {
      case PaymentType.PREMIUM_SUBSCRIPTION:
        return `Subscribe ${selectedPlan === 'monthly' ? 'Monthly' : 'Yearly'}`;
      case PaymentType.GRACE_DAY:
        return 'Purchase Grace Days';
      default:
        return 'Complete Payment';
    }
  };

  const getPaymentAmount = () => {
    if (paymentType === PaymentType.PREMIUM_SUBSCRIPTION) {
      return selectedPlan === 'monthly' 
        ? `$${SUBSCRIPTION_PRICING.monthly.amount}/month`
        : `$${SUBSCRIPTION_PRICING.yearly.amount}/year`;
    }
    return '';
  };

  const getBlurConfig = () => {
    if (Platform.OS === 'ios') {
      return {
        intensity: 100,
        tint: 'dark' as const,
        experimentalBlurMethod: 'dimezisBlurView' as const,
      };
    } else {
      return {
        intensity: 150,
        tint: 'dark' as const,
        experimentalBlurMethod: 'dimezisBlurView' as const,
      };
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <BlurView
          intensity={getBlurConfig().intensity}
          tint={getBlurConfig().tint}
          style={styles.blurOverlay}
        />
        
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              disabled={isProcessing}
            >
              <MaterialIcons name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            
            <Text style={styles.title}>{getPaymentTitle()}</Text>
            
            {getPaymentAmount() && (
              <Text style={styles.amount}>{getPaymentAmount()}</Text>
            )}
          </View>

          {/* Content */}
          <ScrollView 
            style={styles.content}
            showsVerticalScrollIndicator={false}
          >
            {/* Payment Method Selection */}
            <PaymentMethodSelector
              paymentType={paymentType}
              selectedProvider={selectedProvider}
              onProviderSelect={handlePaymentProviderSelection}
              disabled={isProcessing}
              darkTheme={true}
              showRecommended={true}
            />

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <MaterialIcons name="error" size={20} color="#FF6B6B" />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={[
                styles.payButton,
                (!selectedProvider || isProcessing) && styles.disabledButton,
              ]}
              onPress={handlePayment}
              disabled={!selectedProvider || isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator size="small" color="#000" />
              ) : (
                <>
                  <MaterialIcons name="payment" size={20} color="#000" />
                  <Text style={styles.payButtonText}>
                    {paymentType === PaymentType.PREMIUM_SUBSCRIPTION ? 'Subscribe Now' : 'Pay Now'}
                  </Text>
                </>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
              disabled={isProcessing}
            >
              <Text style={[styles.cancelButtonText, { opacity: isProcessing ? 0.5 : 1 }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = {
  overlay: {
    flex: 1,
    justifyContent: 'flex-end' as const,
  },
  blurOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    backgroundColor: 'rgba(20, 20, 20, 0.95)',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: '80%',
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.2)',
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 215, 0, 0.1)',
    alignItems: 'center' as const,
  },
  closeButton: {
    position: 'absolute' as const,
    top: 20,
    right: 20,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  title: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#FFFFFF',
    textAlign: 'center' as const,
    marginBottom: 8,
  },
  amount: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#FFD700',
    textAlign: 'center' as const,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  errorContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    gap: 12,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 107, 107, 0.3)',
  },
  errorText: {
    fontSize: 14,
    flex: 1,
    color: '#FF6B6B',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 215, 0, 0.1)',
  },
  payButton: {
    backgroundColor: '#FFD700',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 8,
    marginBottom: 12,
  },
  disabledButton: {
    opacity: 0.5,
  },
  payButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#000',
  },
  cancelButton: {
    padding: 12,
    alignItems: 'center' as const,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
};
