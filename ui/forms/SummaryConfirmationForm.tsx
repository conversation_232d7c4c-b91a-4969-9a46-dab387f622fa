import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Platform,
  ActivityIndicator,
  Modal,
  Image,
} from 'react-native';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import SignatureCanvas from 'react-native-signature-canvas';
import { useRouter } from 'expo-router';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { CustomStakeData, EVIDENCE_OPTIONS, FREQUENCY_OPTIONS, RECIPIENT_OPTIONS, STRICTNESS_OPTIONS } from "../../shared/types/customStake";
import { processStripePayment, processGooglePayPayment, processApplePayPayment, PaymentData } from "../../lib/services/paymentService";
import { getId } from "../../lib/utils/variables";
import { ErrorModal } from "../../shared/components/modals";
import { firestoreService } from "../../lib/services/database";
import { calendarService } from "../../lib/services/calendarService";
import { createCommitmentCalendarEvent } from "../../lib/utils/calendarEventUtils";

interface SummaryConfirmationFormProps {
  formData: CustomStakeData;
  totalStake: number;
  onSubmit: () => void;
  onCancel: () => void;
}

export const SummaryConfirmationForm: React.FC<SummaryConfirmationFormProps> = ({
  formData,
  totalStake,
  onSubmit,
  onCancel,
}) => {
  const { colors } = useTheme();
  const router = useRouter();
  const [signature, setSignature] = useState('');
  const [isSignatureValid, setIsSignatureValid] = useState(false);
  const [isSigningActive, setIsSigningActive] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [commitCreated, setCommitCreated] = useState(false); // Track if commit was already created
  const signatureRef = useRef<any>(null);
  const modalSignatureRef = useRef<any>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const { width, height } = Dimensions.get('window');
  const isWeb = Platform.OS === 'web';

  // Signature canvas handlers
  const handleSignature = (signature: string) => {
    setSignature(signature);
    setIsSignatureValid(true);
    setIsSigningActive(false);
  };

  const handleEmpty = () => {
    setIsSignatureValid(false);
    setIsSigningActive(false);
  };

  const handleClear = () => {
    if (isWeb) {
      clearWebCanvas();
    } else {
      signatureRef.current?.clearSignature();
    }
    setSignature('');
    setIsSignatureValid(false);
    setIsSigningActive(false);
  };

  const handleBeginSigning = () => {
    setIsSigningActive(true);
  };

  const handleEndSigning = () => {
    setIsSigningActive(false);
  };

  // Modal signature handlers
  const handleModalSignature = (signature: string) => {
    setSignature(signature);
    setIsSignatureValid(true);
    setShowSignatureModal(false);
  };

  const handleModalEmpty = () => {
    setIsSignatureValid(false);
  };

  const handleModalClear = () => {
    modalSignatureRef.current?.clearSignature();
    setSignature('');
    setIsSignatureValid(false);
    // The existing signature overlay will automatically disappear since signature is now empty
  };

  const handleModalSave = () => {
    // Manually trigger signature capture
    if (modalSignatureRef.current) {
      modalSignatureRef.current.readSignature();
    } else {
    }
  };

  const openSignatureModal = () => {
    setShowSignatureModal(true);
  };

  // Load existing signature when modal opens
  useEffect(() => {
    if (showSignatureModal && signature && modalSignatureRef.current) {
      // Small delay to ensure the modal is fully rendered
      setTimeout(() => {
        try {
          // Since SignatureCanvas doesn't support loading existing signatures,
          // we'll use the webStyle to show the existing signature as background
          if (modalSignatureRef.current) {
            // The existing signature will be shown via CSS background in webStyle
          }
        } catch (error) {
        }
      }, 100);
    }
  }, [showSignatureModal, signature]);

  const closeSignatureModal = () => {
    setShowSignatureModal(false);
  };

  // Web-specific signature handling
  const setupWebCanvas = () => {
    if (!isWeb || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let isDrawing = false;
    let hasDrawn = false;

    // Ensure canvas has proper dimensions
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width || width - 80;
    canvas.height = 150;

    // Set up drawing context with white background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    const getEventPos = (e: MouseEvent | TouchEvent) => {
      const rect = canvas.getBoundingClientRect();
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
      return {
        x: clientX - rect.left,
        y: clientY - rect.top
      };
    };

    const startDrawing = (e: MouseEvent | TouchEvent) => {
      e.preventDefault();
      isDrawing = true;
      hasDrawn = true;
      setIsSigningActive(true);
      const pos = getEventPos(e);
      ctx.beginPath();
      ctx.moveTo(pos.x, pos.y);
    };

    const draw = (e: MouseEvent | TouchEvent) => {
      if (!isDrawing) return;
      e.preventDefault();
      const pos = getEventPos(e);
      ctx.lineTo(pos.x, pos.y);
      ctx.stroke();
    };

    const stopDrawing = (e?: MouseEvent | TouchEvent) => {
      if (!isDrawing) return;
      if (e) e.preventDefault();
      isDrawing = false;
      setIsSigningActive(false);
      if (hasDrawn) {
        const dataURL = canvas.toDataURL();
        setSignature(dataURL);
        setIsSignatureValid(true);
      }
    };

    // Mouse events
    canvas.addEventListener('mousedown', startDrawing, { passive: false });
    canvas.addEventListener('mousemove', draw, { passive: false });
    canvas.addEventListener('mouseup', stopDrawing, { passive: false });
    canvas.addEventListener('mouseout', stopDrawing, { passive: false });

    // Touch events
    canvas.addEventListener('touchstart', startDrawing, { passive: false });
    canvas.addEventListener('touchmove', draw, { passive: false });
    canvas.addEventListener('touchend', stopDrawing, { passive: false });

    return () => {
      canvas.removeEventListener('mousedown', startDrawing);
      canvas.removeEventListener('mousemove', draw);
      canvas.removeEventListener('mouseup', stopDrawing);
      canvas.removeEventListener('mouseout', stopDrawing);
      canvas.removeEventListener('touchstart', startDrawing);
      canvas.removeEventListener('touchmove', draw);
      canvas.removeEventListener('touchend', stopDrawing);
    };
  };

  const clearWebCanvas = () => {
    if (!isWeb || !canvasRef.current) return;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      // Clear and reset with white background
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      setSignature('');
      setIsSignatureValid(false);
    }
  };

  // Set up web canvas on mount
  useEffect(() => {
    if (isWeb && canvasRef.current) {
      // Small delay to ensure canvas is rendered
      const timer = setTimeout(() => {
        const cleanup = setupWebCanvas();
        return cleanup;
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isWeb]);

  // Additional effect to ensure canvas is properly sized
  useEffect(() => {
    if (isWeb && canvasRef.current) {
      const canvas = canvasRef.current;
      const rect = canvas.getBoundingClientRect();

      // Set canvas internal dimensions
      canvas.width = rect.width || width - 80;
      canvas.height = 150;

      // Set up drawing context
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // Clear canvas with white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }
    }
  }, [isWeb, width]);

  // Helper functions to get display labels
  const getEvidenceLabel = (type: string) => {
    return EVIDENCE_OPTIONS.find(opt => opt.key === type)?.label || type;
  };

  const getFrequencyLabel = (freq: string) => {
    return FREQUENCY_OPTIONS.find(opt => opt.key === freq)?.label || freq;
  };

  const getRecipientLabel = (recipient: string) => {
    return RECIPIENT_OPTIONS.find(opt => opt.key === recipient)?.label || recipient;
  };

  const getStrictnessLabel = (level: string) => {
    return STRICTNESS_OPTIONS.find(opt => opt.key === level)?.label || level;
  };

  // Generate sentence-like commitment summary (like step 1)
  const generateSentenceSummary = () => {
    const evidenceLabel = getEvidenceLabel(formData.evidenceType);
    const frequencyLabel = getFrequencyLabel(formData.reportingFrequency).toLowerCase();
    const recipientLabel = getRecipientLabel(formData.recipient);
    const strictnessLabel = formData.programPreferences?.strictnessLevel
      ? getStrictnessLabel(formData.programPreferences.strictnessLevel).toLowerCase()
      : 'reasonable';

    let timingText = '';
    if (formData.timingType === 'before' && formData.beforeTime) {
      timingText = ` before ${formData.beforeTime}`;
    } else if (formData.timingType === 'after' && formData.afterTime) {
      timingText = ` after ${formData.afterTime}`;
    } else if (formData.timingType === 'between' && formData.startTime && formData.endTime) {
      timingText = ` between ${formData.startTime} and ${formData.endTime}`;
    }

    let frequencyText = frequencyLabel;
    if (formData.reportingFrequency === 'weekly' && formData.timesPerWeek && formData.timesPerWeek > 1) {
      frequencyText = `${formData.timesPerWeek} times per week`;
    } else if (formData.reportingFrequency === 'monthly' && formData.timesPerMonth && formData.timesPerMonth > 1) {
      frequencyText = `${formData.timesPerMonth} times per month`;
    }

    let reminderText = '';
    if (formData.programPreferences?.reminderChannels && formData.programPreferences.reminderChannels.length > 0) {
      const channels = formData.programPreferences.reminderChannels.join(', ');
      reminderText = ` You will be reminded through ${channels}.`;
    }

    const stakeText = ` and I stake $${totalStake.toFixed(2)} going to ${recipientLabel}`;

    return `I will provide ${evidenceLabel} as proof of my commitment to ${formData.commitment} ${frequencyText}${timingText}${stakeText}.${reminderText} This commitment will be enforced with ${strictnessLabel} level of strictness.`;
  };

  // Generate detailed breakdown data for visual display
  const getSummaryData = () => {
    const evidenceLabel = getEvidenceLabel(formData.evidenceType);
    const frequencyLabel = getFrequencyLabel(formData.reportingFrequency).toLowerCase();
    const recipientLabel = getRecipientLabel(formData.recipient);
    const strictnessLabel = formData.programPreferences?.strictnessLevel
      ? getStrictnessLabel(formData.programPreferences.strictnessLevel).toLowerCase()
      : 'reasonable';

    let timingText = '';
    if (formData.timingType === 'before' && formData.beforeTime) {
      timingText = ` before ${formData.beforeTime}`;
    } else if (formData.timingType === 'after' && formData.afterTime) {
      timingText = ` after ${formData.afterTime}`;
    } else if (formData.timingType === 'between' && formData.startTime && formData.endTime) {
      timingText = ` between ${formData.startTime} and ${formData.endTime}`;
    }

    let frequencyText = frequencyLabel;
    if (formData.reportingFrequency === 'weekly' && formData.timesPerWeek && formData.timesPerWeek > 1) {
      frequencyText = `${formData.timesPerWeek} times per week`;
    } else if (formData.reportingFrequency === 'monthly' && formData.timesPerMonth && formData.timesPerMonth > 1) {
      frequencyText = `${formData.timesPerMonth} times per month`;
    }

    const reminderChannels = formData.programPreferences?.reminderChannels || [];
    const hasStake = true; // All recipients now require money at stake

    return {
      evidenceLabel,
      commitment: formData.commitment,
      frequencyText,
      timingText,
      reminderChannels,
      strictnessLabel,
      hasStake,
      stakeAmount: totalStake,
      recipientLabel,
    };
  };

  // Payment handlers
  // Helper function to add commitment to calendar after successful payment
  const addCommitmentToCalendar = async (commitData: any) => {
    try {
      // Check if calendar integration is supported
      if (!calendarService.isSupported()) {
        return;
      }

      // Check permissions
      const permissionResult = await calendarService.getCalendarPermissions();
      if (!permissionResult.success || !permissionResult.data?.granted) {
        // Don't request permissions automatically - let user do it manually later
        return;
      }

      // Create calendar event from commitment data
      const calendarEvent = createCommitmentCalendarEvent(commitData);

      // Create the event
      const result = await calendarService.createEvent(calendarEvent);

      if (result.success && result.data) {
        // Update the commitment with calendar info
        await firestoreService.commits.updateCommit(commitData.id, {
          calendar: {
            eventId: result.data,
            addedAt: new Date().toISOString(),
            isAdded: true,
          }
        });
      }
    } catch (error) {
      // Silently fail calendar integration - don't interrupt the main flow
      console.warn('Calendar integration failed:', error);
    }
  };

  const handleStripePayment = async () => {
    if (!isSignatureValid) {
      setModalMessage('Please provide your signature before proceeding.');
      setShowErrorModal(true);
      return;
    }
    if (!termsAgreed) {
      setModalMessage('Please agree to the terms and conditions before proceeding.');
      setShowErrorModal(true);
      return;
    }

    // Prevent multiple submissions
    if (processingPayment || commitCreated) {
      return;
    }

    setProcessingPayment(true);
    const userId = await getId();

    if (!userId) {
      setModalMessage('User ID not found. Please try again.');
      setShowErrorModal(true);
      setProcessingPayment(false);
      return;
    }

    try {
      const paymentData: PaymentData = {
        amount: totalStake * 100, // Convert to cents
        currency: 'usd',
        description: `Custom Stake - ${formData.commitment}`,
        programId: 'custom-stake', // Custom stakes don't have program IDs
        userId: userId,
      };

      const result = await processStripePayment(paymentData);

      if (result.success) {
        setModalMessage("Your payment has been processed and your commitment has been created.\n\n⏰ Scheduling setup is running in the background. Your reminders and daily checks will be active shortly!");
        setShowSuccessModal(true);
      } else {
        setModalMessage(result.error || "An unknown error occurred.");
        setShowErrorModal(true);
      }
    } catch (error) {
      setModalMessage("An unexpected error occurred during payment processing.");
      setShowErrorModal(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleGooglePayPayment = async () => {
    if (!isSignatureValid) {
      setModalMessage('Please provide your signature before proceeding.');
      setShowErrorModal(true);
      return;
    }
    if (!termsAgreed) {
      setModalMessage('Please agree to the terms and conditions before proceeding.');
      setShowErrorModal(true);
      return;
    }

    // Prevent multiple submissions
    if (processingPayment || commitCreated) {
      return;
    }

    setProcessingPayment(true);
    const userId = await getId();

    if (!userId) {
      setModalMessage('User ID not found. Please try again.');
      setShowErrorModal(true);
      setProcessingPayment(false);
      return;
    }

    try {
      const paymentData: PaymentData = {
        amount: totalStake * 100, // Convert to cents
        currency: 'usd',
        description: `Custom Stake - ${formData.commitment}`,
        programId: 'custom-stake',
        userId: userId,
      };

      const result = await processGooglePayPayment(paymentData);

      if (result.success) {
        setModalMessage("Your payment has been processed and your commitment has been created.\n\n⏰ Scheduling setup is running in the background. Your reminders and daily checks will be active shortly!");
        setShowSuccessModal(true);
      } else {
        setModalMessage(result.error || "An unknown error occurred.");
        setShowErrorModal(true);
      }
    } catch (error) {
      setModalMessage("An unexpected error occurred during payment processing.");
      setShowErrorModal(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleApplePayPayment = async () => {
    if (!isSignatureValid) {
      setModalMessage('Please provide your signature before proceeding.');
      setShowErrorModal(true);
      return;
    }
    if (!termsAgreed) {
      setModalMessage('Please agree to the terms and conditions before proceeding.');
      setShowErrorModal(true);
      return;
    }

    // Prevent multiple submissions
    if (processingPayment || commitCreated) {
      return;
    }

    setProcessingPayment(true);
    const userId = await getId();

    if (!userId) {
      setModalMessage('User ID not found. Please try again.');
      setShowErrorModal(true);
      setProcessingPayment(false);
      return;
    }

    try {
      const paymentData: PaymentData = {
        amount: totalStake * 100, // Convert to cents
        currency: 'usd',
        description: `Custom Stake - ${formData.commitment}`,
        programId: 'custom-stake',
        userId: userId,
      };

      const result = await processApplePayPayment(paymentData);

      if (result.success) {
        setModalMessage("Your payment has been processed and your commitment has been created.");
        setShowSuccessModal(true);
      } else {
        setModalMessage(result.error || "An unknown error occurred.");
        setShowErrorModal(true);
      }
    } catch (error) {
      setModalMessage("An unexpected error occurred during payment processing.");
      setShowErrorModal(true);
    } finally {
      setProcessingPayment(false);
    }
  };



  const handleJoinForFree = async () => {
    if (!isSignatureValid) {
      setModalMessage('Please provide your signature before proceeding.');
      setShowErrorModal(true);
      return;
    }
    if (!termsAgreed) {
      setModalMessage('Please agree to the terms and conditions before proceeding.');
      setShowErrorModal(true);
      return;
    }

    // Prevent multiple submissions
    if (processingPayment || commitCreated) {
      return;
    }

    setProcessingPayment(true);

    try {
      const userId = await getId();
      if (!userId) {
        setModalMessage('User ID not found. Please sign in and try again.');
        setShowErrorModal(true);
        setProcessingPayment(false);
        return;
      }

      // Prepare commit data for Firestore (same as regular flow - respect user's recipient choice)
      // Filter out undefined values to prevent Firebase errors
      const baseCommitData = {
        commitment: formData.commitment,
        evidenceType: formData.evidenceType,
        reportingFrequency: formData.reportingFrequency,
        startDate: formData.startDate?.toISOString ? formData.startDate.toISOString() : formData.startDate,
        endDate: formData.endDate?.toISOString ? formData.endDate.toISOString() : formData.endDate,
        recipient: formData.recipient, // Keep user's selected recipient (don't override!)
        amountPerReport: formData.amountPerReport, // Keep user's entered amount
        totalStake: totalStake, // Keep calculated total stake
        referee: formData.referee,
        reportingSchedule: {
          frequency: formData.reportingFrequency,
          ...(formData.reportingFrequency === 'weekly' && formData.timesPerWeek && {
            ...(formData.startDate && { dayOfWeek: new Date(formData.startDate).getDay() }),
            timesPerWeek: formData.timesPerWeek
          }),
          ...(formData.reportingFrequency === 'monthly' && formData.timesPerMonth && {
            ...(formData.startDate && { dayOfMonth: new Date(formData.startDate).getDate() }),
            timesPerMonth: formData.timesPerMonth
          })
        }
      };

      // Add optional fields only if they have valid values (not undefined or null)
      const commitData: any = { ...baseCommitData };

      // Add weekLength only for frequencies that need it and when it has a value
      if (formData.reportingFrequency !== 'once' && formData.weekLength !== undefined && formData.weekLength !== null) {
        commitData.weekLength = formData.weekLength;
      }

      // Add timing fields only if they have values
      if (formData.timingType) commitData.timingType = formData.timingType;
      if (formData.beforeTime) commitData.beforeTime = formData.beforeTime;
      if (formData.afterTime) commitData.afterTime = formData.afterTime;
      if (formData.startTime) commitData.startTime = formData.startTime;
      if (formData.endTime) commitData.endTime = formData.endTime;

      // Add evidence-specific fields only if they have values
      if (formData.location) commitData.location = formData.location;
      if (formData.locationData) commitData.locationData = formData.locationData;
      if (formData.appName) commitData.appName = formData.appName;
      if (formData.stravaActivity) commitData.stravaActivity = formData.stravaActivity;

      // Add program preferences only if they exist
      if (formData.programPreferences) commitData.programPreferences = formData.programPreferences;

      // Add frequency-specific fields only if they have values
      if (formData.timesPerWeek !== undefined && formData.timesPerWeek !== null) {
        commitData.timesPerWeek = formData.timesPerWeek;
      }
      if (formData.timesPerMonth !== undefined && formData.timesPerMonth !== null) {
        commitData.timesPerMonth = formData.timesPerMonth;
      }

      // Save commit to Firestore
      const result = await firestoreService.commits.createFullCommit(userId, commitData);

      if (result.success) {
        setCommitCreated(true); // Mark that commit was created

        // Add to calendar if possible
        if (result.data) {
          const createdCommit = { ...commitData, id: result.data };
          addCommitmentToCalendar(createdCommit);
        }

        const baseMessage = totalStake > 0
          ? `Your commitment has been created successfully!\n\nStake Amount: $${totalStake.toFixed(2)}\nNo payment required - accountability only.`
          : 'Your commitment has been created successfully!\n\nNo money at stake - accountability only.';

        const schedulingMessage = '\n\n⏰ Scheduling setup is running in the background. Your reminders and daily checks will be active shortly!';
        setModalMessage(baseMessage + schedulingMessage);
        setShowSuccessModal(true);
        // Do NOT call onSubmit() here - we've already created the commit
      } else {
        console.error('Failed to save free commit:', result.error);
        setModalMessage('Failed to save your commitment. Please try again.');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error in handleJoinForFree:', error);
      setModalMessage('An unexpected error occurred. Please try again.');
      setShowErrorModal(true);
    } finally {
      setProcessingPayment(false);
    }
  };

  const styles = {
    container: {
      flex: 1,
      paddingHorizontal: 12, // Match PreferencesFeaturesForm padding
      paddingVertical: 20,
    },
    summaryContainer: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 12, // Match PreferencesFeaturesForm padding
      marginBottom: 16, // Match PreferencesFeaturesForm spacing
      borderWidth: 1,
      borderColor: colors.border,
    },
    summaryTitle: {
      fontSize: 18,
      fontFamily: 'MontserratBold', // Match app's font family
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center' as const,
    },
    summaryText: {
      fontSize: 16,
      lineHeight: 24,
      color: colors.text,
      textAlign: 'center' as const,
    },
    sentenceSummaryContainer: {
      backgroundColor: colors.background,
      borderRadius: 12, // Match step 1 styling
      padding: 20, // Match step 1 padding
      borderWidth: 1,
      borderColor: colors.border,
      marginVertical: 10, // Match step 1 spacing
    },
    sentenceSummaryText: {
      fontSize: 18, // Match step 1 sentence text
      fontFamily: 'MontserratBold', // Match step 1 sentence text
      color: colors.text,
      lineHeight: 32, // Match step 1 sentence text
      textAlign: 'center' as const,
      marginVertical: 4, // Match step 1 sentence text
    },
    summarySection: {
      marginBottom: 16,
    },
    summaryRow: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 12,
      paddingHorizontal: 16,
    },
    summaryIcon: {
      marginRight: 12,
      width: 24,
      alignItems: 'center' as const,
    },
    summaryLabel: {
      fontSize: 14,
      fontFamily: 'MontserratMedium', // Match app's font family
      color: colors.textSecondary,
      flex: 1,
    },
    summaryValue: {
      fontSize: 14,
      fontFamily: 'MontserratSemiBold', // Match app's font family
      color: colors.text,
      flex: 2,
      textAlign: 'right' as const,
    },
    summaryHighlight: {
      backgroundColor: '#FFEB3B20',
      borderRadius: 6,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    summaryHighlightText: {
      color: '#F57F17',
      fontFamily: 'MontserratBold', // Match app's font family
    },
    summaryDivider: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: 12,
      marginHorizontal: 16,
    },
    signatureContainer: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 12, // Match app's padding
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: 16, // Match app's spacing
    },
    signatureTitle: {
      fontSize: 18,
      fontFamily: 'MontserratBold', // Match app's font family
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center' as const,
    },
    signatureSubtitle: {
      fontSize: 14,
      fontFamily: 'MontserratRegular', // Match app's font family
      color: colors.textSecondary,
      marginBottom: 16,
      textAlign: 'center' as const,
      lineHeight: 20,
    },
    signatureCanvasContainer: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: '#ffffff',
      height: 150,
      marginBottom: 10,
      overflow: 'hidden' as const,
      position: 'relative' as const,
    },
    signatureCanvas: {
      flex: 1,
      borderRadius: 8,
      width: '100%',
      height: 150,
      backgroundColor: '#ffffff',
    },
    webCanvas: {
      width: '100%',
      height: 150,
      borderRadius: 8,
      border: '1px solid #e0e0e0',
      cursor: 'crosshair',
      backgroundColor: '#ffffff',
      display: 'block',
    },
    signatureActions: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
      marginBottom: 10,
    },
    clearButton: {
      backgroundColor: colors.surface,
      borderRadius: 6,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderWidth: 1,
      borderColor: colors.border,
    },
    clearButtonText: {
      color: colors.text,
      fontSize: 12,
      fontFamily: 'MontserratMedium', // Match app's font family
    },
    signatureStatus: {
      fontSize: 12,
      fontFamily: 'MontserratMedium', // Match app's font family
    },
    signatureStatusValid: {
      color: '#4CAF50',
    },
    signatureStatusInvalid: {
      color: colors.textSecondary,
    },
    buttonContainer: {
      flexDirection: 'row' as const,
      gap: 12,
      marginTop: 24, // Match PreferencesFeaturesForm spacing
    },
    cancelButton: {
      flex: 1,
      backgroundColor: colors.surface,
      borderRadius: 12, // Match app's border radius
      paddingVertical: 16,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      borderWidth: 1,
      borderColor: colors.border,
    },
    cancelButtonText: {
      color: colors.text,
      fontSize: 16,
      fontFamily: 'MontserratBold', // Match app's font family
    },
    submitButton: {
      flex: 2,
      backgroundColor: colors.primary, // Use theme primary color
      borderRadius: 12, // Match app's border radius
      paddingVertical: 16,
      alignItems: 'center' as const,
      flexDirection: 'row' as const,
      justifyContent: 'center' as const,
      gap: 8,
      shadowColor: colors.primary, // Match PreferencesFeaturesForm shadow
      shadowOffset: {
        width: 0,
        height: 3,
      },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 8,
    },
    submitButtonDisabled: {
      backgroundColor: colors.border,
      opacity: 0.6,
      shadowOpacity: 0,
      elevation: 0,
    },
    submitButtonText: {
      color: '#000000', // Match PreferencesFeaturesForm text color
      fontSize: 16,
      fontFamily: 'MontserratBold', // Match app's font family
    },
    submitButtonTextDisabled: {
      color: colors.textSecondary,
    },
    // Terms and Conditions styles
    termsContainer: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 12,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    termsTitle: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: colors.text,
      marginBottom: 12,
      textAlign: 'center' as const,
    },
    termsCheckbox: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 16,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 2,
      borderColor: colors.border,
      marginRight: 12,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    checkboxChecked: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    termsText: {
      flex: 1,
      fontSize: 14,
      fontFamily: 'MontserratRegular',
      color: colors.text,
      lineHeight: 20,
    },
    termsLink: {
      color: colors.primary,
      textDecorationLine: 'underline' as const,
    },
    // Payment button styles
    paymentButtonsContainer: {
      marginTop: 16,
      gap: 12,
    },
    paymentButton: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      padding: 15,
      borderRadius: 12,
      gap: 10,
    },
    stripeButton: {
      backgroundColor: '#6772E5',
    },
    googlePayButton: {
      backgroundColor: '#4285F4',
    },
    applePayButton: {
      backgroundColor: '#000',
    },
    freeJoinButton: {
      backgroundColor: '#4CAF50', // Green color for free option
    },
    paymentButtonDisabled: {
      backgroundColor: colors.border,
      opacity: 0.6,
    },
    paymentButtonText: {
      color: '#fff',
      fontSize: 16,
      fontFamily: 'MontserratBold',
    },
    paymentButtonTextDisabled: {
      color: colors.textSecondary,
    },
    freeJoinContainer: {
      marginTop: 20,
      marginBottom: 10,
      alignItems: 'center' as const,
    },
    freeJoinText: {
      fontSize: 14,
      fontFamily: 'MontserratMedium',
      color: colors.textSecondary,
      marginBottom: 12,
      textAlign: 'center' as const,
    },
    // Modal signature styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    modalContainer: {
      backgroundColor: colors.background,
      borderRadius: 12,
      padding: 20,
      margin: 20,
      width: width - 40,
      maxHeight: height * 0.8,
    },
    modalHeader: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: colors.text,
    },
    modalCloseButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: colors.surface,
    },
    modalSignatureContainer: {
      height: 350,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: '#ffffff',
      marginBottom: 20,
      overflow: 'hidden' as const,
    },
    modalSignatureCanvas: {
      flex: 1,
      borderRadius: 8,
    },
    existingSignatureOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      pointerEvents: 'none' as const,
    },
    existingSignatureImage: {
      width: '90%' as const,
      height: '70%' as const,
      opacity: 0.3,
    },
    existingSignatureText: {
      position: 'absolute' as const,
      bottom: 10,
      fontSize: 12,
      color: colors.textSecondary,
      fontFamily: 'MontserratRegular',
      textAlign: 'center' as const,
      backgroundColor: 'rgba(255,255,255,0.8)',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 4,
    },
    modalActions: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
    },
    modalClearButton: {
      backgroundColor: colors.surface,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 10,
      borderWidth: 1,
      borderColor: colors.border,
    },
    modalClearButtonText: {
      color: colors.text,
      fontSize: 14,
      fontFamily: 'MontserratMedium',
    },
    modalSaveButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingHorizontal: 20,
      paddingVertical: 10,
    },
    modalSaveButtonText: {
      color: '#000',
      fontSize: 14,
      fontFamily: 'MontserratBold',
    },
    // Signature display styles
    signatureDisplay: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      backgroundColor: '#ffffff',
      height: 120,
      marginBottom: 10,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      overflow: 'hidden' as const,
    },
    signatureImage: {
      flex: 1,
      width: '100%' as const,
      height: 120,
      resizeMode: 'contain' as const,
      backgroundColor: '#ffffff',
    },
    signatureButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingHorizontal: 16,
      paddingVertical: 12,
      alignItems: 'center' as const,
      marginBottom: 10,
    },
    signatureButtonText: {
      color: '#000',
      fontSize: 14,
      fontFamily: 'MontserratBold',
    },
    // Success/Error Modal styles
    modalContent: {
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    modalMessage: {
      fontSize: 16,
      fontFamily: 'MontserratRegular',
      textAlign: 'center' as const,
      lineHeight: 24,
    },
    modalButtons: {
      padding: 20,
      paddingTop: 10,
    },
    modalButton: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center' as const,
    },
    modalButtonText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: '#000',
    },
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      style={styles.container}
      scrollEnabled={!isSigningActive}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      nestedScrollEnabled={false}
      bounces={false}
      overScrollMode="never"
      contentInsetAdjustmentBehavior="never"
    >
      {/* Summary Section */}
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Commitment Summary</Text>

        {/* Sentence-like Summary */}
        <View style={styles.sentenceSummaryContainer}>
          <Text style={styles.sentenceSummaryText}>
            {generateSentenceSummary()}
          </Text>
        </View>
      </View>

      {/* Signature Section */}
      <View style={styles.signatureContainer}>
        <Text style={styles.signatureTitle}>Digital Signature</Text>
        <Text style={styles.signatureSubtitle}>
          Please sign below to confirm your commitment to this stake.
        </Text>

        {isWeb ? (
          // Web version - inline canvas
          <>
            <View style={styles.signatureCanvasContainer}>
              <canvas
                ref={canvasRef}
                width={width - 80}
                height={150}
                style={styles.webCanvas}
              />
            </View>

            <View style={styles.signatureActions}>
              <TouchableOpacity
                style={styles.clearButton}
                onPress={handleClear}
                activeOpacity={0.7}
              >
                <Text style={styles.clearButtonText}>Clear Signature</Text>
              </TouchableOpacity>

              <Text style={[
                styles.signatureStatus,
                isSignatureValid ? styles.signatureStatusValid : styles.signatureStatusInvalid
              ]}>
                {isSignatureValid ? '✓ Signature captured' : 'Please sign above'}
              </Text>
            </View>
          </>
        ) : (
          // Mobile version - modal signature
          <>
            {isSignatureValid && signature ? (
              // Show signature preview
              <View style={styles.signatureDisplay}>
                <Image
                  source={{ uri: signature }}
                  style={styles.signatureImage}
                />
              </View>
            ) : (
              // Show placeholder
              <View style={styles.signatureDisplay}>
                <MaterialIcons name="edit" size={32} color={colors.textSecondary} />
                <Text style={[styles.signatureStatus, styles.signatureStatusInvalid]}>
                  No signature captured
                </Text>
              </View>
            )}

            <TouchableOpacity
              style={styles.signatureButton}
              onPress={openSignatureModal}
              activeOpacity={0.7}
            >
              <Text style={styles.signatureButtonText}>
                {isSignatureValid ? 'Update Signature' : 'Add Signature'}
              </Text>
            </TouchableOpacity>

            <Text style={[
              styles.signatureStatus,
              { textAlign: 'center' as const },
              isSignatureValid ? styles.signatureStatusValid : styles.signatureStatusInvalid
            ]}>
              {isSignatureValid ? '✓ Signature captured' : 'Signature required'}
            </Text>
          </>
        )}
      </View>

      {/* Terms and Conditions */}
      <View style={styles.termsContainer}>
        <Text style={styles.termsTitle}>Terms & Conditions</Text>
        <TouchableOpacity
          style={styles.termsCheckbox}
          onPress={() => setTermsAgreed(!termsAgreed)}
          activeOpacity={0.7}
        >
          <View style={[styles.checkbox, termsAgreed && styles.checkboxChecked]}>
            {termsAgreed && (
              <MaterialIcons name="check" size={16} color="#000" />
            )}
          </View>
          <Text style={styles.termsText}>
            I agree to the{" "}
            <Text style={styles.termsLink}>
              Terms and Conditions
            </Text>
          </Text>
        </TouchableOpacity>
      </View>

      {/* Payment Options */}
          <View style={styles.paymentButtonsContainer}>
            {/* Stripe Payment - Available on all platforms */}
            <TouchableOpacity
              style={[
                styles.paymentButton,
                styles.stripeButton,
                (!termsAgreed || !isSignatureValid) && styles.paymentButtonDisabled,
              ]}
              onPress={handleStripePayment}
              disabled={!termsAgreed || !isSignatureValid || processingPayment}
              activeOpacity={0.7}
            >
              {processingPayment ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <MaterialCommunityIcons name="credit-card" size={24} color="#fff" />
                  <Text style={[
                    styles.paymentButtonText,
                    (!termsAgreed || !isSignatureValid) && styles.paymentButtonTextDisabled,
                  ]}>
                    Create Commit - Pay with Card
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {/* Apple Pay - iOS only */}
            {Platform.OS === 'ios' && (
              <TouchableOpacity
                style={[
                  styles.paymentButton,
                  styles.applePayButton,
                  (!termsAgreed || !isSignatureValid) && styles.paymentButtonDisabled,
                ]}
                onPress={handleApplePayPayment}
                disabled={!termsAgreed || !isSignatureValid || processingPayment}
                activeOpacity={0.7}
              >
                {processingPayment ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <MaterialCommunityIcons name="apple" size={24} color="#fff" />
                    <Text style={[
                      styles.paymentButtonText,
                      (!termsAgreed || !isSignatureValid) && styles.paymentButtonTextDisabled,
                    ]}>
                      Create Commit - Apple Pay
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            )}

            {/* Google Pay - Android only */}
            {Platform.OS === 'android' && (
              <TouchableOpacity
                style={[
                  styles.paymentButton,
                  styles.googlePayButton,
                  (!termsAgreed || !isSignatureValid) && styles.paymentButtonDisabled,
                ]}
                onPress={handleGooglePayPayment}
                disabled={!termsAgreed || !isSignatureValid || processingPayment}
                activeOpacity={0.7}
              >
                {processingPayment ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <MaterialCommunityIcons name="google" size={24} color="#fff" />
                    <Text style={[
                      styles.paymentButtonText,
                      (!termsAgreed || !isSignatureValid) && styles.paymentButtonTextDisabled,
                    ]}>
                      Create Commit - Google Pay
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            )}
          </View>

          {/* Join for Free Button */}
          <View style={styles.freeJoinContainer}>
            <Text style={styles.freeJoinText}>
              Or create commitment without payment:
            </Text>
            <TouchableOpacity
              style={[
                styles.paymentButton,
                styles.freeJoinButton,
                (!termsAgreed || !isSignatureValid) && styles.paymentButtonDisabled,
              ]}
              onPress={handleJoinForFree}
              disabled={!termsAgreed || !isSignatureValid || processingPayment}
              activeOpacity={0.7}
            >
              {processingPayment ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <MaterialCommunityIcons name="heart-outline" size={24} color="#fff" />
                  <Text style={[
                    styles.paymentButtonText,
                    (!termsAgreed || !isSignatureValid) && styles.paymentButtonTextDisabled,
                  ]}>
                    Create Commitment - No Payment Required
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>

          {/* Back Button */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onCancel}
              activeOpacity={0.7}
            >
              <Text style={styles.cancelButtonText}>Back</Text>
            </TouchableOpacity>
          </View>


      {/* Signature Modal - Mobile Only */}
      {!isWeb && (
        <Modal
          visible={showSignatureModal}
          transparent
          animationType="slide"
          onRequestClose={closeSignatureModal}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Digital Signature</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={closeSignatureModal}
                  activeOpacity={0.7}
                >
                  <MaterialIcons name="close" size={20} color={colors.text} />
                </TouchableOpacity>
              </View>

              <View style={styles.modalSignatureContainer}>
                {signature && (
                  <View style={styles.existingSignatureOverlay}>
                    <Image
                      source={{ uri: signature }}
                      style={styles.existingSignatureImage}
                      resizeMode="contain"
                    />
                    <Text style={styles.existingSignatureText}>
                      Draw over to update or clear to start fresh
                    </Text>
                  </View>
                )}
                <SignatureCanvas
                  key={`signature-${showSignatureModal}-${signature ? 'with-sig' : 'no-sig'}`}
                  ref={modalSignatureRef}
                  onOK={handleModalSignature}
                  onEmpty={handleModalEmpty}
                  onBegin={handleBeginSigning}
                  onEnd={handleEndSigning}
                  descriptionText="Sign above"
                  clearText=""
                  confirmText=""
                  autoClear={false}
                  backgroundColor="rgba(255,255,255,0.8)"
                  penColor="#000000"
                  minWidth={2}
                  maxWidth={4}
                  trimWhitespace={true}
                  androidHardwareAccelerationDisabled={true}
                  webStyle={`
                    .m-signature-pad {
                      box-shadow: none;
                      border: none;
                      margin: 0;
                      padding: 0;
                      width: 100%;
                      height: 100%;
                      display: flex;
                      flex-direction: column;
                      font-family: 'MontserratRegular', -apple-system, BlinkMacSystemFont, sans-serif;
                    }
                    .m-signature-pad--body {
                      border: none;
                      margin: 0;
                      padding: 0;
                      flex: 1;
                      position: relative;
                    }
                    .m-signature-pad--footer {
                      display: none;
                    }
                    body, html {
                      margin: 0;
                      padding: 0;
                      background-color: white;
                      width: 100%;
                      height: 100%;
                      overflow: hidden;
                      -webkit-user-select: none;
                      -moz-user-select: none;
                      -ms-user-select: none;
                      user-select: none;
                    }
                    canvas {
                      border-radius: 8px;
                      width: 100% !important;
                      height: 100% !important;
                      display: block;
                      position: relative;
                      background-color: ${signature ? 'rgba(255,255,255,0.8)' : 'white'} !important;
                      -webkit-user-select: none;
                      -moz-user-select: none;
                      -ms-user-select: none;
                      user-select: none;
                      touch-action: none;
                      min-height: 200px;
                      border: 1px solid #e0e0e0;
                    }
                    * {
                      -webkit-touch-callout: none;
                      -webkit-user-select: none;
                      -khtml-user-select: none;
                      -moz-user-select: none;
                      -ms-user-select: none;
                      user-select: none;
                      touch-action: none;
                    }
                  `}
                  style={styles.modalSignatureCanvas}
                />
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.modalClearButton}
                  onPress={handleModalClear}
                  activeOpacity={0.7}
                >
                  <Text style={styles.modalClearButtonText}>Clear</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.modalSaveButton}
                  onPress={handleModalSave}
                  activeOpacity={0.7}
                >
                  <Text style={styles.modalSaveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>

            </View>
          </View>
        </Modal>
      )}

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSuccessModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>Success!</Text>
            </View>

            <View style={styles.modalContent}>
              <Text style={[styles.modalMessage, { color: colors.text }]}>
                {modalMessage}
              </Text>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={() => {
                  setShowSuccessModal(false);
                  // Navigate to progress page to maintain flow after successful commit creation
                  router.replace('/(tabs)/progress');
                  // Only call onSubmit if commit wasn't already created via "Join for Free"
                  if (!commitCreated) {
                    onSubmit();
                  }
                  // If commit was already created, just close the modal - no further action needed
                }}
                activeOpacity={0.7}
              >
                <Text style={styles.modalButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Error Modal */}
      <Modal
        visible={showErrorModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowErrorModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>Error</Text>
            </View>

            <View style={styles.modalContent}>
              <Text style={[styles.modalMessage, { color: colors.text }]}>
                {modalMessage}
              </Text>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={() => setShowErrorModal(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.modalButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};
