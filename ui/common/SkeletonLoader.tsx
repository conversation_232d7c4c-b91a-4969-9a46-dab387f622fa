import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import { useTheme } from "../../shared/contexts/ThemeContext";

interface SkeletonLoaderProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

/**
 * Individual skeleton element with wavy animation
 */
export const SkeletonElement: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 8,
  style,
}) => {
  const { colors } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Create a wavy, shimmer-like animation
    const animation = Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      })
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  // Create a smooth shimmer effect
  const shimmerOpacity = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.1, 0.3, 0.1],
  });

  const shimmerTranslateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-200, 200],
  });

  return (
    <View
      style={[
        {
          width,
          height,
          borderRadius,
          overflow: 'hidden',
          backgroundColor: colors.surface,
        },
        style,
      ]}
    >
      {/* Base skeleton background with gradient-like effect */}
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: colors.border,
          opacity: 0.2,
        }}
      />

      {/* Shimmer wave effect */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: -50,
          right: -50,
          bottom: 0,
          backgroundColor: colors.primary,
          opacity: shimmerOpacity,
          transform: [{ translateX: shimmerTranslateX }, { skewX: '-20deg' }],
        }}
      />
    </View>
  );
};




