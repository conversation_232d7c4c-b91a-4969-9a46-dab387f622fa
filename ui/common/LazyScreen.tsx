import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useTheme } from "../../shared/contexts/ThemeContext";

interface LazyScreenProps {
  children: React.ReactNode;
  delay?: number;
}

/**
 * LazyScreen component that delays rendering of heavy screens
 * to allow navigation transitions to complete smoothly
 */
export const LazyScreen: React.FC<LazyScreenProps> = ({ 
  children, 
  delay = 100 
}) => {
  const [isReady, setIsReady] = useState(false);
  const { colors } = useTheme();

  useEffect(() => {
    // Use requestAnimationFrame to ensure the transition has started
    const frame1 = requestAnimationFrame(() => {
      const frame2 = requestAnimationFrame(() => {
        // Add a small delay to let the transition animation complete
        const timer = setTimeout(() => {
          setIsReady(true);
        }, delay);

        return () => clearTimeout(timer);
      });

      return () => cancelAnimationFrame(frame2);
    });

    return () => cancelAnimationFrame(frame1);
  }, [delay]);

  if (!isReady) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: colors.background,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return <>{children}</>;
};
