import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, StyleSheet, Image } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { AccustomText } from "../../shared/components/AccustomText";

const CoFounderChat = () => {
  const { colors, isDark, designSystem } = useTheme();
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi! I'm one of the co-founders of Accustom. I'm here to help you with any questions or feedback you might have about the app. What's on your mind?",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');



  const send = () => {
    if (!input.trim()) return;
    setMessages(prev => [...prev, {
      id: Date.now(),
      text: input,
      isUser: true,
      timestamp: new Date()
    }]);
    setInput('');
    setTimeout(() => {
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        text: "Thanks for your message! This is just a frontend demo for now. We'll be implementing real chat functionality soon. Your feedback is valuable to us!",
        isUser: false,
        timestamp: new Date()
      }]);
    }, 1000);
  };

  const styles = createStyles(colors, isDark, designSystem);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.messagesContainer}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
      >
        {messages.map(msg => {
          if (msg.isUser) {
            return (
              <View key={msg.id} style={styles.userMessageContainer}>
                <TouchableOpacity activeOpacity={1} style={styles.userMessageButton}>
                  <LinearGradient
                    colors={[colors.primary, colors.warning]}
                    style={styles.userMessageGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Text style={styles.userMessageText}>
                      {msg.text}
                    </Text>
                    <Text style={styles.userTimestamp}>
                      {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            );
          } else {
            return (
              <View key={msg.id} style={styles.coFounderMessageRow}>
                <Image
                  source={require("../../assets/images/cofounder_pic.jpeg")}
                  style={styles.coFounderAvatar}
                />
                <View style={styles.coFounderMessageContainer}>
                  <BlurView intensity={40} tint="dark" style={styles.coFounderMessageBlur}>
                    <View style={styles.coFounderMessage}>
                      <AccustomText
                        accustomSize="inherit"
                        style={styles.coFounderMessageText}
                      >
                        {msg.text}
                      </AccustomText>
                      <Text style={styles.coFounderTimestamp}>
                        {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </Text>
                    </View>
                  </BlurView>
                </View>
              </View>
            );
          }
        })}
      </ScrollView>

      <View style={styles.inputSection}>
        <View style={styles.inputWrapper}>
          <BlurView intensity={30} tint="dark" style={styles.inputBlur}>
            <TextInput
              style={styles.textInput}
              value={input}
              onChangeText={setInput}
              placeholder="Message"
              placeholderTextColor={colors.textMuted}
              multiline
              selectionColor={colors.primary}
            />
          </BlurView>
        </View>

        <TouchableOpacity
          onPress={send}
          disabled={!input.trim()}
          activeOpacity={0.8}
          style={[styles.sendButton, !input.trim() && styles.sendButtonDisabled]}
        >
          <LinearGradient
            colors={input.trim()
              ? [colors.primary, colors.warning]
              : [colors.textMuted, colors.textMuted]
            }
            style={styles.sendButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <MaterialIcons
              name="send"
              size={20}
              color={input.trim() ? '#000000' : colors.text}
            />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const createStyles = (colors: any, isDark: boolean, designSystem: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: designSystem.spacing.lg,
  },
  messagesContent: {
    paddingVertical: designSystem.spacing.xl,
    flexGrow: 1,
  },

  // User message styles
  userMessageContainer: {
    alignSelf: 'flex-end',
    marginBottom: designSystem.spacing.lg,
    maxWidth: '85%',
  },
  userMessageButton: {
    borderRadius: 24,
    borderBottomRightRadius: 8,
  },
  userMessageGradient: {
    padding: designSystem.spacing.lg,
    borderRadius: 24,
    borderBottomRightRadius: 8,
  },
  userMessageText: {
    color: '#000000',
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: 'MontserratBold',
    lineHeight: 20,
    letterSpacing: designSystem.typography.letterSpacing.normal,
  },
  userTimestamp: {
    color: '#000000',
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: 'MontserratRegular',
    marginTop: designSystem.spacing.xs,
    opacity: 0.8,
  },

  // Co-founder message styles
  coFounderMessageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: designSystem.spacing.lg,
    alignSelf: 'flex-start',
    maxWidth: '85%',
  },
  coFounderAvatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
    marginRight: designSystem.spacing.sm,
    marginBottom: 2,
    borderWidth: 1.5,
    borderColor: 'rgba(255, 215, 0, 0.6)', // Gold border around avatar
  },
  coFounderMessageContainer: {
    flex: 1,
    borderRadius: 24,
    borderBottomLeftRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)', // Subtle gold border
  },
  coFounderMessageBlur: {
    borderRadius: 24,
    borderBottomLeftRadius: 8,
    overflow: 'hidden',
    backgroundColor: isDark ? 'rgba(255, 215, 0, 0.05)' : 'rgba(255, 215, 0, 0.03)', // Gold tint
  },
  coFounderMessage: {
    padding: designSystem.spacing.lg,
  },
  coFounderMessageText: {
    color: colors.text,
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: 'MontserratRegular',
    lineHeight: 20,
    letterSpacing: designSystem.typography.letterSpacing.normal,
  },
  coFounderTimestamp: {
    color: colors.textMuted,
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: 'MontserratRegular',
    marginTop: designSystem.spacing.xs,
    opacity: 0.7,
  },

  // Input section styles
  inputSection: {
    flexDirection: 'row',
    paddingHorizontal: designSystem.spacing.xl,
    paddingVertical: designSystem.spacing.lg,
    paddingBottom: designSystem.spacing.xl,
    backgroundColor: 'transparent',
    alignItems: 'flex-end',
    gap: designSystem.spacing.md,
  },
  inputWrapper: {
    flex: 1,
    borderRadius: 28,
    overflow: 'hidden',
  },
  inputBlur: {
    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)',
    borderRadius: 28,
  },
  textInput: {
    paddingHorizontal: designSystem.spacing.lg + 2,
    paddingVertical: designSystem.spacing.md + 2,
    color: colors.text,
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: 'MontserratRegular',
    maxHeight: 120,
    letterSpacing: designSystem.typography.letterSpacing.normal,
    minHeight: 48,
  },

  // Send button styles
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  sendButtonDisabled: {
    opacity: 0.6,
  },
  sendButtonGradient: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CoFounderChat;