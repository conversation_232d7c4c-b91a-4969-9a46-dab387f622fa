import React, { useState, useEffect } from "react";
import { Text, View, StyleSheet, TouchableOpacity, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { getId } from "../../lib/utils/variables";
import BreathingDot from "../pages/Progress/BreathingDot";
import { firestoreService, addSubscription, removeSubscription } from "../../lib/services/database";
import { useTheme } from "../../shared/contexts/ThemeContext";
import { AccustomText } from "../../shared/components/AccustomText";
import { BaseBottomSheet } from "../../shared/components/modals";
import CoFounderChat from "./CoFounderChat";

export default function Header() {
  const router = useRouter();
  const { colors } = useTheme();
  const [hasUnreadNotifications, setHasUnreadNotifications] = useState(false);
  const [isChatModalVisible, setIsChatModalVisible] = useState(false);

  useEffect(() => {
    const setupNotificationListener = async () => {
      try {
        const userEmail = await getId();
        if (!userEmail) return;

        const unsubscribe = firestoreService.notifications.subscribeToUnreadCount(
          userEmail,
          (count) => setHasUnreadNotifications(count > 0)
        );

        addSubscription('header-notifications', unsubscribe);
      } catch (error) {
        console.error("Error setting up notification listener:", error);
      }
    };

    setupNotificationListener();

    // Cleanup subscription on unmount
    return () => removeSubscription('header-notifications');
  }, []);

  return (
    <View style={[styles.headerContainer, { backgroundColor: colors.header }]}>
      <View style={styles.header}>
        {/* Chat Icon - Left Side */}
        <View style={styles.chatContainer}>
          <TouchableOpacity
            style={styles.chatButton}
            onPress={() => setIsChatModalVisible(true)}
          >
            <Ionicons name="chatbubble-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <AccustomText
          accustomSize="medium"
          style={[styles.headerText, { color: colors.text }]}
        >
          Accustom
        </AccustomText>

        {/* Notifications - Right Side */}
        <View style={styles.notificationContainer}>
          {hasUnreadNotifications && (
            <View style={styles.dotContainer}>
              <BreathingDot />
            </View>
          )}
          <TouchableOpacity
            style={styles.notificationButton}
            onPress={() => router.push("/Notifications")}
          >
            <Ionicons name="notifications-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Co-Founder Chat Modal */}
      <BaseBottomSheet
        visible={isChatModalVisible}
        onClose={() => setIsChatModalVisible(false)}
        title="Chat with Co-Founder"
        maxHeight={90}
        minHeight={70}
        scrollable={false}
        noPadding={true}
      >
        <CoFounderChat />
      </BaseBottomSheet>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    // backgroundColor will be set dynamically based on theme
    zIndex: 100, // Ensure header stays above other elements
  },
  header: {
    paddingTop: Platform.OS === 'web' ? 20 : Platform.OS === 'ios' ? 50 : 45, // No padding for web, iOS: 50, Android: 45
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: 12,
    position: "relative",
    minHeight: Platform.OS === 'web' ? 60 : Platform.OS === 'ios' ? 80 : 75, // Responsive height
  },
  headerText: {
    // color will be set dynamically, font styling handled by AccustomHeader
    textAlign: "center",
    // Ensure text doesn't get cut off on smaller screens
    maxWidth: '80%',
    flexShrink: 1,
  },
  chatContainer: {
    position: "absolute",
    left: 15,
    top: Platform.OS === 'web' ? 18 : Platform.OS === 'ios' ? 49 : 44, // Responsive positioning
  },
  chatButton: {
    padding: 5,
  },
  notificationContainer: {
    position: "absolute",
    right: 15,
    top: Platform.OS === 'web' ? 18 : Platform.OS === 'ios' ? 49 : 44, // Responsive positioning
  },
  notificationButton: {
    padding: 5,
  },
  dotContainer: {
    position: "absolute",
    right: 3,
    top: 3,
    zIndex: 1,
  }
});
