import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { useTheme } from "../../shared/contexts/ThemeContext";

interface LazySkeletonScreenProps {
  children: React.ReactNode;
  skeleton: React.ReactNode;
  delay?: number;
}

/**
 * LazySkeletonScreen component that shows a skeleton loader
 * while the actual content is being prepared
 */
export const LazySkeletonScreen: React.FC<LazySkeletonScreenProps> = ({ 
  children, 
  skeleton,
  delay = 200
}) => {
  const [isReady, setIsReady] = useState(false);
  const { colors } = useTheme();

  useEffect(() => {
    // Use multiple animation frames to ensure smooth transition
    const frame1 = requestAnimationFrame(() => {
      const frame2 = requestAnimationFrame(() => {
        const frame3 = requestAnimationFrame(() => {
          // Final delay to let the transition animation complete
          const timer = setTimeout(() => {
            setIsReady(true);
          }, delay);

          return () => clearTimeout(timer);
        });

        return () => cancelAnimationFrame(frame3);
      });

      return () => cancelAnimationFrame(frame2);
    });

    return () => cancelAnimationFrame(frame1);
  }, [delay]);

  if (!isReady) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: colors.background,
      }}>
        {skeleton}
      </View>
    );
  }

  return <>{children}</>;
};
