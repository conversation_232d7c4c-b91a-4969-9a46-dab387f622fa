import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Dimensions,
  Platform,
  Alert,
} from 'react-native';
import * as Location from 'expo-location';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";

// Conditional import for expo-maps (only available in development builds)
let Map: any = null;
let Marker: any = null;
let mapsAvailable = false;

try {
  const expoMaps = require('expo-maps');
  Map = expoMaps.Map;
  Marker = expoMaps.Marker;
  mapsAvailable = true;
} catch (error) {
  // expo-maps not available, falling back to text input
  mapsAvailable = false;
}

interface LocationData {
  title: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface LocationPickerProps {
  visible: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationData) => void;
  initialLocation?: LocationData;
}

interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

const { width, height } = Dimensions.get('window');

const LocationPicker: React.FC<LocationPickerProps> = ({
  visible,
  onClose,
  onLocationSelect,
  initialLocation,
}) => {
  const { colors } = useTheme();
  const [region, setRegion] = useState<MapRegion>({
    latitude: initialLocation?.latitude || 37.78825,
    longitude: initialLocation?.longitude || -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(
    initialLocation || null
  );
  const [locationTitle, setLocationTitle] = useState(initialLocation?.title || '');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  useEffect(() => {
    if (visible && !initialLocation) {
      getCurrentLocation();
    }
  }, [visible]);

  const getCurrentLocation = async () => {
    try {
      setIsLoadingLocation(true);
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is required to show your current location on the map.'
        );
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const newRegion = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      
      setRegion(newRegion);
      
      // Get address for current location
      const address = await getAddressFromCoordinates(
        location.coords.latitude,
        location.coords.longitude
      );
      
      setSelectedLocation({
        title: locationTitle || 'Current Location',
        address,
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Error', 'Unable to get your current location');
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const getAddressFromCoordinates = async (latitude: number, longitude: number): Promise<string> => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        return `${address.street || ''} ${address.city || ''} ${address.region || ''} ${address.postalCode || ''}`.trim();
      }
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    } catch (error) {
      console.error('Error getting address:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  };

  const handleMapPress = async (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    
    const address = await getAddressFromCoordinates(latitude, longitude);
    
    setSelectedLocation({
      title: locationTitle || 'Selected Location',
      address,
      latitude,
      longitude,
    });
  };

  const handleConfirm = () => {
    if (!selectedLocation) {
      Alert.alert('Error', 'Please select a location on the map');
      return;
    }

    if (!locationTitle.trim()) {
      Alert.alert('Error', 'Please enter a title for this location');
      return;
    }

    const finalLocation = {
      ...selectedLocation,
      title: locationTitle.trim(),
    };

    onLocationSelect(finalLocation);
    onClose();
  };

  const handleClose = () => {
    setLocationTitle(initialLocation?.title || '');
    setSelectedLocation(initialLocation || null);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <MaterialIcons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Select Location</Text>
          <TouchableOpacity onPress={handleConfirm} style={styles.confirmButton}>
            <Text style={[styles.confirmButtonText, { color: colors.primary }]}>Done</Text>
          </TouchableOpacity>
        </View>

        {/* Location Title Input */}
        <View style={[styles.inputContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
          <Text style={[styles.inputLabel, { color: colors.text }]}>Location Title</Text>
          <TextInput
            style={[styles.titleInput, { color: colors.text, borderColor: colors.border }]}
            placeholder="Enter a name for this location"
            placeholderTextColor={colors.textMuted}
            value={locationTitle}
            onChangeText={setLocationTitle}
            maxLength={100}
          />
        </View>

        {/* Address Display */}
        {selectedLocation && (
          <View style={[styles.addressContainer, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
            <Text style={[styles.addressLabel, { color: colors.textMuted }]}>Address</Text>
            <Text style={[styles.addressText, { color: colors.text }]}>{selectedLocation.address}</Text>
          </View>
        )}

        {/* Map or Fallback */}
        <View style={styles.mapContainer}>
          {mapsAvailable && Map ? (
            <Map
              style={styles.map}
              initialRegion={region}
              onPress={handleMapPress}
              showsUserLocation={true}
            >
              {selectedLocation && Marker && (
                <Marker
                  coordinate={{
                    latitude: selectedLocation.latitude,
                    longitude: selectedLocation.longitude,
                  }}
                  title={selectedLocation.title}
                  description={selectedLocation.address}
                />
              )}
            </Map>
          ) : (
            <View style={styles.fallbackContainer}>
              <MaterialIcons name="map" size={48} color={colors.textMuted} />
              <Text style={[styles.fallbackTitle, { color: colors.text }]}>
                Map Not Available
              </Text>
              <Text style={[styles.fallbackText, { color: colors.textMuted }]}>
                Maps are only available in development builds. Please enter your location manually.
              </Text>
              <TextInput
                style={[styles.fallbackInput, { color: colors.text, borderColor: colors.border }]}
                placeholder="Enter address or location"
                placeholderTextColor={colors.textMuted}
                value={selectedLocation?.address || ''}
                onChangeText={(text) => {
                  setSelectedLocation({
                    title: locationTitle || 'Manual Location',
                    address: text,
                    latitude: 0,
                    longitude: 0,
                  });
                }}
                multiline
              />
            </View>
          )}
        </View>

        {/* Instructions */}
        <View style={[styles.instructionsContainer, { backgroundColor: colors.surface }]}>
          <Text style={[styles.instructionsText, { color: colors.textMuted }]}>
            {mapsAvailable
              ? "Tap on the map to select a location, or use the location button to use your current position."
              : "Enter a location title and address manually. Maps are available in development builds."
            }
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  confirmButton: {
    padding: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  addressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  addressLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  instructionsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  instructionsText: {
    fontSize: 12,
    textAlign: 'center',
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  fallbackTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  fallbackText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  fallbackInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    width: '100%',
    minHeight: 80,
    textAlignVertical: 'top',
  },
});

export default LocationPicker;
