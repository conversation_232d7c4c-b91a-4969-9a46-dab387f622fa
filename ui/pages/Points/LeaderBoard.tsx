// Leaderboard.tsx
import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Platform } from "react-native";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "../../../shared/contexts/ThemeContext";

// Platform-specific blur configuration
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 15,
      tint: 'default' as const
    };
  } else if (Platform.OS === 'android') {
    return {
      intensity: 15, // Slightly reduced blur for Android
      tint: 'default' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  } else {
    // iOS - Further reduced blur intensity for better visibility
    return {
      intensity: 18,
      tint: 'systemMaterial' as const
    };
  }
};

type LeaderboardProps = {
  monthlyLeaderboard: any[];
  fortnightlyLeaderboard: any[];
};

const Leaderboard: React.FC<LeaderboardProps> = ({
  monthlyLeaderboard,
  fortnightlyLeaderboard,
}) => {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors, designSystem, isDark);
  const [activeTab, setActiveTab] = useState<"Monthly" | "Fortnightly" | "Rewards">("Monthly");

  // Dummy data for coming soon display
  const dummyLeaderboard = [
    { rank: 1, name: "Player 1", points: 1250 },
    { rank: 2, name: "Player 2", points: 1100 },
    { rank: 3, name: "Player 3", points: 950 },
    { rank: 4, name: "You", points: 800 },
  ];

  const getRewards = (rank: number, type: "Monthly" | "Fortnightly") => {
    const rewards = {
      Monthly: { 1: 100, 2: 90, 3: 80 },
      Fortnightly: { 1: 50, 2: 45, 3: 40 }
    };
    return rewards[type][rank as keyof typeof rewards[typeof type]] || 0;
  };

  const renderContent = () => {
    if (activeTab === "Rewards") {
      return (
        <View style={styles.tableContainer}>
          <View style={styles.tableHeader}>
            <Text style={styles.tableHeaderText}>Rank</Text>
            <Text style={styles.tableHeaderText}>Monthly</Text>
            <Text style={styles.tableHeaderText}>Fortnightly</Text>
          </View>
          {[1, 2, 3].map((rank) => (
            <View key={rank} style={styles.tableRow}>
              <Text style={styles.tableCell}>{rank}</Text>
              <Text style={styles.tableCell}>${getRewards(rank, "Monthly")}</Text>
              <Text style={styles.tableCell}>${getRewards(rank, "Fortnightly")}</Text>
            </View>
          ))}
        </View>
      );
    }

    // Use dummy data for coming soon display
    const data = dummyLeaderboard;
    const entriesToShow = data;

    return (
      <View style={[styles.tableContainer, { backgroundColor: colors.surface }]}>
        <View style={[styles.tableHeader, { backgroundColor: colors.surface, borderBottomColor: colors.separator }]}>
          <Text style={[styles.tableHeaderText, { color: colors.primary }]}>Rank</Text>
          <Text style={[styles.tableHeaderText, { color: colors.primary }]}>Name</Text>
          <Text style={[styles.tableHeaderText, { color: colors.primary }]}>Points</Text>
        </View>
        {entriesToShow.map((entry, index) => (
          <View key={entry.rank} style={[
            styles.tableRow,
            { borderBottomColor: colors.separator },
            entry.name === "You" && { backgroundColor: colors.primary }
          ]}>
            <Text style={[
              styles.tableCell,
              { color: colors.text },
              entry.name === "You" && { color: '#000', fontFamily: "MontserratBold" }
            ]}>
              {entry.rank}
            </Text>
            <Text style={[
              styles.tableCell,
              { color: colors.text },
              entry.name === "You" && { color: '#000', fontFamily: "MontserratBold" }
            ]}>
              {entry.name}
            </Text>
            <Text style={[
              styles.tableCell,
              { color: colors.text },
              entry.name === "You" && { color: '#000', fontFamily: "MontserratBold" }
            ]}>
              {entry.points}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.leaderboardContainer, { backgroundColor: colors.surface }]}>
      <Text style={[styles.leaderboardTitle, { color: colors.text, backgroundColor: colors.surface }]}>Leaderboard</Text>
      <View style={[styles.tabsContainer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "Monthly" && { ...styles.activeTab, borderBottomColor: colors.primary }]}
          onPress={() => setActiveTab("Monthly")}
        >
          <Text style={[styles.tabText, { color: colors.text }, activeTab === "Monthly" && { color: colors.primary }]}>
            Monthly
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "Fortnightly" && { ...styles.activeTab, borderBottomColor: colors.primary }]}
          onPress={() => setActiveTab("Fortnightly")}
        >
          <Text style={[styles.tabText, activeTab === "Fortnightly" && styles.activeTabText]}>
            Fortnightly
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "Rewards" && styles.activeTab]}
          onPress={() => setActiveTab("Rewards")}
        >
          <View style={styles.tabWithTag}>
            <Text style={[styles.tabText, activeTab === "Rewards" && styles.activeTabText]}>
              Rewards
            </Text>
            <View style={styles.comingSoonTag}>
              <LinearGradient
                colors={['#FFD700', '#DAA520']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.goldTagBorder}
              >
                <View style={styles.tagInner}>
                  <Text style={styles.comingSoonTagText}>Soon</Text>
                </View>
              </LinearGradient>
            </View>
          </View>
        </TouchableOpacity>
      </View>
      <View style={styles.contentWrapper}>
        {renderContent()}

        {/* Coming Soon Overlay - Only show for Monthly and Fortnightly tabs */}
        {activeTab !== "Rewards" && (
          <BlurView
            intensity={getBlurConfig().intensity}
            tint={getBlurConfig().tint}
            {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
            style={styles.comingSoonOverlay}
          >
            <View style={styles.comingSoonContent}>
              <View style={styles.comingSoonLabel}>
                <LinearGradient
                  colors={['#FFD700', '#DAA520', '#B8860B']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.goldBorder}
                >
                  <View style={styles.labelInner}>
                    <Text style={styles.comingSoonLabelText}>
                      COMING SOON
                    </Text>
                  </View>
                </LinearGradient>
              </View>
              <View style={[styles.descriptionBackground, { backgroundColor: colors.background }]}>
                <Text style={[styles.comingSoonDescription, { color: colors.textSecondary }]}>
                  Compete with others and climb the leaderboard
                </Text>
              </View>
            </View>
          </BlurView>
        )}
      </View>
    </View>
  );
};

const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  leaderboardContainer: {
    marginVertical: 2,
    marginHorizontal: 10,
    backgroundColor: colors.card,
    borderRadius: 8,
    overflow: "hidden",
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  leaderboardTitle: {
    // color and backgroundColor will be set dynamically based on theme
    fontSize: 18,
    fontFamily: "MontserratBold",
    textAlign: "center",
    paddingVertical: 5,
  },
  tabsContainer: {
    flexDirection: "row",
    // backgroundColor will be set dynamically based on theme
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 3,
    borderBottomColor: "#FFEB3B",
  },
  tabText: {
    color: "white",
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
  activeTabText: {
    color: "#FFEB3B",
  },
  tableContainer: {
    backgroundColor: "#2A2A2A",
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: "#1e1e1e",
    paddingVertical: 6,
    borderBottomWidth: 1,
    borderBottomColor: "#444",
  },
  tableHeaderText: {
    flex: 1,
    color: "#FFEB3B",
    fontSize: 14,
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
  tableRow: {
    flexDirection: "row",
    paddingVertical: 4,
    borderBottomWidth: 1,
    borderBottomColor: "#444",
  },
  tableCell: {
    flex: 1,
    color: "white",
    fontSize: 14,
    textAlign: "center",
  },
  highlightRow: {
    backgroundColor: "#FFEB3B",
  },
  highlightRowText: {
    color: "black",
    fontFamily: "MontserratBold",
  },
  separator: {
    height: 8,
    backgroundColor: "#1e1e1e",
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#444",
  },
  contentWrapper: {
    position: "relative",
  },
  comingSoonOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    overflow: "hidden",
  },
  comingSoonContent: {
    alignItems: "center",
    padding: 16,
  },
  comingSoonLabel: {
    marginBottom: 8,
    ...designSystem.shadows.sm,
    shadowColor: colors.primary,
    transform: [{ rotate: '-1deg' }],
  },
  goldBorder: {
    borderRadius: 12,
    padding: 1.5,
  },
  labelInner: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 9,
    backgroundColor: '#1a1a1a',
    borderWidth: 0,
  },
  comingSoonLabelText: {
    fontSize: 9,
    fontFamily: "MontserratBold",
    fontWeight: "700",
    letterSpacing: 0.8,
    color: '#FFD700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0.3, height: 0.3 },
    textShadowRadius: 0.5,
  },
  descriptionBackground: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginTop: 6,
    borderWidth: 1,
    borderColor: 'rgba(218, 165, 32, 0.2)',
  },
  comingSoonDescription: {
    fontSize: 12,
    textAlign: "center",
    fontFamily: "Montserrat",
  },
  tabWithTag: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
  },
  comingSoonTag: {
    position: "absolute",
    top: -6,
    right: -10,
    alignItems: "center",
    ...designSystem.shadows.sm,
    shadowColor: colors.primary,
  },
  goldTagBorder: {
    borderRadius: 8,
    padding: 1,
  },
  tagInner: {
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 6,
    backgroundColor: '#1a1a1a',
    borderWidth: 0,
  },
  comingSoonTagText: {
    fontSize: 8,
    fontFamily: "MontserratBold",
    fontWeight: "700",
    color: '#FFD700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0.5, height: 0.5 },
    textShadowRadius: 1,
  },
});

export default Leaderboard;
