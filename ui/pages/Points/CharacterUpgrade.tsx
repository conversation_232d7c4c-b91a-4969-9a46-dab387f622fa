// CharacterUpgrade.tsx
import React, { useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  Image,
  Dimensions,
  StyleSheet,
  Platform,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { useTheme } from "../../../shared/contexts/ThemeContext";

// Platform-specific blur configuration
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 15,
      tint: 'default' as const
    };
  } else if (Platform.OS === 'android') {
    return {
      intensity: 15, // Slightly reduced blur for Android
      tint: 'default' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  } else {
    // iOS - Further reduced blur intensity for better visibility
    return {
      intensity: 18,
      tint: 'systemMaterial' as const
    };
  }
};

type CharacterUpgradeProps = {
  userPoints: number | null;
};

const characterMilestones = [
  { tier: "🌱 Beginner Tier", points: 0, character: "The Seeker" },
  { points: 3, character: "The Spark" },
  { points: 7, character: "Striker" },
  { points: 14, character: "The Builder" },
  { points: 21, character: "Momentum" },
  { tier: "⚡ Intermediate Tier", points: 30, character: "The Challenger" },
  { points: 45, character: "Ascender" },
  { points: 60, character: "Pioneer" },
  { points: 75, character: "Vanguard" },
  { points: 90, character: "The Resilient" },
  { tier: "🏆 Advanced Tier", points: 100, character: "Fortitude" },
  { points: 120, character: "The Executor" },
  { points: 150, character: "The Architect" },
  { points: 180, character: "Prime" },
  { points: 210, character: "The Unyielding" },
  { tier: "🚀 Legendary Tier", points: 250, character: "The Dominant" },
  { points: 300, character: "Legacy" },
  { points: 365, character: "The Apex" },
  { points: 500, character: "Sovereign" },
  { points: 1000, character: "Eterna (Ultimate Status)" },
];

const avatarImageMap: { [points: number]: any } = {
  0: require("../../../assets/avatars/0.png"),
  3: require("../../../assets/avatars/3.png"),
  7: require("../../../assets/avatars/7.png"),
  14: require("../../../assets/avatars/14.png"),
  21: require("../../../assets/avatars/21.png"),
  30: require("../../../assets/avatars/30.png"),
  45: require("../../../assets/avatars/45.png"),
};

const getAvatarImage = (points: number) => avatarImageMap[points] ?? null;

const CharacterUpgrade: React.FC<CharacterUpgradeProps> = ({ userPoints }) => {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors, designSystem, isDark);
  const scrollViewRef = useRef<ScrollView>(null);

  // Group milestones by tier
  const groupedMilestones: { tier: string; milestones: any[] }[] = [];
  let currentGroup: { tier: string; milestones: any[] } | null = null;
  characterMilestones.forEach((milestone, index) => {
    const milestoneWithIndex = { ...milestone, globalIndex: index };
    if (milestone.tier) {
      if (currentGroup) groupedMilestones.push(currentGroup);
      currentGroup = {
        tier: milestone.tier,
        milestones: [milestoneWithIndex],
      };
    } else {
      if (!currentGroup) {
        currentGroup = { tier: "", milestones: [milestoneWithIndex] };
      } else {
        currentGroup.milestones.push(milestoneWithIndex);
      }
    }
  });
  if (currentGroup) groupedMilestones.push(currentGroup);

  let currentMilestoneGlobalIndex = 0;
  if (userPoints !== null) {
    for (let i = 0; i < characterMilestones.length; i++) {
      if (userPoints >= characterMilestones[i].points) {
        currentMilestoneGlobalIndex = i;
      } else break;
    }
  }

  return (
    <View style={[styles.characterUpgradeContainer, { backgroundColor: colors.surface }]}>
      <Text style={[styles.upgradeTitle, { color: colors.text, backgroundColor: colors.surface }]}>Avatars</Text>
      <View style={styles.contentWrapper}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          ref={scrollViewRef}
        >
          {groupedMilestones.map((group, groupIndex) => (
          <LinearGradient
            key={groupIndex}
            colors={getTierGradientColors(group.tier)}
            style={styles.tierGroupGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.tierGroupInner}>
              <Text style={[styles.tierHeader, { color: colors.primary }]}>{group.tier}</Text>
              <View style={styles.milestonesRow}>
                {group.milestones.map((milestone) => {
                  const isCurrent =
                    milestone.globalIndex === currentMilestoneGlobalIndex;
                  const avatarImage = getAvatarImage(milestone.points);
                  if (avatarImage) {
                    return (
                      <View
                        key={milestone.globalIndex}
                        onLayout={
                          isCurrent
                            ? (event) => {
                                const { x, width } = event.nativeEvent.layout;
                                const screenWidth =
                                  Dimensions.get("window").width;
                                const scrollToX = x - screenWidth / 2 + width / 2;
                                scrollViewRef.current?.scrollTo({
                                  x: scrollToX,
                                  animated: true,
                                });
                              }
                            : undefined
                        }
                        style={[
                          styles.milestoneCard,
                          isCurrent
                            ? styles.currentCard
                            : styles.nonCurrentCard,
                        ]}
                      >
                        <View style={styles.textContainer}>
                          <Text style={[styles.milestonePoints, { color: colors.text }]}>
                            {milestone.points} Points
                          </Text>
                          <Text style={[styles.milestoneCharacter, { color: colors.text }]}>
                            {milestone.character}
                          </Text>
                        </View>
                        <View style={styles.imageContainer}>
                          <Image
                            source={avatarImage}
                            style={styles.imageStyle}
                            resizeMode="cover"
                          />
                        </View>
                      </View>
                    );
                  } else {
                    let gradientColors: readonly [string, string, ...string[]];
                    if (
                      userPoints !== null &&
                      userPoints >= milestone.points
                    ) {
                      gradientColors = isCurrent
                        ? ([
                            "rgba(255,215,0,0.5)",
                            "rgba(255,140,0,0.5)",
                          ] as const)
                        : (["#4CAF50", "#8BC34A"] as const);
                    } else {
                      gradientColors = ["#555", "#333"] as const;
                    }
                    return (
                      <LinearGradient
                        key={milestone.globalIndex}
                        colors={gradientColors}
                        style={[
                          styles.milestoneCard,
                          isCurrent && styles.currentCard,
                        ]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                      >
                        <Text style={[styles.milestonePoints, { color: '#FFF' }]}>
                          {milestone.points} Points
                        </Text>
                        <Text style={[styles.milestoneCharacter, { color: '#FFF' }]}>
                          {milestone.character}
                        </Text>
                      </LinearGradient>
                    );
                  }
                })}
              </View>
            </View>
          </LinearGradient>
        ))}
      </ScrollView>

      {/* Coming Soon Overlay */}
      <BlurView
        intensity={getBlurConfig().intensity}
        tint={getBlurConfig().tint}
        {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
        style={styles.comingSoonOverlay}
      >
        <View style={styles.comingSoonContent}>
          <View style={styles.comingSoonLabel}>
            <LinearGradient
              colors={['#FFD700', '#DAA520', '#B8860B']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.goldBorder}
            >
              <View style={styles.labelInner}>
                <Text style={styles.comingSoonLabelText}>
                  COMING SOON
                </Text>
              </View>
            </LinearGradient>
          </View>
          <View style={[styles.descriptionBackground, { backgroundColor: colors.background }]}>
            <Text style={[styles.comingSoonDescription, { color: colors.textSecondary }]}>
              Unlock unique avatars as you progress
            </Text>
          </View>
        </View>
      </BlurView>
    </View>
    </View>
  );
};

const getTierGradientColors = (tier: string) => {
  switch (tier) {
    case "🌱 Beginner Tier":
      return ["rgba(168,224,99,0.3)", "rgba(86,171,47,0.3)"] as const;
    case "⚡ Intermediate Tier":
      return ["rgba(247,151,30,0.3)", "rgba(255,210,0,0.3)"] as const;
    case "🏆 Advanced Tier":
      return ["rgba(33,147,176,0.3)", "rgba(109,213,237,0.3)"] as const;
    case "🚀 Legendary Tier":
      return ["rgba(255,65,108,0.3)", "rgba(255,75,43,0.3)"] as const;
    default:
      return ["rgba(255,255,255,0.3)", "rgba(204,204,204,0.3)"] as const;
  }
};

const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  characterUpgradeContainer: {
    marginVertical: 2,
    marginHorizontal: 10,
    backgroundColor: colors.card,
    borderRadius: 10,
    paddingVertical: 8,
    paddingHorizontal: 5,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  upgradeTitle: {
    marginBottom: 5,
    color: colors.text,
    fontSize: 18,
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
  tierGroupGradient: {
    marginRight: 10,
    borderRadius: 8,
    padding: 4,
  },
  tierGroupInner: {
    backgroundColor: "rgba(29,29,29,0.8)",
    borderRadius: 6,
    paddingVertical: 4,
    paddingHorizontal: 4,
    alignItems: "center",
  },
  tierHeader: {
    // color will be set dynamically based on theme
    fontSize: 12,
    fontFamily: "MontserratBold",
    marginBottom: 4,
  },
  milestonesRow: {
    flexDirection: "row",
  },
  milestoneCard: {
    width: 130,
    height: 170,
    marginHorizontal: 6,
    borderRadius: 12,
    // backgroundColor will be set dynamically based on theme
    overflow: "hidden",
    flexDirection: "column",
    transform: [{ scale: 0.85 }],
  },
  currentCard: {
    borderWidth: 2.5,
    // borderColor will be set dynamically based on theme
    transform: [{ scale: 1 }],
  },
  nonCurrentCard: {
    opacity: 0.8,
  },
  textContainer: {
    width: "100%",
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    marginBottom: 4,
  },
  milestonePoints: {
    fontSize: 12,
    // color will be set dynamically based on theme
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
  milestoneCharacter: {
    fontSize: 15,
    // color will be set dynamically based on theme
    textAlign: "center",
    fontFamily: "MontserratBold",
    fontWeight: "700",
  },
  imageContainer: {
    flex: 1,
    width: "100%",
  },
  imageStyle: {
    width: "100%",
    height: "100%",
  },
  contentWrapper: {
    position: "relative",
  },
  comingSoonOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    overflow: "hidden",
  },
  comingSoonContent: {
    alignItems: "center",
    padding: 16,
  },
  comingSoonLabel: {
    marginBottom: 8,
    ...designSystem.shadows.sm,
    shadowColor: colors.primary,
    transform: [{ rotate: '1deg' }],
  },
  goldBorder: {
    borderRadius: 12,
    padding: 1.5,
  },
  labelInner: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 9,
    backgroundColor: '#1a1a1a',
    borderWidth: 0,
  },
  comingSoonLabelText: {
    fontSize: 9,
    fontFamily: "MontserratBold",
    fontWeight: "700",
    letterSpacing: 0.8,
    color: '#FFD700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0.3, height: 0.3 },
    textShadowRadius: 0.5,
  },
  descriptionBackground: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginTop: 6,
    borderWidth: 1,
    borderColor: 'rgba(218, 165, 32, 0.2)',
  },
  comingSoonDescription: {
    fontSize: 12,
    textAlign: "center",
    fontFamily: "Montserrat",
  },
});

export default CharacterUpgrade;
