import React, { useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useTheme } from "../../../shared/contexts/ThemeContext";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface SwipeableViewContainerProps {
  useCalendarView: boolean;
  onViewChange: (useCalendar: boolean) => void;
  calendarView: React.ReactNode;
  listView: React.ReactNode;
  style?: any;
}

export const SwipeableViewContainer: React.FC<SwipeableViewContainerProps> = ({
  useCalendarView,
  onViewChange,
  calendarView,
  listView,
  style,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.spring(translateX, {
        toValue: useCalendarView ? 0 : -SCREEN_WIDTH,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [useCalendarView, translateX, opacity]);

  // Gesture handling removed for simplicity
  // Can be added back with react-native-gesture-handler if needed

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.contentContainer,
          {
            opacity,
            transform: [{ translateX }],
          },
        ]}
      >
          {/* Calendar View */}
          <View style={[styles.viewContainer, styles.calendarContainer]}>
            {calendarView}
          </View>

          {/* List View */}
          <View style={[styles.viewContainer, styles.listContainer]}>
            {listView}
          </View>
        </Animated.View>

      {/* Swipe Indicators */}
      <View style={styles.indicatorContainer}>
        <View style={[
          styles.indicator,
          useCalendarView && styles.indicatorActive,
        ]} />
        <View style={[
          styles.indicator,
          !useCalendarView && styles.indicatorActive,
        ]} />
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  contentContainer: {
    flexDirection: 'row',
    flex: 1,
    width: SCREEN_WIDTH * 2,
  },
  viewContainer: {
    width: SCREEN_WIDTH,
    flex: 1,
  },
  calendarContainer: {
    // Calendar-specific styles if needed
  },
  listContainer: {
    // List-specific styles if needed
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    gap: 8,
  },
  indicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.textMuted,
    opacity: 0.3,
  },
  indicatorActive: {
    backgroundColor: colors.primary,
    opacity: 1,
    transform: [{ scale: 1.2 }],
  },
});
