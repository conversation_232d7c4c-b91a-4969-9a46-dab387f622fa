import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../../shared/contexts/ThemeContext";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface TabViewSwitchProps {
  useCalendarView: boolean;
  onToggle: (useCalendar: boolean) => void;
  style?: any;
  programCount?: number;
}

export const TabViewSwitch: React.FC<TabViewSwitchProps> = ({
  useCalendarView,
  onToggle,
  style,
  programCount = 0,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  const slideAnim = useRef(new Animated.Value(useCalendarView ? 0 : 1)).current;

  // Initialize the animation value correctly
  useEffect(() => {
    slideAnim.setValue(useCalendarView ? 0 : 1);
  }, []);

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: useCalendarView ? 0 : 1,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  }, [useCalendarView]);

  const handleToggle = (useCalendar: boolean) => {
    if (useCalendar !== useCalendarView) {
      onToggle(useCalendar);
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.tabContainer}>
        {/* Sliding Indicator */}
        <Animated.View
          style={[
            styles.indicator,
            {
              left: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '50%'],
                extrapolate: 'clamp',
              }),
            },
          ]}
        />

        {/* Calendar Tab */}
        <TouchableOpacity
          style={[
            styles.tab,
            useCalendarView && styles.tabActive,
          ]}
          onPress={() => handleToggle(true)}
          activeOpacity={0.8}
        >
          <View style={styles.tabContent}>
            <MaterialIcons
              name="calendar-today"
              size={18}
              color={useCalendarView ? colors.primary : colors.textMuted}
            />
            <Text
              style={[
                styles.tabText,
                useCalendarView && styles.tabTextActive,
              ]}
            >
              Calendar
            </Text>
            {useCalendarView && (
              <View style={styles.activeIndicator}>
                <View style={styles.activeDot} />
              </View>
            )}
          </View>
        </TouchableOpacity>

        {/* List Tab */}
        <TouchableOpacity
          style={[
            styles.tab,
            !useCalendarView && styles.tabActive,
          ]}
          onPress={() => handleToggle(false)}
          activeOpacity={0.8}
        >
          <View style={styles.tabContent}>
            <MaterialIcons
              name="view-list"
              size={18}
              color={!useCalendarView ? colors.primary : colors.textMuted}
            />
            <Text
              style={[
                styles.tabText,
                !useCalendarView && styles.tabTextActive,
              ]}
            >
              List
            </Text>
            {!useCalendarView && (
              <View style={styles.activeIndicator}>
                <View style={styles.activeDot} />
              </View>
            )}
          </View>
        </TouchableOpacity>
      </View>

      {/* Subtle divider */}
      <View style={styles.divider} />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    marginBottom: 4,
    width: '100%',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: 0,
    position: 'relative',
    height: 40,
    width: '100%',
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    width: '50%',
    height: 3,
    backgroundColor: colors.primary,
    borderTopLeftRadius: 2,
    borderTopRightRadius: 2,
  },
  tab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  tabActive: {
    // Active state handled by indicator
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    position: 'relative',
  },
  tabText: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
  },
  tabTextActive: {
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  activeIndicator: {
    position: 'absolute',
    top: -2,
    right: -8,
  },
  activeDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: colors.primary,
  },

  divider: {
    height: 1,
    backgroundColor: colors.border || colors.surface,
    opacity: 0.3,
  },
});
