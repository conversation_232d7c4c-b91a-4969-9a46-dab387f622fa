import { StyleSheet } from "react-native";
import { Text, View } from "react-native";
import { MaterialIcons } from '@expo/vector-icons';
import CategoryInput from "./CategoryInput";
import { DayTile, ParticipantList, Program } from "../../../shared/types/CommonInterface";
import { useCameraPermissions } from "expo-camera";
import { useTheme } from "../../../shared/contexts/ThemeContext";

interface ContentAreaProps {
    userId: string;
    selectedProgram: Program;
    selectedDay: number;
    currentDay: number;
    days: DayTile[];
    participantsList: ParticipantList[];
    submissionTriggered: boolean;
    loadingStatuses: boolean[];
    submissionTrigger: (submissionTriggered: boolean) => void;
}

export const ContentArea: React.FC<ContentAreaProps> = ({userId, selectedProgram, selectedDay, currentDay, days, participantsList, submissionTriggered, loadingStatuses, submissionTrigger}) => {
    const [permission, requestPermission] = useCameraPermissions();
    const { colors } = useTheme();
    const styles = createStyles(colors);
  
    if (selectedProgram.duration && selectedDay > Number(selectedProgram.duration)) {
    return (
      <View style={[styles.messageContainer, styles.endedContainer]}>
        <MaterialIcons name="flag" size={24} color={colors.textMuted} />
        <Text style={styles.endedTitle}>Program Completed!</Text>
        <Text style={styles.endedSubtitle}>This challenge has reached its end</Text>
      </View>
    );
  }

  const selectedDayTile = days.find((d) => d.day === selectedDay);
  const currentUserSubmissionStatus = participantsList.find(
    (p) => p.id === userId
  )?.status;

  if (selectedDay < currentDay) {
    // Past day: if the day's status is still upcoming, indicate the day was missed.
    if (selectedDayTile?.status === "upcoming") {
      return (
        <View style={[styles.messageContainer, styles.missedContainer]}>
          <MaterialIcons name="schedule" size={28} color={colors.error} />
          <Text style={styles.missedTitle}>Day Missed</Text>
          <Text style={styles.missedSubtitle}>Lock in next time and stay consistent!</Text>
        </View>
      );
    } else if (selectedDayTile?.status === "submitted") {
      return (
        <View style={[styles.messageContainer, styles.submittedContainer]}>
          <MaterialIcons name="hourglass-empty" size={28} color={colors.primary} />
          <Text style={styles.successTitle}>Great job! 🎉</Text>
          <Text style={styles.successSubtitle}>Submission made for this day</Text>
          <Text style={styles.pendingText}>
            Waiting for moderator evaluation
          </Text>
        </View>
      );
    } else if (selectedDayTile?.status === "verified") {
      return (
        <View style={[styles.messageContainer, styles.verifiedContainer]}>
          <MaterialIcons name="verified" size={28} color={colors.success || '#4CAF50'} />
          <Text style={styles.verifiedTitle}>Verified! ✅</Text>
          <Text style={styles.verifiedSubtitle}>
            Your submission was approved. Keep pushing forward!
          </Text>
        </View>
      );
    } else if (selectedDayTile?.status === "bailed") {
      return (
        <View style={[styles.messageContainer, styles.bailedContainer]}>
          <MaterialIcons name="shield" size={28} color={colors.warning || '#FF9800'} />
          <Text style={styles.bailedTitle}>Grace Day Used ⚠️</Text>
          <Text style={styles.bailedSubtitle}>Hope you had a good excuse.</Text>
          <Text style={styles.bailedMessage}>
            We expect you to stay consistent. Stay hard!
          </Text>
        </View>
      );
    }
  } else if (selectedDay === currentDay) {
    // Current day: render the regular submission UI.
    if (currentUserSubmissionStatus === "submitted") {
      return (
        <View style={[styles.messageContainer, styles.submittedContainer]}>
          <MaterialIcons name="hourglass-empty" size={28} color={colors.primary} />
          <Text style={styles.successTitle}>Great job! 🎉</Text>
          <Text style={styles.successSubtitle}>Submission made for today</Text>
          <Text style={styles.pendingText}>
            Waiting for moderator evaluation
          </Text>
        </View>
      );
    } else if (currentUserSubmissionStatus === "verified") {
      return (
        <View style={[styles.messageContainer, styles.verifiedContainer]}>
          <MaterialIcons name="verified" size={28} color={colors.success || '#4CAF50'} />
          <Text style={styles.verifiedTitle}>Verified! ✅</Text>
          <Text style={styles.verifiedSubtitle}>
            Your submission was approved. Keep pushing forward!
          </Text>
        </View>
      );
    } else if (currentUserSubmissionStatus === "bailed") {
      return (
        <View style={[styles.messageContainer, styles.bailedContainer]}>
          <MaterialIcons name="shield" size={28} color={colors.warning || '#FF9800'} />
          <Text style={styles.bailedTitle}>Grace Day Used ⚠️</Text>
          <Text style={styles.bailedSubtitle}>Hope you had a good excuse.</Text>
          <Text style={styles.bailedMessage}>
            We expect you to stay consistent. Stay hard!
          </Text>
        </View>
      );
    } else {
      return (
        <View style={styles.submissionContainer}>
          <CategoryInput
            key={`${selectedProgram?.id}-${selectedDay}`}
            user_id={userId}
            category={selectedProgram?.category}
            program_id={String(selectedProgram?.id)}
            day={selectedDay}
            permissionGranted={permission?.granted}
            requestPermission={requestPermission}
            livesLeft={
              participantsList.find((p) => p.id === userId)?.livesLeft || 0
            }
            livesPurchaseLeft={
              participantsList.find((p) => p.id === userId)?.livesPurchaseLeft ||
              0
            }
            onSubmissionSuccess={() =>
              submissionTrigger(!submissionTriggered)
            }
            isLoading={loadingStatuses[0] || loadingStatuses[1] || loadingStatuses[2]}
          />
        </View>
      );
    }
  } else {
    // Future day: prevent submissions
    return (
      <View style={[styles.messageContainer, styles.futureContainer]}>
        <MaterialIcons name="lock" size={28} color={colors.textMuted} />
        <Text style={styles.futureTitle}>Day Locked</Text>
        <Text style={styles.futureSubtitle}>
          Come back on this day to submit your progress!
        </Text>
      </View>
    );
  }
  return null;
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 4,
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: colors.surface,
    borderRadius: 16,
    alignItems: "center",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: colors.border + '30',
    gap: 8,
  },
  submissionContainer: {
    marginVertical: 4,
  },

  // Program Ended Styles
  endedContainer: {
    borderColor: colors.textMuted + '40',
    backgroundColor: colors.background,
  },
  endedTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
  },
  endedSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
  },

  // Missed Day Styles
  missedContainer: {
    borderColor: colors.error + '40',
    backgroundColor: colors.error + '08',
  },
  missedTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.error,
    textAlign: 'center',
  },
  missedSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    textAlign: 'center',
  },

  // Submitted Styles
  submittedContainer: {
    borderColor: colors.primary + '40',
    backgroundColor: colors.primary + '08',
  },
  successTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    textAlign: 'center',
  },
  successSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    textAlign: 'center',
  },
  pendingText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  // Verified Styles
  verifiedContainer: {
    borderColor: colors.success + '40',
    backgroundColor: colors.success + '10',
  },
  verifiedTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.success,
    textAlign: 'center',
  },
  verifiedSubtitle: {
    fontSize: 15,
    fontFamily: 'MontserratMedium',
    color: colors.text,
    textAlign: 'center',
    lineHeight: 22,
  },

  // Bailed/Grace Day Styles
  bailedContainer: {
    borderColor: colors.warning + '40',
    backgroundColor: colors.warning + '10',
  },
  bailedTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.warning,
    textAlign: 'center',
  },
  bailedSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratMedium',
    color: colors.text,
    textAlign: 'center',
  },
  bailedMessage: {
    fontSize: 13,
    fontFamily: 'MontserratSemiBold',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  // Future Day Styles
  futureContainer: {
    borderColor: colors.textMuted + '40',
    backgroundColor: colors.textMuted + '08',
  },
  futureTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.textMuted,
    textAlign: 'center',
  },
  futureSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});
