import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { firestoreService } from "../../../../lib/services/database";
import { useTheme } from "../../../../shared/contexts/ThemeContext";

import SubmissionStatusBoard from './SubmissionStatusBoard';
import MoneyContainer from './MoneyContainer';
import DisqualifiedStats from './DisqualifiedStats';
import WinnerShare from './WinnerShare';
import TotalEarnings from './TotalEarnings';
import DownloadReport from './DownloadReport';

interface ProgramEndProps {
  programId: string;
  duration: number;
  currentUserId: string;
  betAmount: number;
  totalPlayers: number;
}

export interface Participant {
  id: string;
  fname: string;
  disqualified: boolean;
  livesLeft: number;
  livesPurchaseLeft: number;
  submissions: Record<string, { status: string }>;
}

export interface Winner {
  id: string;
  name: string;
  bailoutsUsed: number;
  sharePercentage: number;
  winnings: number;
}

interface Stats {
  submissionStats: {
    totalSubmissions: number;
    completionRate: number;
  };
  moneyStats: {
    totalPool: number;
    distributedAmount: number;
  };
  disqualifiedStats: {
    count: number;
    loserPool: number;
  };
  winnerShare: {
    amount: number;
    rank?: number;
  };
}

const ProgramEnd: React.FC<ProgramEndProps> = ({
  programId,
  duration,
  currentUserId,
  betAmount,
  totalPlayers,
}) => {
  const router = useRouter();
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [loading, setLoading] = useState(true);
  const [isArchiving, setIsArchiving] = useState(false);

  const [programData, setProgramData] = useState<any>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);

  const [winners, setWinners] = useState<Winner[]>([]);
  const [stats, setStats] = useState<Stats>({
    submissionStats: { totalSubmissions: 0, completionRate: 0 },
    moneyStats: { totalPool: 0, distributedAmount: 0 },
    disqualifiedStats: { count: 0, loserPool: 0 },
    winnerShare: { amount: 0, rank: undefined },
  });

  const [showFullSubmissionBoard, setShowFullSubmissionBoard] = useState(false);
  const [showFullWinnerTable, setShowFullWinnerTable] = useState(false);

  // Fetch everything in one go
  useEffect(() => {
    const fetchAll = async () => {
      try {
        // 1) program doc
        const programResult = await firestoreService.programs.getProgramById(programId);
        if (programResult.success && programResult.data) {
          setProgramData(programResult.data);
        }

        // 2) participants + each one's submissions
        const participantsResult = await firestoreService.participants.getAllParticipants(programId);
        if (participantsResult.success && participantsResult.data) {
          const parts: Participant[] = await Promise.all(
            participantsResult.data.map(async (participant) => {
              const submissionsResult = await firestoreService.submissions.getAllSubmissions(
                programId,
                participant.userId
              );
              const submissions: Record<string, { status: string }> = {};
              if (submissionsResult.success && submissionsResult.data) {
                submissionsResult.data.forEach((submission) => {
                  if (submission.id) {
                    submissions[submission.id] = { status: submission.status ?? 'not_submitted' };
                  }
                });
              }
              return {
                id: participant.userId,
                fname: participant.fname || 'Anonymous',
                disqualified: participant.disqualified ?? false,
                livesLeft: participant.livesLeft ?? 0,
                livesPurchaseLeft: participant.livesPurchaseLeft ?? 0,
                submissions,
              };
            })
          );
          setParticipants(parts);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error loading program end data:', error);
        setLoading(false);
      }
    };
    fetchAll();
  }, [programId]);

  // Once participants are loaded, compute all stats exactly once
  useEffect(() => {
    if (loading || participants.length === 0) return;

    // Submission stats
    const totalSubmissions = participants.reduce((acc, p) => {
      const good = Object.values(p.submissions).filter((s) =>
        ['submitted', 'verified'].includes(s.status.toLowerCase())
      ).length;
      return acc + good;
    }, 0);
    const possible = participants.length * duration;
    const completionRate = possible > 0
      ? Math.round((totalSubmissions / possible) * 10000) / 100
      : 0;

    // Disqualifications
    const disqCount = participants.filter((p) => p.disqualified).length;
    const loserPool = disqCount * betAmount;

    // Money stats
    const totalPool = betAmount * totalPlayers;
    const distributable = loserPool * 0.88;

    // Winners & share
    const rawWinners = participants.filter((p) => !p.disqualified);
    let totalBailouts = 0;
    rawWinners.forEach((p) => {
      const used = 3 - p.livesLeft + (3 - p.livesPurchaseLeft);
      totalBailouts += used;
    });
    const baseShare = rawWinners.length > 0
      ? 100 / rawWinners.length
      : 0;

    const beforeNorm = rawWinners.map((p) => {
      const used = 3 - p.livesLeft + (3 - p.livesPurchaseLeft);
      const penaltyPct = totalBailouts > 0
        ? (used / totalBailouts) * 50
        : 0;
      return {
        id: p.id,
        name: p.fname,
        bailoutsUsed: used,
        shareRaw: baseShare * (1 - penaltyPct / 100),
      };
    });
    const sumRaw = beforeNorm.reduce((acc, w) => acc + w.shareRaw, 0);
    const normalized: Winner[] = beforeNorm
      .map((w) => {
        const sharePct = sumRaw > 0
          ? (w.shareRaw / sumRaw) * 100
          : 0;
        return {
          id: w.id,
          name: w.name,
          bailoutsUsed: w.bailoutsUsed,
          sharePercentage: sharePct,
          winnings: (sharePct / 100) * distributable,
        };
      })
      .sort((a, b) => b.winnings - a.winnings);

    // Current user's prize & rank
    const me = normalized.find((w) => w.id === currentUserId);
    const myAmount = me?.winnings ?? 0;
    const myRank = me
      ? normalized.findIndex((w) => w.id === currentUserId) + 1
      : undefined;

    // Commit all stats
    setWinners(normalized);
    setStats({
      submissionStats: { totalSubmissions, completionRate },
      disqualifiedStats: { count: disqCount, loserPool },
      moneyStats: { totalPool, distributedAmount: distributable },
      winnerShare: { amount: myAmount, rank: myRank },
    });
  }, [loading, participants, duration, betAmount, totalPlayers, currentUserId]);

  if (loading) {
    return (
      <View style={styles.loader}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Program ended</Text>
        <Text style={styles.subtitle}>
          You've successfully made it through! Below are the stats.
        </Text>
      </View>
      
      <View style={styles.sectionContainer}>
        <View style={styles.boardContainer}>
        <SubmissionStatusBoard
          participants={participants}
          duration={duration}
          currentUserId={currentUserId}
          stats={stats.submissionStats}
          compact
          showFullView={showFullSubmissionBoard}
          setShowFullView={setShowFullSubmissionBoard}
        />
        </View>
      </View>
      
      {/* Modal for full submission board view */}
      <Modal
        visible={showFullSubmissionBoard}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFullSubmissionBoard(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Submission Status</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowFullSubmissionBoard(false)}
              >
                <MaterialIcons name="close" size={24} color={colors.primary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalBoardContainer}>
              <SubmissionStatusBoard
                participants={participants}
                duration={duration}
                currentUserId={currentUserId}
                stats={stats.submissionStats}
                compact
                showFullView={showFullSubmissionBoard}
                setShowFullView={setShowFullSubmissionBoard}
              />
            </View>
          </View>
        </View>
      </Modal>
      
      <View style={styles.sectionContainer}>
        <MoneyContainer 
          betAmount={betAmount}
          totalPlayers={totalPlayers}
        />
      </View>

      <View style={styles.sectionContainer}>
        <DisqualifiedStats
          count={stats.disqualifiedStats.count}
          loserPool={stats.disqualifiedStats.loserPool}
        />
      </View>

      <View style={styles.sectionContainer}>
        <WinnerShare
          winners={winners}
          loserPool={stats.disqualifiedStats.loserPool}
          currentUserId={currentUserId}
          showFullTable={showFullWinnerTable}
          setShowFullTable={setShowFullWinnerTable}
        />
      </View>

      <View style={[styles.sectionContainer, { marginTop: 10, marginBottom: 6 }]}>
        <TotalEarnings
          betAmount={betAmount}
          winnings={stats.winnerShare?.amount || 0}
          programId={programId}
          programName={programData?.name || 'Program'}
          currentUserId={currentUserId}
        />
      </View>

      {/* Download and Archive Section with reduced margin */}
      {programData && (
        <View style={[styles.actionButtonsContainer, { marginTop: 0 }]}>
          <View style={styles.buttonWrapper}>
            <DownloadReport
              programId={programId}
              programData={programData}
              stats={stats}
            />
          </View>
          <View style={styles.buttonWrapper}>
            <TouchableOpacity
              style={[styles.archiveButton, { opacity: isArchiving ? 0.6 : 1 }]}
              disabled={isArchiving}
              onPress={async () => {
                if (!isArchiving) {
                  try {
                    setIsArchiving(true);
                    const result = await firestoreService.users.archiveUserProgram(currentUserId, programId);

                    if (result.success) {
                      // Force a complete page reload by navigating to explore then back to progress
                      router.replace('/(tabs)');
                      setTimeout(() => {
                        router.replace('/(tabs)/progress');
                      }, 100);
                    } else {
                      console.error('Failed to archive program:', result.error);
                      setIsArchiving(false);
                    }
                  } catch (error) {
                    console.error('Error archiving program:', error);
                    setIsArchiving(false);
                  }
                }
              }}
            >
              <View style={styles.archiveContent}>
                <Text style={styles.archiveMainText}>
                  {isArchiving ? 'Archiving...' : 'Archive Program'}
                </Text>
                <Text style={styles.archiveSubText}>
                  {isArchiving ? 'please wait' : 'hide from list'}
                </Text>
              </View>
              {isArchiving ? (
                <ActivityIndicator size={20} color={colors.primary} />
              ) : (
                <MaterialIcons name="archive" size={20} color={colors.primary} />
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}

      <TouchableOpacity
        style={styles.infoButton}
        onPress={() => router.push(`/ProgramDetails?programId=${programId}`)}
      >
        <MaterialIcons name="info-outline" size={24} color={colors.primary} />
      </TouchableOpacity>
    </ScrollView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  headerContainer: {
    padding: 12, // Reduced from 16
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    color: colors.primary,
    marginBottom: 4, // Reduced from 8
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12,
    color: colors.text,
    textAlign: 'center',
    fontFamily: 'MontserratRegular',
    opacity: 0.9,
  },
  boardContainer: {
    height: 160,
    marginBottom: 0, // Reduced from 2
    paddingHorizontal: 4,
  },
  infoButton: {
    position: 'absolute',
    right: 16,
    top: 14,
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.surface,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    marginHorizontal: 12,
    marginVertical: 8, // Reduced from 12
    gap: 6,
  },
  buttonWrapper: {
    flex: 1,
    height: 50,
  },
  archiveButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  archiveContent: {
    justifyContent: 'center',
  },
  archiveMainText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 2,
  },
  archiveSubText: {
    fontSize: 9,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    opacity: 0.8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    height: '80%',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12, // Reduced from 16
  },
  modalTitle: {
    color: colors.primary,
    fontSize: 18,
    fontFamily: 'MontserratBold',
  },
  closeButton: {
    padding: 4,
  },
  modalBoardContainer: {
    flex: 1,
  },
  sectionContainer: {
    marginVertical: 6, // Reduced from 10
    width: '100%',
    backgroundColor: colors.surface,
    borderRadius: 8,
    overflow: 'hidden',
  },
});

export default ProgramEnd;
