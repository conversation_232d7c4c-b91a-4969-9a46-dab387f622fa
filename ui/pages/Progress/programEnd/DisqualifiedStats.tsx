import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from "../../../../shared/contexts/ThemeContext";

interface Props {
  count: number;
  loserPool: number;
}

const DisqualifiedStats: React.FC<Props> = ({
count, loserPool
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.innerBox}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Disqualified Players</Text>
            <Text style={styles.summaryValue}>{count}</Text>
          </View>
          <View style={styles.verticalDivider} />
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Loser Pool</Text>
            <Text style={styles.summaryValue}>${loserPool}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    width: '100%',
    // Remove backgroundColor since it will be provided by the parent
  },
  innerBox: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 9,
    color: colors.primary,
    marginBottom: 1,
  },
  summaryValue: {
    fontSize: 12,
    color: colors.text,
    fontWeight: 'bold',
  },
  verticalDivider: {
    width: 1,
    height: '70%',
    backgroundColor: colors.border,
    marginHorizontal: 6,
  },
});

export default DisqualifiedStats;




