import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Modal } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "../../../../shared/contexts/ThemeContext";
import { Winner } from "./ProgramEnd";

export interface WinnerShareProps {
  programId: string;
  betAmount: number;
  currentUserId: string;
  onStatsUpdate?: (stats: { amount?: number; rank?: number }) => void;
}

interface Props {
  winners: Winner[];
  loserPool: number;
  currentUserId: string;
  showFullTable: boolean;
  setShowFullTable: (show: boolean) => void;
}

const WinnerShare: React.FC<Props> = ({
  winners,
  loserPool,
  currentUserId,
  showFullTable,
  setShowFullTable,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // Table rendering function to avoid duplication
  const renderWinnersTable = (fullView = false) => (
    <>
      <View style={styles.headerRow}>
        <Text style={[styles.headerCell, styles.nameCell]}>Winner</Text>
        <Text style={styles.headerCell}>Grace Days Used</Text>
        <Text style={styles.headerCell}>Share %</Text>
        <Text style={styles.headerCell}>Winnings</Text>
      </View>

      {winners.map((winner, index) => (
        <View
          key={winner.id}
          style={[
            styles.row,
            index % 2 === 0 ? styles.evenRow : styles.oddRow,
            winner.id === currentUserId && styles.currentUserRow,
          ]}
        >
          <Text
            style={[
              styles.cell,
              styles.nameCell,
              winner.id === currentUserId && styles.currentUserText,
            ]}
          >
            {winner.name}
          </Text>
          <Text
            style={[
              styles.cell,
              winner.id === currentUserId && styles.currentUserText,
            ]}
          >
            {winner.bailoutsUsed}
          </Text>
          <Text
            style={[
              styles.cell,
              winner.id === currentUserId && styles.currentUserText,
            ]}
          >
            {winner.sharePercentage.toFixed(1)}%
          </Text>
          <Text
            style={[
              styles.cell,
              winner.id === currentUserId && styles.currentUserText,
            ]}
          >
            ${winner.winnings.toFixed(2)}
          </Text>
        </View>
      ))}

      <View style={styles.platformFeeRow}>
        <Text
          style={[styles.cell, styles.nameCell, styles.platformFeeText]}
        >
          Platform Fee
        </Text>
        <Text style={styles.cell}>-</Text>
        <Text style={styles.cell}>12%</Text>
        <Text style={styles.cell}>${(loserPool * 0.12).toFixed(2)}</Text>
      </View>
    </>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Prize Pool Distribution</Text>
      {winners.length === 0 ? (
        <Text style={[styles.cell, { textAlign: "center", padding: 16 }]}>
          No winners data available
        </Text>
      ) : (
        <>
          <ScrollView
            style={styles.tableContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Show only top 3 winners in the preview */}
            <View style={styles.headerRow}>
              <Text style={[styles.headerCell, styles.nameCell]}>Winner</Text>
              <Text style={styles.headerCell}>Grace Days Used</Text>
              <Text style={styles.headerCell}>Share %</Text>
              <Text style={styles.headerCell}>Winnings</Text>
            </View>

            {winners.slice(0, 3).map((winner, index) => (
              <View
                key={winner.id}
                style={[
                  styles.row,
                  index % 2 === 0 ? styles.evenRow : styles.oddRow,
                  winner.id === currentUserId && styles.currentUserRow,
                ]}
              >
                <Text
                  style={[
                    styles.cell,
                    styles.nameCell,
                    winner.id === currentUserId && styles.currentUserText,
                  ]}
                >
                  {winner.name}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    winner.id === currentUserId && styles.currentUserText,
                  ]}
                >
                  {winner.bailoutsUsed}
                </Text>
                <Text
                  style={[
                    styles.cell,
                    winner.id === currentUserId && styles.currentUserText,
                  ]}
                >
                  {winner.sharePercentage.toFixed(1)}%
                </Text>
                <Text
                  style={[
                    styles.cell,
                    winner.id === currentUserId && styles.currentUserText,
                  ]}
                >
                  ${winner.winnings.toFixed(2)}
                </Text>
              </View>
            ))}

            {/* Show current user if not in top 3 */}
            {currentUserId && 
             !winners.slice(0, 3).some(w => w.id === currentUserId) && 
             winners.find(w => w.id === currentUserId) && (
              <>
                <View style={styles.ellipsisRow}>
                  <Text style={styles.ellipsis}>...</Text>
                </View>
                <View
                  style={[
                    styles.row,
                    styles.currentUserRow,
                  ]}
                >
                  {winners.find(w => w.id === currentUserId) && (
                    <>
                      <Text style={[styles.cell, styles.nameCell, styles.currentUserText]}>
                        {winners.find(w => w.id === currentUserId)?.name}
                      </Text>
                      <Text style={[styles.cell, styles.currentUserText]}>
                        {winners.find(w => w.id === currentUserId)?.bailoutsUsed}
                      </Text>
                      <Text style={[styles.cell, styles.currentUserText]}>
                        {winners.find(w => w.id === currentUserId)?.sharePercentage.toFixed(1)}%
                      </Text>
                      <Text style={[styles.cell, styles.currentUserText]}>
                        ${winners.find(w => w.id === currentUserId)?.winnings.toFixed(2)}
                      </Text>
                    </>
                  )}
                </View>
              </>
            )}

            {/* Show More button above platform fee */}
            <TouchableOpacity 
              style={styles.showMoreButton}
              onPress={() => setShowFullTable(true)}
            >
              <Text style={styles.showMoreText}>Show More</Text>
              <MaterialIcons name="expand-more" size={14} color={colors.primary} />
            </TouchableOpacity>

            <View style={styles.platformFeeRow}>
              <Text style={[styles.cell, styles.nameCell, styles.platformFeeText]}>
                Platform Fee
              </Text>
              <Text style={styles.cell}>-</Text>
              <Text style={styles.cell}>12%</Text>
              <Text style={styles.cell}>${(loserPool * 0.12).toFixed(2)}</Text>
            </View>
          </ScrollView>
          
          {/* Full table modal */}
          <Modal
            visible={showFullTable}
            animationType="slide"
            transparent={true}
            onRequestClose={() => setShowFullTable(false)}
          >
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Prize Pool Distribution</Text>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowFullTable(false)}
                  >
                    <MaterialIcons name="close" size={24} color={colors.primary} />
                  </TouchableOpacity>
                </View>
                
                <ScrollView style={styles.modalTableContainer}>
                  {renderWinnersTable(true)}
                </ScrollView>
              </View>
            </View>
          </Modal>
        </>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    width: '100%',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: "center",
  },
  tableContainer: {
    width: "100%",
    minWidth: 350,
    maxWidth: "100%",
    maxHeight: 220,
  },
  title: {
    color: colors.primary,
    fontSize: 14,
    fontFamily: "MontserratBold",
    marginBottom: 8,
    textAlign: "center",
    alignSelf: "center",
  },
  headerRow: {
    flexDirection: "row",
    backgroundColor: colors.card,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    paddingVertical: 6,
    width: "100%",
  },
  row: {
    flexDirection: "row",
    paddingVertical: 6,
    borderBottomWidth: 0.5,
    borderBottomColor: colors.border,
    width: "100%",
  },
  evenRow: {
    backgroundColor: colors.surface,
  },
  oddRow: {
    backgroundColor: colors.card,
  },
  headerCell: {
    color: colors.primary,
    fontSize: 12,
    fontFamily: "MontserratBold",
    width: 90,
    textAlign: "center",
    flex: 1,
  },
  cell: {
    color: colors.text,
    fontSize: 12,
    fontFamily: "MontserratRegular",
    width: 90,
    textAlign: "center",
    flex: 1,
  },
  nameCell: {
    width: 130,
    textAlign: "left",
    paddingLeft: 8,
    flex: 1.5,
  },
  platformFeeRow: {
    flexDirection: "row",
    paddingVertical: 6,
    borderTopWidth: 2,
    borderTopColor: colors.border,
    backgroundColor: colors.card,
    marginBottom: 4,
  },
  platformFeeText: {
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  currentUserRow: {
    backgroundColor: colors.card,
  },
  currentUserText: {
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 3,
    paddingHorizontal: 8,
    marginVertical: 3,
    backgroundColor: colors.card,
    borderRadius: 4,
    alignSelf: 'center',
    opacity: 0.9,
  },
  showMoreText: {
    color: colors.primary,
    fontSize: 10,
    fontFamily: 'MontserratBold',
    marginRight: 3,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    color: colors.primary,
    fontSize: 18,
    fontFamily: 'MontserratBold',
  },
  closeButton: {
    padding: 4,
  },
  modalTableContainer: {
    width: '100%',
  },
  ellipsisRow: {
    alignItems: 'center',
    paddingVertical: 2,
    backgroundColor: colors.surface,
  },
  ellipsis: {
    color: colors.text,
    fontSize: 14,
    fontFamily: 'MontserratRegular',
  },
});

export default WinnerShare;
