import ProgramChat from "./ProgramChat";
import { Program } from "../../../shared/types/CommonInterface";
import { BaseBottomSheet } from "../../../shared/components/modals";

interface ChatModalProps {
  userId: string;
  userName: string;
  isChatVisible: boolean;
  selectedProgram: Program | null;
  chatVisibler: (chatVisible: boolean) => void;
}

export const ChatModal: React.FC<ChatModalProps> = ({
  userId,
  userName,
  isChatVisible,
  selectedProgram,
  chatVisibler,
}) => {
  return (
    <BaseBottomSheet
      visible={Boolean(isChatVisible)}
      onClose={() => chatVisibler(false)}
      title="Program Chat"
      maxHeight={90}
      minHeight={70}
      scrollable={false}
      noPadding={true}
    >
      {selectedProgram && (
        <ProgramChat
          programId={selectedProgram.id}
          userId={userId}
          userName={userName}
        />
      )}
    </BaseBottomSheet>
  );
};


