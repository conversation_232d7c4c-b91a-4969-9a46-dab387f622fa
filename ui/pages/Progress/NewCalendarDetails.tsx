import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../../shared/contexts/ThemeContext";
import { Program, DayTile, ParticipantList } from "../../../shared/types/CommonInterface";
import { ProgramDayRenderer } from './ProgramDayRenderer';
import { ProgramTabsSelector } from './ProgramTabsSelector';
import { calculateCurrentDay, calculateCurrentDayWithTimezone } from './Calculations';
import { getUserTimezone } from "../../../lib/utils/timezoneUtils";
import CommitInput from './CommitInput';
import { firestoreService } from "../../../lib/services/database";

interface ProgramDay {
  programId: string;
  programName: string;
  status: 'upcoming' | 'ongoing' | 'ended' | 'disqualified';
  needsAttention: boolean;
  programDay: number;
  dayStatus?: 'pending' | 'submitted' | 'approved' | 'failed';
}

// Interface for commit day data (matching NewCalendar.tsx)
interface CommitDay {
  commitId: string;
  commitTitle: string;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
  isDue: boolean;
  isOverdue: boolean;
  currentDay: number;
  totalDays: number;
  dayProgressText: string; // "Day x of x" format
}

interface NewCalendarDetailsProps {
  selectedDate: string;
  programs: ProgramDay[];
  commits?: CommitDay[];
  fullCommits?: any[]; // Full commit objects with evidence data
  userId: string;
  userName?: string;
  onProgramAttention: (programId: string) => void;
  // Data props
  programsData: { [programId: string]: Program };
  userDaysData: { [programId: string]: DayTile[] };
  participantsData: { [programId: string]: ParticipantList[] };
  loading?: boolean;
  submissionTriggered?: boolean;
  onSubmissionTrigger?: () => void;
  // Setup-related props
  onSetupStatusUpdate?: (programId: string, status: boolean) => void;
  onProgramUpdate?: (program: Program) => void;
  onSignedUpProgramsUpdate?: (programs: Program[]) => void;
  // Additional props for full feature parity
  onArchive?: (programId: string) => void;
  archiveLoading?: { [programId: string]: boolean };
  setupLoading?: { [programId: string]: boolean };

}

export const NewCalendarDetails: React.FC<NewCalendarDetailsProps> = ({
  selectedDate,
  programs,
  commits = [],
  fullCommits = [],
  userId,
  userName,
  onProgramAttention,
  programsData,
  userDaysData,
  participantsData,
  loading = false,
  submissionTriggered = false,
  onSubmissionTrigger,
  onSetupStatusUpdate,
  onProgramUpdate,
  onSignedUpProgramsUpdate,
  onArchive,
  archiveLoading = {},
  setupLoading = {},
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);



  const [selectedProgramIndex, setSelectedProgramIndex] = useState(0);
  const [selectedCommitIndex, setSelectedCommitIndex] = useState<number | null>(null);
  const [selectedItemType, setSelectedItemType] = useState<'program' | 'commit'>('program');

  // State for detailed program status handling
  const [disqualificationDetails, setDisqualificationDetails] = useState<{
    reason: string;
    description: string;
    canDispute: boolean;
  } | null>(null);
  const [endedProgramParticipantCounts, setEndedProgramParticipantCounts] = useState<{ [programId: string]: number }>({});

  // Reset selected program/commit when date changes
  useEffect(() => {
    if (programs.length > 0) {
      // If there are programs, select the first program
      setSelectedProgramIndex(0);
      setSelectedCommitIndex(null);
      setSelectedItemType('program');
    } else if (commits.length > 0) {
      // If there are only commits, select the first commit
      setSelectedProgramIndex(0);
      setSelectedCommitIndex(0);
      setSelectedItemType('commit');
    } else {
      // No programs or commits
      setSelectedProgramIndex(0);
      setSelectedCommitIndex(null);
      setSelectedItemType('program');
    }
  }, [selectedDate, programs.length, commits.length]);

  // Handlers for tab selection
  const handleProgramSelect = (index: number) => {
    setSelectedProgramIndex(index);
    setSelectedCommitIndex(null);
    setSelectedItemType('program');
  };

  const handleCommitSelect = (index: number) => {
    const commitIndex = index - programs.length; // Adjust for programs that come first
    setSelectedCommitIndex(commitIndex);
    setSelectedProgramIndex(0); // Reset program selection
    setSelectedItemType('commit');
  };





  const renderMarketingContent = () => {
    const formatSelectedDate = (dateStr: string) => {
      const date = new Date(dateStr + 'T00:00:00');
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const diffTime = date.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        month: 'long',
        day: 'numeric'
      };
      const formattedDate = date.toLocaleDateString('en-US', options);

      if (diffDays === 0) return `Today, ${formattedDate}`;
      if (diffDays === 1) return `Tomorrow, ${formattedDate}`;
      if (diffDays > 1) return `${formattedDate}`;
      return formattedDate;
    };

    // Check if the selected date is in the past
    const selectedDateObj = new Date(selectedDate + 'T00:00:00');
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isPastDate = selectedDateObj < today;

    return (
      <ScrollView showsVerticalScrollIndicator={false} style={styles.marketingContainer}>
        {/* Header */}
        <View style={styles.marketingHeader}>
          <Text style={styles.marketingDate}>{formatSelectedDate(selectedDate)}</Text>
        </View>

        {/* Content based on date */}
        <View style={styles.marketingContent}>
          {isPastDate ? (
            // Past date content - no action buttons
            <>
              <Text style={styles.marketingHeadline}>
                No activity on this date
              </Text>
              <Text style={styles.marketingDescription}>
                You didn't have any programs or commitments scheduled for this day.
              </Text>
            </>
          ) : (
            // Present/future date content - with action buttons
            <>
              <Text style={styles.marketingHeadline}>
                This date looks empty...
              </Text>
              <Text style={styles.marketingDescription}>
                Stay engaged with your goals by joining a program or creating your own commitment.
              </Text>

              {/* Simple Action Buttons - only for present/future dates */}
              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.primaryButton}>
                  <Text style={styles.primaryButtonText}>Join a Program</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.secondaryButton}>
                  <Text style={styles.secondaryButtonText}>Create Your Commitment</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </ScrollView>
    );
  };

  // Fetch disqualification details and ended program participant counts
  useEffect(() => {
    const fetchProgramDetails = async () => {
      if (!userId) return;

      for (const program of programs) {
        const fullProgram = programsData[program.programId];

        // Fetch disqualification details for disqualified programs
        if (fullProgram?.status === 'disqualified') {
          try {
            const result = await firestoreService.participants.getParticipant(program.programId, userId);
            if (result.success && result.data) {
              const participantData = result.data;
              if (participantData.disqualified && participantData.disqualifyReason) {
                setDisqualificationDetails({
                  reason: participantData.disqualifyReason,
                  description: getDisqualificationDetails(participantData.disqualifyReason).description,
                  canDispute: getDisqualificationDetails(participantData.disqualifyReason).canDispute
                });
              }
            }
          } catch (error) {
            console.error('Error fetching disqualification details:', error);
          }
        }

        // Fetch participant count for ended programs
        if (fullProgram?.status === 'ended') {
          try {
            const result = await firestoreService.participants.getParticipantCount(program.programId);
            if (result.success) {
              setEndedProgramParticipantCounts(prev => ({
                ...prev,
                [program.programId]: result.data || 0
              }));
            }
          } catch (error) {
            console.error('Error fetching ended program participant count:', error);
          }
        }
      }
    };

    fetchProgramDetails();
  }, [programs, programsData, userId]);

  // Function to get disqualification details
  const getDisqualificationDetails = (reason: string | null) => {
    switch (reason) {
      case "1a":
        return {
          reason: "1a",
          description: "You've been disqualified because you were inactive and ran out of lives.",
          details: "Our system detected that you didn't submit your daily check-ins for multiple days. After using all your available lives, you were automatically disqualified from the program. Remember that consistent participation is key to success in these challenges.",
          canDispute: false
        };
      case "1b":
        return {
          reason: "1b",
          description: "You've been disqualified because you had too many failed submissions and ran out of lives.",
          details: "Your submissions did not meet the program requirements on multiple occasions. After using all your available lives to cover these failed submissions, you were disqualified from the program. Make sure to carefully read the submission guidelines for future challenges.",
          canDispute: false
        };
      case "1c":
        return {
          reason: "1c",
          description: "You've been disqualified because you ran out of lives.",
          details: "You've used all your available lives in this challenge. Lives are a crucial resource that help you recover from missed or failed submissions. Once depleted, disqualification is automatic according to program rules. Consider purchasing additional lives in future challenges if needed.",
          canDispute: false
        };
      case "2":
        return {
          reason: "2",
          description: "You've been disqualified due to malpractice.",
          details: "Our moderators have identified violations of program rules or submission guidelines. This may include falsified submissions, inappropriate content, or other forms of misconduct that compromise the integrity of the challenge. If you believe this was in error, you can file a dispute through the app.",
          canDispute: true
        };
      default:
        return {
          reason: "unknown",
          description: "Unfortunately, you've been disqualified from this program.",
          details: "Your participation in this challenge has ended due to disqualification. This could be due to various reasons including rule violations, missed submissions, or running out of lives. We encourage you to review the program guidelines for future challenges.",
          canDispute: false
        };
    }
  };

  if (programs.length === 0 && commits.length === 0) {
    return (
      <View style={styles.container}>
        {renderMarketingContent()}
      </View>
    );
  }

  const selectedProgram = programs.length > 0 ? programs[selectedProgramIndex] : null;
  const fullProgram = selectedProgram ? programsData[selectedProgram.programId] : null;
  const userDays = selectedProgram ? (userDaysData[selectedProgram.programId] || []) : [];
  const participants = selectedProgram ? (participantsData[selectedProgram.programId] || []) : [];

  // Check if this is a future date for any of the programs or commits
  const isFutureDate = (() => {
    // First check if the selected date is in the future compared to today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDateObj = new Date(selectedDate + 'T00:00:00');
    selectedDateObj.setHours(0, 0, 0, 0);

    // If the selected date is in the future, it's definitely a future date
    if (selectedDateObj > today) {
      return true;
    }

    // If the selected date is today or in the past, we should show the interactive interface
    // Only show the "future date" view if the selected date is actually in the future
    return false;
  })();

  const renderProgramTabs = () => {
    if (programs.length + commits.length <= 1) return null;

    // Calculate the selected index based on current selection
    const selectedIndex = selectedItemType === 'program'
      ? selectedProgramIndex
      : programs.length + (selectedCommitIndex || 0);

    return (
      <ProgramTabsSelector
        programs={programs}
        commits={commits}
        selectedIndex={selectedIndex}
        onSelectProgram={handleProgramSelect}
        onSelectCommit={handleCommitSelect}
      />
    );
  };

  const renderAllPrograms = () => {
    const formatSelectedDate = (dateStr: string) => {
      const date = new Date(dateStr + 'T00:00:00');
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        month: 'long',
        day: 'numeric'
      };
      return date.toLocaleDateString('en-US', options);
    };

    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Title with Date */}
        <View style={styles.titleContainer}>
          <Text style={styles.futureViewTitle}>Upcoming</Text>
          <Text style={styles.futureViewDate}>{formatSelectedDate(selectedDate)}</Text>
        </View>

        {/* Programs Section */}
        {programs.length > 0 && (
          <>
            <Text style={styles.sectionTitle}>Programs</Text>
            {programs.map((program) => {
              const fullProg = programsData[program.programId];
              if (!fullProg) return null;

              return (
                <View key={program.programId} style={styles.minimalProgramCard}>
                  {/* Program Header */}
                  <View style={styles.minimalProgramHeader}>
                    <Text style={styles.minimalProgramTitle}>{program.programName}</Text>
                    <Text style={styles.minimalDayProgress}>
                      Day {program.programDay} of {typeof fullProg.duration === 'string'
                        ? parseInt(fullProg.duration, 10)
                        : fullProg.duration || 'N/A'}
                    </Text>
                  </View>

                  {/* Status */}
                  <View style={styles.minimalStatusContainer}>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(fullProg.status || 'upcoming') + '20' }]}>
                      <Text style={[styles.statusText, { color: getStatusColor(fullProg.status || 'upcoming') }]}>
                        {(fullProg.status || 'upcoming').toUpperCase()}
                      </Text>
                    </View>
                    {program.needsAttention && (
                      <MaterialIcons name="warning" size={16} color={colors.error} style={{ marginLeft: 8 }} />
                    )}
                  </View>
                </View>
              );
            })}
          </>
        )}

        {/* Commits Section */}
        {commits.length > 0 && (
          <>
            <Text style={styles.sectionTitle}>Commits</Text>
            {commits.map((commit) => (
              <View key={commit.commitId} style={styles.minimalCommitCard}>
                {/* Commit Header */}
                <View style={styles.minimalCommitHeader}>
                  <Text style={styles.minimalCommitTitle}>{commit.commitTitle}</Text>
                  <Text style={styles.minimalCommitFrequency}>
                    {commit.frequency.charAt(0).toUpperCase() + commit.frequency.slice(1)}
                  </Text>
                </View>



                {/* Status and Due Indicator */}
                <View style={styles.minimalStatusContainer}>
                  <View style={[styles.statusBadge, { backgroundColor: getCommitStatusColor(commit.status) + '20' }]}>
                    <Text style={[styles.statusText, { color: getCommitStatusColor(commit.status) }]}>
                      {commit.status.toUpperCase()}
                    </Text>
                  </View>
                  {commit.isDue && (
                    <View style={[styles.dueBadge, { backgroundColor: commit.isOverdue ? colors.error + '20' : colors.primary + '20' }]}>
                      <Text style={[styles.dueText, { color: commit.isOverdue ? colors.error : colors.primary }]}>
                        {commit.isOverdue ? 'OVERDUE' : 'DUE'}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            ))}
          </>
        )}
      </ScrollView>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return colors.textMuted;
      case 'ongoing': return colors.primary;
      case 'ended': return colors.success || '#4CAF50';
      case 'disqualified': return colors.error;
      default: return colors.textMuted;
    }
  };

  const getCommitStatusColor = (status: string) => {
    switch (status) {
      case 'active': return colors.primary;
      case 'completed': return colors.success || '#4CAF50';
      case 'failed': return colors.error;
      case 'cancelled': return colors.textMuted;
      default: return colors.textMuted;
    }
  };

  const renderCommitContent = () => {
    if (selectedCommitIndex === null || !commits[selectedCommitIndex]) {
      return null;
    }

    const selectedCommit = commits[selectedCommitIndex];

    // Find the full commit object from fullCommits array
    const fullCommit = fullCommits.find(commit => commit.id === selectedCommit.commitId);

    if (!fullCommit) {
      console.warn(`Full commit data not found for commitId: ${selectedCommit.commitId}`);
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Unable to load commit details</Text>
        </View>
      );
    }



    return (
      <CommitInput
        key={`${selectedCommit.commitId}-${selectedDate}`}
        commit={fullCommit}
        userId={userId}
        selectedDate={selectedDate}
        onSubmissionSuccess={() => onSubmissionTrigger?.()}
        onCommitUpdate={(updatedCommit) => {
          // Update the fullCommits array with the updated commit
          const updatedFullCommits = fullCommits.map(commit =>
            commit.id === updatedCommit.id ? updatedCommit : commit
          );
          // Note: We would need a callback to update the parent's fullCommits state
          // For now, the setup completion will work locally
        }}
        isLoading={false}
        refreshTrigger={submissionTriggered ? 1 : 0}
      />
    );
  };

  const renderProgramContent = () => {
    if (!selectedProgram || !fullProgram) {
      return null; // No program content to show
    }

    // Show setup loading state if applicable
    if (setupLoading[fullProgram.id]) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading setup status...</Text>
        </View>
      );
    }

    // Calculate the actual current day of the program (not the selected day)
    const userTimezone = getUserTimezone();
    const actualCurrentDay = calculateCurrentDayWithTimezone(
      fullProgram.startDate,
      userTimezone,
      (fullProgram.status || 'ongoing') as 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified',
      typeof fullProgram.duration === 'string' ? Number(fullProgram.duration) : fullProgram.duration || 30 // Pass duration for new simplified system
    );

    return (
      <ProgramDayRenderer
        program={fullProgram}
        programDay={selectedProgram.programDay}
        currentDay={actualCurrentDay}
        userId={userId}
        userName={userName}
        participants={participants}
        userDays={userDays}
        loading={loading}
        submissionTriggered={submissionTriggered}
        onSubmissionTrigger={onSubmissionTrigger}
        showProgramInfo={false}
        needsAttention={selectedProgram.needsAttention}
        onProgramAttention={onProgramAttention}
        attentionReason={
          selectedProgram.status === 'upcoming' ? 'setup' :
          selectedProgram.status === 'disqualified' ? 'disqualified' : 'ended'
        }
        setupStatus={fullProgram.setupStatus}
        onSetupStatusUpdate={(status) => onSetupStatusUpdate?.(fullProgram.id, status)}
        onProgramUpdate={onProgramUpdate}
        onSignedUpProgramsUpdate={onSignedUpProgramsUpdate}
        disqualificationReason={disqualificationDetails?.reason || null}
        onArchive={onArchive}
        archiveLoading={archiveLoading[fullProgram.id] || false}
        setupLoading={setupLoading[fullProgram.id] || false}
        endedProgramParticipants={
          fullProgram.status === 'ended'
            ? (endedProgramParticipantCounts[fullProgram.id] || participants.length)
            : participants.length
        }
        isCalendarView={true}
      />
    );
  };

  return (
    <View style={styles.container}>
      {isFutureDate ? (
        // For future dates, always show simple list format regardless of content type
        renderAllPrograms()
      ) : (
        <>
          {/* Program Tabs */}
          {renderProgramTabs()}

          {/* Day Information - Show for programs and commits */}
          {selectedItemType === 'program' && programs.length > 0 && selectedProgram && (
            <View style={styles.dayInfo}>
              <Text style={styles.dayProgressText}>
                Day {selectedProgram.programDay} of {typeof fullProgram?.duration === 'string'
                  ? parseInt(fullProgram.duration, 10)
                  : fullProgram?.duration || 'N/A'}
              </Text>
            </View>
          )}

          {/* Day Information for Commits */}
          {selectedItemType === 'commit' && commits.length > 0 && selectedCommitIndex !== null && commits[selectedCommitIndex] && (
            <View style={styles.dayInfo}>
              <Text style={styles.dayProgressText}>
                {commits[selectedCommitIndex].dayProgressText}
              </Text>
            </View>
          )}

          {/* Content Area - Show either program or commit content */}
          {selectedItemType === 'program' ? renderProgramContent() : renderCommitContent()}

          {/* Commits Section for Current Date - Only show when no tabs are present and no commit is selected */}
          {(programs.length + commits.length <= 1) && commits.length > 0 && selectedItemType !== 'commit' && (
            <View style={styles.commitsSection}>
              <Text style={styles.sectionTitle}>Commits</Text>
              {commits.map((commit) => (
                <View key={commit.commitId} style={styles.minimalCommitCard}>
                  {/* Commit Header */}
                  <View style={styles.minimalCommitHeader}>
                    <Text style={styles.minimalCommitTitle}>{commit.commitTitle}</Text>
                    <Text style={styles.minimalCommitFrequency}>
                      {commit.frequency.charAt(0).toUpperCase() + commit.frequency.slice(1)}
                    </Text>
                  </View>



                  {/* Status and Due Indicator */}
                  <View style={styles.minimalStatusContainer}>
                    <View style={[styles.statusBadge, { backgroundColor: getCommitStatusColor(commit.status) + '20' }]}>
                      <Text style={[styles.statusText, { color: getCommitStatusColor(commit.status) }]}>
                        {commit.status.toUpperCase()}
                      </Text>
                    </View>
                    {commit.isDue && (
                      <View style={[styles.dueBadge, { backgroundColor: commit.isOverdue ? colors.error + '20' : colors.primary + '20' }]}>
                        <Text style={[styles.dueText, { color: commit.isOverdue ? colors.error : colors.primary }]}>
                          {commit.isOverdue ? 'OVERDUE' : 'DUE'}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },

  dayInfo: {
    marginBottom: 4,
    alignItems: 'center',
  },
  dayProgressText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    letterSpacing: 0.3,
  },

  loadingContainer: {
    padding: 24,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 8,
  },
  noDataText: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    padding: 24,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  noDataSubtext: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.7,
  },
  programCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.textMuted + '10',
  },
  programHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    paddingBottom: 8,
  },
  programTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    marginRight: 12,
  },
  programMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontFamily: 'MontserratBold',
  },
  // Minimal Future View Styles
  titleContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  futureViewTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 4,
  },
  futureViewDate: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
  },
  minimalProgramCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.textMuted + '10',
  },
  minimalProgramHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  minimalProgramTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    marginRight: 12,
  },
  minimalDayProgress: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
  },
  minimalStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Minimal Marketing Content Styles
  marketingContainer: {
    flex: 1,
  },
  marketingHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  marketingDate: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    textAlign: 'center',
  },
  marketingContent: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 24,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  marketingHeadline: {
    fontSize: 20,
    fontFamily: 'MontserratMedium',
    fontWeight: '500',
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  marketingDescription: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    fontWeight: '400',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  actionButtons: {
    width: '100%',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: colors.primary,
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginBottom: 12,
    width: '100%',
    maxWidth: 280,
  },
  primaryButtonText: {
    fontSize: 15,
    fontFamily: 'MontserratBold',
    fontWeight: '700',
    color: colors.background,
    letterSpacing: 0.5,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 32,
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: colors.textMuted + '30',
    width: '100%',
    maxWidth: 280,
  },
  secondaryButtonText: {
    fontSize: 15,
    fontFamily: 'MontserratSemiBold',
    fontWeight: '600',
    color: colors.text,
    letterSpacing: 0.3,
  },

  // Section and Commit Styles
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
    marginHorizontal: 4,
  },
  minimalCommitCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.border || colors.textMuted + '20',
    borderStyle: 'dashed', // Distinguish commits from programs
  },
  minimalCommitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  minimalCommitTitle: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    flex: 1,
  },
  minimalCommitFrequency: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textTransform: 'capitalize',
  },
  dueBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  dueText: {
    fontSize: 10,
    fontFamily: 'MontserratBold',
    fontWeight: '700',
  },
  commitsSection: {
    marginTop: 16,
  },
  errorContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 20,
    margin: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.error + '30',
    borderStyle: 'dashed',
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.error,
    textAlign: 'center',
  },



});
