export const calculateCurrentDay = (startDate: string | undefined) => {
    if (!startDate) return 1;

    // Create date objects with time set to midnight to avoid time-of-day issues
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Calculate difference in days
    const diff = Math.round(
        (today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
    );

    return diff + 1;
};

/**
 * Calculate current day with timezone awareness
 * UPDATED: Now uses simplified participant-level timezone system
 */
export const calculateCurrentDayWithTimezone = (
    startDate: string | undefined,
    userTimezone: string,
    programStatus: 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified',
    programDuration: number = 30
) => {
    if (!startDate) return 1;

    try {
        const { calculateParticipantProgramDay } = require("../../../lib/utils/timezoneUtils");
        const result = calculateParticipantProgramDay(startDate, userTimezone, programDuration);
        return result.currentDay;
    } catch (error) {
        console.error('Error using simplified calculation, falling back to legacy:', error);
        return calculateCurrentDay(startDate);
    }
};

export const calculateDaysToStart = (startDate: string | undefined): number => {
    if (!startDate) return 0;
    const currentDate = new Date();
    const start = new Date(startDate);
    const difference = start.getTime() - currentDate.getTime();
    return Math.ceil(difference / (1000 * 60 * 60 * 24));
};
