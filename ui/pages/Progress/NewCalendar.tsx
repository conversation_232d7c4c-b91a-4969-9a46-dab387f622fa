import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../../shared/contexts/ThemeContext";
import { Program, DayTile } from "../../../shared/types/CommonInterface";

// Platform-specific calendar dimensions
const getCalendarDimensions = () => {
  if (Platform.OS === 'web') {
    // Web-specific dimensions - use fixed height for better appearance
    return {
      CALENDAR_PADDING: 12,
      DAY_WIDTH_PERCENTAGE: '14.285714%', // 100% / 7 days
      DAY_SIZE: 38, // Reduced from 45 to decrease vertical padding
    };
  } else {
    // Mobile dimensions - use responsive sizing
    const { width: SCREEN_WIDTH } = Dimensions.get('window');
    const CALENDAR_PADDING = 12;
    const CALENDAR_WIDTH = SCREEN_WIDTH - CALENDAR_PADDING * 2;
    const DAY_WIDTH = CALENDAR_WIDTH / 7;
    const DAY_SIZE = Math.floor(DAY_WIDTH) * 0.75; // Reduced from 0.85 to 0.75

    return {
      CALENDAR_PADDING,
      DAY_WIDTH_PERCENTAGE: '14.285714%',
      DAY_SIZE,
    };
  }
};

const { CALENDAR_PADDING, DAY_WIDTH_PERCENTAGE, DAY_SIZE } = getCalendarDimensions();

// Program color palette for bands - theme-aware colors
const getProgramColors = (isDark: boolean) => {
  if (isDark) {
    // Bright colors for dark mode
    return [
      '#FF6B6B', // Red
      '#4ECDC4', // Teal
      '#45B7D1', // Blue
      '#96CEB4', // Green
      '#FFEAA7', // Yellow
      '#DDA0DD', // Plum
      '#98D8C8', // Mint
      '#F7DC6F', // Light Yellow
      '#BB8FCE', // Light Purple
      '#85C1E9', // Light Blue
      '#F8C471', // Orange
      '#82E0AA', // Light Green
    ];
  } else {
    // Darker, more saturated colors for light mode
    return [
      '#E53E3E', // Darker Red
      '#319795', // Darker Teal
      '#3182CE', // Darker Blue
      '#38A169', // Darker Green
      '#D69E2E', // Darker Yellow
      '#B794F6', // Darker Plum
      '#4FD1C7', // Darker Mint
      '#ECC94B', // Darker Light Yellow
      '#9F7AEA', // Darker Purple
      '#4299E1', // Darker Light Blue
      '#ED8936', // Darker Orange
      '#48BB78', // Darker Light Green
    ];
  }
};

// Helper function to format date as YYYY-MM-DD
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Cache for program date calculations
const programDateCache = new Map<string, { startDate: Date; endDate: Date }>();

// Cache clearing utility
export const clearCalendarCaches = (): void => {
  programDateCache.clear();
  commitDateCache.clear();
};

// Utility to get visible date range for efficient data loading
export const getVisibleDateRange = (currentDate: Date, isExpanded: boolean): { startDate: string; endDate: string } => {
  if (isExpanded) {
    // Full month view
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    // Include some padding for previous/next month days
    const startOfCalendar = new Date(firstDay);
    startOfCalendar.setDate(startOfCalendar.getDate() - firstDay.getDay());

    const endOfCalendar = new Date(lastDay);
    const remainingDays = 6 - lastDay.getDay();
    endOfCalendar.setDate(endOfCalendar.getDate() + remainingDays);

    return {
      startDate: formatDate(startOfCalendar),
      endDate: formatDate(endOfCalendar)
    };
  } else {
    // Week view
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    return {
      startDate: formatDate(startOfWeek),
      endDate: formatDate(endOfWeek)
    };
  }
};

// Performance monitoring for calendar operations
const measureCalendarPerformance = <T,>(operation: string, fn: () => T): T => {
  if (__DEV__) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    const duration = end - start;

    if (duration > 16) { // More than one frame at 60fps
      // Performance monitoring: operation took longer than expected
    }

    return result;
  }
  return fn();
};

// Optimized function to get cached program date range
const getCachedProgramDates = (program: Program): { startDate: Date; endDate: Date } | null => {
  if (!program.startDate) return null;

  const cacheKey = `${program.id}-${program.startDate}-${program.endDate || program.duration}`;

  if (!programDateCache.has(cacheKey)) {
    const startDate = new Date(program.startDate + 'T00:00:00');

    let endDate: Date;
    if (program.endDate) {
      endDate = new Date(program.endDate + 'T00:00:00');
    } else if (program.duration) {
      const durationNum = typeof program.duration === 'string' ? parseInt(program.duration, 10) : program.duration;
      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + durationNum - 1);
    } else {
      return null;
    }

    programDateCache.set(cacheKey, { startDate, endDate });
  }

  return programDateCache.get(cacheKey)!;
};

// Get programs active on a specific date (optimized)
const getProgramsForDate = (dateStr: string, programs: Program[], userDaysData: { [programId: string]: DayTile[] }): ProgramDay[] => {
  const date = new Date(dateStr + 'T00:00:00');
  const programDays: ProgramDay[] = [];

  programs.forEach(program => {
    const dateRange = getCachedProgramDates(program);
    if (!dateRange) return;

    const { startDate, endDate } = dateRange;

    // Check if date falls within program duration
    if (date >= startDate && date <= endDate) {
      // Calculate program day
      const daysDiff = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const programDay = daysDiff + 1;

      // Get day status from user data
      const userDays = userDaysData[program.id] || [];
      const dayTile = userDays.find(d => d.day === programDay);

      // Determine if needs attention
      const needsAttention = (
        (program.status === 'upcoming' && program.setupStatus === false) ||
        program.status === 'disqualified' ||
        program.status === 'ended'
      );

      programDays.push({
        programId: program.id,
        programName: program.name,
        status: program.status || 'upcoming',
        needsAttention,
        programDay,
        dayStatus: dayTile?.status as 'pending' | 'submitted' | 'approved' | 'failed' | undefined,
      });
    }
  });

  return programDays;
};

// Interface for commit day data
interface CommitDay {
  commitId: string;
  commitTitle: string;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
  isDue: boolean;
  isOverdue: boolean;
  currentDay: number;
  totalDays: number;
  dayProgressText: string; // "Day x of x" format
}

// Cache for commit date calculations
const commitDateCache = new Map<string, { startDate: Date; endDate: Date }>();

// Optimized function to get cached commit date range
const getCachedCommitDates = (commit: any): { startDate: Date; endDate: Date } | null => {
  if (!commit?.schedule?.startDate) return null;

  const cacheKey = `${commit.id || 'unknown'}-${commit.schedule.startDate}-${commit.schedule.endDate || commit.schedule.duration}-${commit.schedule.frequency}`;

  if (!commitDateCache.has(cacheKey)) {
    const startDateStr = commit.schedule.startDate.includes('T')
      ? commit.schedule.startDate.split('T')[0]
      : commit.schedule.startDate;
    const startDate = new Date(startDateStr + 'T00:00:00');

    let endDate: Date;
    if (commit.schedule.endDate) {
      const endDateStr = commit.schedule.endDate.includes('T')
        ? commit.schedule.endDate.split('T')[0]
        : commit.schedule.endDate;
      endDate = new Date(endDateStr + 'T23:59:59.999');
    } else if (commit.schedule.duration) {
      const duration = commit.schedule.duration;

      switch (commit.schedule.frequency) {
        case 'daily':
          endDate = new Date(startDate.getTime() + (duration - 1) * 24 * 60 * 60 * 1000);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'weekly':
          endDate = new Date(startDate.getTime() + (duration * 7 - 1) * 24 * 60 * 60 * 1000);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'monthly':
          endDate = new Date(startDate);
          endDate.setMonth(endDate.getMonth() + duration);
          endDate.setDate(endDate.getDate() - 1);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'once':
          endDate = new Date(startDateStr + 'T23:59:59.999');
          break;
        default:
          return null;
      }
    } else {
      return null;
    }

    commitDateCache.set(cacheKey, { startDate, endDate });
  }

  return commitDateCache.get(cacheKey)!;
};

// Get commits active on a specific date (optimized)
const getCommitsForDate = (dateStr: string, commits: any[]): CommitDay[] => {
  const date = new Date(dateStr + 'T00:00:00');
  const commitDays: CommitDay[] = [];

  commits.forEach(commit => {
    const dateRange = getCachedCommitDates(commit);
    if (!dateRange) return;

    const { startDate, endDate } = dateRange;

    // Check if date falls within commit duration
    if (date >= startDate && date <= endDate) {
      // Show commits for any date within their range, regardless of due date
      let isDue = false;
      let isOverdue = false;

      switch (commit.schedule.frequency) {
        case 'daily':
          isDue = true; // Every day is a due date for daily commits
          break;
        case 'weekly':
          // Check if this is the same day of week as start date
          isDue = date.getDay() === startDate.getDay();
          break;
        case 'monthly':
          // Check if this is the same date of month as start date
          isDue = date.getDate() === startDate.getDate();
          break;
        case 'once':
          // Only due on the exact start date
          isDue = date.getTime() === startDate.getTime();
          break;
      }

      // For now, we'll consider overdue logic later when we have submission data
      // This would require checking if the commit was submitted on time

      // Show commit for any date within its range (removed the isDue filter)
      {
        // Calculate current day and total days using the same logic as CommitService
        const duration = commit.schedule.duration;
        const endDate = commit.schedule.endDate;
        let currentDay = 1;
        let totalDays = 1;
        let dayProgressText = '';

        // Calculate total duration using the same logic as CommitService
        if (commit.schedule.frequency === 'daily') {
          if (endDate && commit.schedule.startDate) {
            const start = new Date(commit.schedule.startDate);
            const end = new Date(endDate);
            totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
          } else if (duration) {
            totalDays = duration;
          }
        } else if (commit.schedule.frequency === 'weekly') {
          totalDays = duration || 1;
        } else if (commit.schedule.frequency === 'monthly') {
          totalDays = duration || 1;
        } else if (commit.schedule.frequency === 'once') {
          totalDays = 1;
        }

        if (commit.schedule.frequency === 'once') {
          currentDay = 1;
          dayProgressText = 'Day 1 of 1';
        } else if (commit.schedule.frequency === 'daily') {
          const daysDiff = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          currentDay = Math.max(1, daysDiff + 1);
          dayProgressText = `Day ${currentDay} of ${totalDays}`;
        } else if (commit.schedule.frequency === 'weekly') {
          const weeksDiff = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
          currentDay = Math.max(1, weeksDiff + 1);
          dayProgressText = `Week ${currentDay} of ${totalDays}`;
        } else if (commit.schedule.frequency === 'monthly') {
          const monthsDiff = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
          currentDay = Math.max(1, monthsDiff + 1);
          dayProgressText = `Month ${currentDay} of ${totalDays}`;
        }

        commitDays.push({
          commitId: commit.id,
          commitTitle: commit.title || 'Untitled Commit',
          status: commit.status || 'active',
          frequency: commit.schedule.frequency,
          isDue,
          isOverdue,
          currentDay,
          totalDays,
          dayProgressText,
        });
      }
    }
  });

  return commitDays;
};

interface CalendarDay {
  date: string; // YYYY-MM-DD
  day: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  programs: ProgramDay[];
  commits: CommitDay[];
}

interface ProgramDay {
  programId: string;
  programName: string;
  status: 'upcoming' | 'ongoing' | 'ended' | 'disqualified';
  needsAttention: boolean;
  programDay: number; // Day within the program (1, 2, 3...)
  dayStatus?: 'pending' | 'submitted' | 'approved' | 'failed';
}

interface NewCalendarProps {
  programs: Program[];
  commits?: any[]; // Using any[] for now to avoid circular import issues
  onDateSelect: (date: string, programs: ProgramDay[], commits: CommitDay[]) => void;
  selectedDate?: string;
  userDaysData?: { [programId: string]: DayTile[] };
}

// Helper function to get consistent color for a program
const getProgramColor = (programId: string, isDark: boolean): string => {
  // Create a simple hash from program ID to ensure consistent colors
  let hash = 0;
  for (let i = 0; i < programId.length; i++) {
    const char = programId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  const programColors = getProgramColors(isDark);
  const index = Math.abs(hash) % programColors.length;
  return programColors[index];
};

// Interface for program bands
interface ProgramBand {
  programId: string;
  programName: string;
  color: string;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'ongoing' | 'ended' | 'disqualified';
  needsAttention: boolean;
}

// Helper function to generate program bands for the calendar
const generateProgramBands = (programs: Program[], isDark: boolean): ProgramBand[] => {
  if (!programs || !Array.isArray(programs)) {
    return [];
  }

  return programs.map(program => {
    let endDate: string;
    if (program.endDate) {
      endDate = program.endDate;
    } else if (program.duration && program.startDate) {
      const durationNum = typeof program.duration === 'string' ? parseInt(program.duration, 10) : program.duration;
      const end = new Date(program.startDate + 'T00:00:00');
      end.setDate(end.getDate() + durationNum - 1);
      endDate = formatDate(end);
    } else {
      endDate = program.startDate || formatDate(new Date());
    }

    const needsAttention = (
      (program.status === 'upcoming' && program.setupStatus === false) ||
      program.status === 'disqualified' ||
      program.status === 'ended'
    );

    return {
      programId: program.id,
      programName: program.name,
      color: getProgramColor(program.id, isDark),
      startDate: program.startDate || formatDate(new Date()),
      endDate,
      status: program.status || 'upcoming',
      needsAttention,
    };
  }).filter(band => band.startDate); // Only include programs with start dates
};



// Algorithm: Interval Scheduling with Consistent Stack Assignment
// This solves the problem of maintaining visual consistency across calendar cells
// Similar to "Meeting Rooms II" but with global stack position assignment

interface ProgramInterval {
  programId: string;
  startDate: Date;
  endDate: Date;
  stackPosition?: number;
}

// Step 1: Assign consistent stack positions using interval scheduling algorithm
const assignGlobalStackPositions = (bands: ProgramBand[]): Map<string, number> => {
  if (!bands || bands.length === 0) return new Map();

  // Convert to intervals and sort by start date, then by end date
  const intervals: ProgramInterval[] = bands
    .map(band => ({
      programId: band.programId,
      startDate: new Date(band.startDate + 'T00:00:00'),
      endDate: new Date(band.endDate + 'T00:00:00'),
    }))
    .sort((a, b) => {
      if (a.startDate.getTime() !== b.startDate.getTime()) {
        return a.startDate.getTime() - b.startDate.getTime();
      }
      return a.endDate.getTime() - b.endDate.getTime();
    });

  // Greedy algorithm: Use minimum number of stacks
  const stacks: Date[] = []; // Each stack tracks the end time of its last program
  const stackAssignments = new Map<string, number>();

  for (const interval of intervals) {
    // Find the first available stack (earliest ending time that doesn't conflict)
    let assignedStack = -1;

    for (let i = 0; i < stacks.length; i++) {
      if (stacks[i] < interval.startDate) {
        assignedStack = i;
        stacks[i] = interval.endDate;
        break;
      }
    }

    // If no available stack found, create a new one
    if (assignedStack === -1) {
      assignedStack = stacks.length;
      stacks.push(interval.endDate);
    }

    stackAssignments.set(interval.programId, assignedStack);
  }

  return stackAssignments;
};

// Step 2: Get consistent stack position for a program (now O(1) lookup)
const getProgramStackPosition = (programId: string, stackAssignments: Map<string, number>): number => {
  return stackAssignments.get(programId) ?? 0;
};

// Commit band interface
interface CommitBand {
  commitId: string;
  commitTitle: string;
  color: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
}

// Helper function to get consistent color for a commit
const getCommitColor = (commitId: string, isDark: boolean): string => {
  // Create a simple hash from commit ID to ensure consistent colors
  let hash = 0;
  for (let i = 0; i < commitId.length; i++) {
    const char = commitId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use different color palette for commits to distinguish from programs
  const commitColors = isDark
    ? ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
    : ['#E74C3C', '#16A085', '#3498DB', '#27AE60', '#F39C12', '#9B59B6', '#1ABC9C', '#F1C40F'];

  const index = Math.abs(hash) % commitColors.length;
  return commitColors[index];
};

// Helper function to generate commit bands from commits (optimized)
const generateCommitBands = (commits: any[], isDark: boolean): CommitBand[] => {
  if (!commits || !Array.isArray(commits)) {
    return [];
  }

  const filteredCommits = commits.filter(commit => {
    return commit && commit.schedule?.startDate && (commit.schedule?.endDate || commit.schedule?.duration);
  });

  return filteredCommits.map(commit => {
      let endDate = commit.schedule.endDate;

      // Calculate end date if not provided
      if (!endDate && commit.schedule.startDate && commit.schedule.duration) {
        const start = new Date(commit.schedule.startDate);
        const duration = commit.schedule.duration;

        switch (commit.schedule.frequency) {
          case 'daily':
            endDate = new Date(start.getTime() + (duration - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            break;
          case 'weekly':
            endDate = new Date(start.getTime() + (duration * 7 - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            break;
          case 'monthly':
            const endMonth = new Date(start);
            endMonth.setMonth(endMonth.getMonth() + duration);
            endMonth.setDate(endMonth.getDate() - 1);
            endDate = endMonth.toISOString().split('T')[0];
            break;
          case 'once':
            endDate = commit.schedule.startDate;
            break;
        }
      }

      // Extract date-only strings for consistent comparison
      const startDateStr = commit.schedule.startDate.includes('T')
        ? commit.schedule.startDate.split('T')[0]
        : commit.schedule.startDate;

      const endDateStr = endDate
        ? (endDate.includes('T') ? endDate.split('T')[0] : endDate)
        : startDateStr;



      return {
        commitId: commit.id || '',
        commitTitle: commit.title || 'Untitled Commit',
        color: getCommitColor(commit.id || '', isDark),
        startDate: startDateStr,
        endDate: endDateStr,
        status: commit.status,
        frequency: commit.schedule.frequency,
      };
    });
};





// Helper function to render connected program bands across the calendar
const renderConnectedProgramBand = (
  band: ProgramBand,
  calendarData: CalendarDay[],
  allBands: ProgramBand[],
  commitBands: CommitBand[],
  stackAssignments: Map<string, number>,
  isDark: boolean
) => {
  if (!band || !band.startDate || !band.endDate || !calendarData || !Array.isArray(calendarData)) {
    return null;
  }

  const bandStart = new Date(band.startDate + 'T00:00:00');
  const bandEnd = new Date(band.endDate + 'T00:00:00');
  const bandElements: JSX.Element[] = [];



  // Group calendar data into weeks (rows of 7)
  const weeks: CalendarDay[][] = [];
  for (let i = 0; i < calendarData.length; i += 7) {
    weeks.push(calendarData.slice(i, i + 7));
  }

  weeks.forEach((week, weekIndex) => {
    week.forEach((day, dayIndex) => {
      const dayDate = new Date(day.date + 'T00:00:00');
      const isInBand = dayDate >= bandStart && dayDate <= bandEnd && day.isCurrentMonth;

      if (isInBand) {
        // Get programs active on this specific date for sizing
        const activeProgramsOnThisDay = allBands.filter(b => {
          if (!b || !b.startDate || !b.endDate) return false;
          const bStart = new Date(b.startDate + 'T00:00:00');
          const bEnd = new Date(b.endDate + 'T00:00:00');
          return dayDate >= bStart && dayDate <= bEnd && day.isCurrentMonth;
        });

        // Get commits active on this specific date for unified sizing
        const activeCommitsOnThisDay = commitBands.filter(b => {
          if (!b || !b.startDate || !b.endDate) return false;
          const bStart = new Date(b.startDate.includes('T') ? b.startDate : b.startDate + 'T00:00:00');
          const bEnd = new Date(b.endDate.includes('T') ? b.endDate : b.endDate + 'T00:00:00');
          return dayDate >= bStart && dayDate <= bEnd && day.isCurrentMonth;
        });

        const totalProgramsOnThisDay = activeProgramsOnThisDay.length;
        const totalCommitsOnThisDay = activeCommitsOnThisDay.length;
        const totalItemsOnThisDay = totalProgramsOnThisDay + totalCommitsOnThisDay;

        if (totalItemsOnThisDay > 0) {
          // Unified sizing: divide cell height by total number of programs + commits
          const availableHeight = DAY_SIZE * 0.6; // Use 60% of cell height
          const bandHeight = availableHeight / totalItemsOnThisDay;
          const startOffset = (DAY_SIZE - availableHeight) / 2;

          // Map global stack position to local day position
          // Sort active programs by their global stack position to maintain visual order
          const sortedActivePrograms = activeProgramsOnThisDay
            .map(b => ({
              programId: b.programId,
              globalStackPosition: getProgramStackPosition(b.programId, stackAssignments)
            }))
            .sort((a, b) => a.globalStackPosition - b.globalStackPosition);

          // Find this program's local position within the day
          const localStackPosition = sortedActivePrograms.findIndex(p => p.programId === band.programId);



          // Determine if this is first or last cell in the band for border radius
          const isFirstInBand = dayIndex === 0 || !week[dayIndex - 1] ||
            new Date(week[dayIndex - 1].date + 'T00:00:00') < bandStart;
          const isLastInBand = dayIndex === 6 || !week[dayIndex + 1] ||
            new Date(week[dayIndex + 1].date + 'T00:00:00') > bandEnd;

          bandElements.push(
            <View
              key={`${band.programId}-${weekIndex}-${dayIndex}`}
              style={{
                position: 'absolute',
                left: `${(dayIndex / 7) * 100}%`, // Simple percentage positioning
                width: `${100 / 7}%`, // Full width of each day cell (1/7 of container)
                height: bandHeight,
                backgroundColor: band.color + (isDark ? '30' : '25'), // Slightly less opacity in light mode
                borderWidth: isDark ? 0.5 : 1, // Thicker border in light mode for better visibility
                borderColor: band.color + (isDark ? '60' : '80'), // More opaque border in light mode
                top: weekIndex * DAY_SIZE + startOffset + (localStackPosition * bandHeight),
                zIndex: 1,
                // Border radius only on first and last cells
                borderTopLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 4) : 0,
                borderBottomLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 4) : 0,
                borderTopRightRadius: isLastInBand ? Math.min(bandHeight / 3, 4) : 0,
                borderBottomRightRadius: isLastInBand ? Math.min(bandHeight / 3, 4) : 0,
                // Minimal vertical gaps between stacks
                marginTop: localStackPosition > 0 ? 1 : 0,
              }}
            />
          );
        }
      }
    });
  });

  return bandElements;
};

// Helper function to render connected commit bands with dotted styling (optimized)
const renderConnectedCommitBand = (
  band: CommitBand,
  calendarData: CalendarDay[],
  allBands: CommitBand[],
  isDark: boolean
) => {
  if (!band || !band.startDate || !band.endDate || !calendarData || !Array.isArray(calendarData)) {
    return null;
  }

  // Handle both timestamp and date-only formats
  const bandStart = new Date(band.startDate.includes('T') ? band.startDate : band.startDate + 'T00:00:00');
  const bandEnd = new Date(band.endDate.includes('T') ? band.endDate : band.endDate + 'T00:00:00');
  const bandElements: JSX.Element[] = [];

  // Group calendar data into weeks (rows of 7)
  const weeks: CalendarDay[][] = [];
  for (let i = 0; i < calendarData.length; i += 7) {
    weeks.push(calendarData.slice(i, i + 7));
  }

  weeks.forEach((week, weekIndex) => {
    week.forEach((day, dayIndex) => {
      const dayDate = new Date(day.date + 'T00:00:00');
      const isInBand = dayDate >= bandStart && dayDate <= bandEnd && day.isCurrentMonth;

      if (isInBand) {
        // Get ALL active items (programs + commits) on this specific date for unified stacking
        const activeProgramsOnThisDay = day.programs || [];
        const activeCommitsOnThisDay = allBands.filter(b => {
          if (!b || !b.startDate || !b.endDate) return false;
          const bStart = new Date(b.startDate.includes('T') ? b.startDate : b.startDate + 'T00:00:00');
          const bEnd = new Date(b.endDate.includes('T') ? b.endDate : b.endDate + 'T00:00:00');
          return dayDate >= bStart && dayDate <= bEnd && day.isCurrentMonth;
        });

        const totalProgramsOnThisDay = activeProgramsOnThisDay.length;
        const totalCommitsOnThisDay = activeCommitsOnThisDay.length;
        const totalItemsOnThisDay = totalProgramsOnThisDay + totalCommitsOnThisDay;

        if (totalItemsOnThisDay > 0) {
          // Calculate unified band dimensions - share the full day height
          const availableHeight = DAY_SIZE * 0.6; // Use 60% of day height for all bands
          const bandHeight = availableHeight / totalItemsOnThisDay; // Use same calculation as programs
          const startOffset = (DAY_SIZE - availableHeight) / 2; // Use same offset as programs

          // Calculate the actual position of this commit in the unified stack
          // Commits come after programs, so we need to find this commit's position among all commits on this day
          const commitsOnThisDay = activeCommitsOnThisDay.map(c => c.commitId).sort();
          const thisCommitIndexInDay = commitsOnThisDay.indexOf(band.commitId);
          const actualStackPosition = totalProgramsOnThisDay + thisCommitIndexInDay;

          // Determine if this is first or last cell in the band for border radius
          const isFirstInBand = dayIndex === 0 || !week[dayIndex - 1] ||
            new Date(week[dayIndex - 1].date + 'T00:00:00') < bandStart;
          const isLastInBand = dayIndex === 6 || !week[dayIndex + 1] ||
            new Date(week[dayIndex + 1].date + 'T00:00:00') > bandEnd;



          bandElements.push(
            <View
              key={`${band.commitId}-${weekIndex}-${dayIndex}`}
              style={{
                position: 'absolute',
                left: `${(dayIndex / 7) * 100}%`,
                width: `${(1 / 7) * 100}%`,
                height: bandHeight,
                backgroundColor: band.color + (isDark ? '20' : '15'), // More transparent than programs
                borderWidth: 1,
                borderColor: band.color + (isDark ? '80' : '90'),
                borderStyle: 'dashed', // Dotted/dashed style for commits
                // Alternative visual style for commits - use opacity pattern
                opacity: 0.8,
                top: weekIndex * DAY_SIZE + startOffset + (actualStackPosition * bandHeight),
                zIndex: 1, // Same level as programs
                // Border radius only on first and last cells
                borderTopLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 3) : 0,
                borderBottomLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 3) : 0,
                borderTopRightRadius: isLastInBand ? Math.min(bandHeight / 3, 3) : 0,
                borderBottomRightRadius: isLastInBand ? Math.min(bandHeight / 3, 3) : 0,
              }}
            />
          );
        }
      }
    });
  });

  return bandElements;
};

const NewCalendarComponent: React.FC<NewCalendarProps> = ({
  programs,
  commits = [],
  onDateSelect,
  selectedDate,
  userDaysData = {},
}) => {
  const { colors, isDark } = useTheme();
  const styles = createStyles(colors);

  const [currentDate, setCurrentDate] = useState(new Date());
  const [isExpanded, setIsExpanded] = useState(false);





  // Auto-select today's date if no date is selected
  useEffect(() => {
    if (!selectedDate) {
      const today = formatDate(new Date());
      const todayPrograms = getProgramsForDate(today, programs, userDaysData);
      const todayCommits = getCommitsForDate(today, commits);
      // Always select today's date, even if there are no programs or commits
      onDateSelect(today, todayPrograms, todayCommits);
    }
  }, [programs, commits, userDaysData, selectedDate, onDateSelect]);

  // Generate program bands (with performance monitoring)
  const programBands = useMemo(() =>
    measureCalendarPerformance('generateProgramBands', () => generateProgramBands(programs, isDark)),
    [programs, isDark]
  );

  // Generate commit bands (optimized with performance monitoring)
  const commitBands = useMemo(() =>
    measureCalendarPerformance('generateCommitBands', () => generateCommitBands(commits, isDark)),
    [commits, isDark]
  );

  // Generate global stack assignments for consistent visual lines (optimized with performance monitoring)
  const stackAssignments = useMemo(() =>
    measureCalendarPerformance('assignGlobalStackPositions', () => assignGlobalStackPositions(programBands)),
    [programBands]
  );

  // Note: We don't need separate commit stack assignments since we calculate positions directly

  // Memoize expensive date calculations
  const dateCalculations = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    return { today, startOfWeek };
  }, [currentDate]);

  // Generate calendar data (optimized with performance monitoring)
  const calendarData = useMemo(() => measureCalendarPerformance('generateCalendarData', () => {
    const { today, startOfWeek } = dateCalculations;

    if (!isExpanded) {
      // Show only current week
      const days: CalendarDay[] = [];

      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        const dateStr = formatDate(date);
        const isToday = date.getTime() === today.getTime();
        const isCurrentMonth = date.getMonth() === currentDate.getMonth() &&
                              date.getFullYear() === currentDate.getFullYear();

        days.push({
          date: dateStr,
          day: date.getDate(),
          isCurrentMonth,
          isToday,
          programs: getProgramsForDate(dateStr, programs, userDaysData),
          commits: getCommitsForDate(dateStr, commits),
        });
      }

      return days;
    } else {
      // Show full month
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();

      // Get first day of month and calculate grid
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const daysInMonth = lastDay.getDate();
      const startingDayOfWeek = firstDay.getDay();

      const days: CalendarDay[] = [];

      // Add previous month's trailing days
      for (let i = startingDayOfWeek - 1; i >= 0; i--) {
        const date = new Date(year, month, -i);
        const dateStr = formatDate(date);
        days.push({
          date: dateStr,
          day: date.getDate(),
          isCurrentMonth: false,
          isToday: false,
          programs: getProgramsForDate(dateStr, programs, userDaysData),
          commits: getCommitsForDate(dateStr, commits),
        });
      }

      // Add current month's days
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const dateStr = formatDate(date);
        const isToday = date.getTime() === today.getTime();

        days.push({
          date: dateStr,
          day,
          isCurrentMonth: true,
          isToday,
          programs: getProgramsForDate(dateStr, programs, userDaysData),
          commits: getCommitsForDate(dateStr, commits),
        });
      }

      // Add next month's leading days to complete the grid
      const totalCells = Math.ceil(days.length / 7) * 7;
      let nextMonthDay = 1;
      while (days.length < totalCells) {
        const date = new Date(year, month + 1, nextMonthDay);
        const dateStr = formatDate(date);
        days.push({
          date: dateStr,
          day: nextMonthDay,
          isCurrentMonth: false,
          isToday: false,
          programs: getProgramsForDate(dateStr, programs, userDaysData),
          commits: getCommitsForDate(dateStr, commits),
        });
        nextMonthDay++;
      }

      return days;
    }
  }), [dateCalculations, currentDate, programs, userDaysData, commits, isExpanded]);



  // Navigation handlers (optimized with useCallback)
  const handlePrev = useCallback(() => {
    if (isExpanded) {
      // Navigate to previous month
      setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
    } else {
      // Navigate to previous week
      const newDate = new Date(currentDate);
      newDate.setDate(currentDate.getDate() - 7);
      setCurrentDate(newDate);
    }
  }, [currentDate, isExpanded]);

  const handleNext = useCallback(() => {
    if (isExpanded) {
      // Navigate to next month
      setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
    } else {
      // Navigate to next week
      const newDate = new Date(currentDate);
      newDate.setDate(currentDate.getDate() + 7);
      setCurrentDate(newDate);
    }
  }, [currentDate, isExpanded]);

  const toggleExpanded = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const handleDatePress = useCallback((day: CalendarDay) => {
    // Allow selection of any date in current month, including future dates without programs
    if (day.isCurrentMonth) {
      onDateSelect(day.date, day.programs, day.commits);
    }
  }, [onDateSelect]);

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const getHeaderTitle = () => {
    if (isExpanded) {
      return `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
    } else {
      // For week view, show the week range
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      const startMonth = monthNames[startOfWeek.getMonth()];
      const endMonth = monthNames[endOfWeek.getMonth()];
      const startDay = startOfWeek.getDate();
      const endDay = endOfWeek.getDate();
      const year = currentDate.getFullYear();

      if (startOfWeek.getMonth() === endOfWeek.getMonth()) {
        return `${startMonth} ${startDay}-${endDay}, ${year}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.navButton} onPress={handlePrev}>
          <MaterialIcons name="chevron-left" size={24} color={colors.text} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.titleContainer} onPress={toggleExpanded}>
          <Text style={styles.monthTitle}>
            {getHeaderTitle()}
          </Text>
          <MaterialIcons
            name={isExpanded ? "expand-less" : "expand-more"}
            size={20}
            color={colors.text}
            style={styles.expandIcon}
          />
        </TouchableOpacity>

        <TouchableOpacity style={styles.navButton} onPress={handleNext}>
          <MaterialIcons name="chevron-right" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Day headers */}
      <View style={styles.dayHeaders}>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <View key={day} style={styles.dayHeader}>
            <Text style={styles.dayHeaderText}>{day}</Text>
          </View>
        ))}
      </View>

      {/* Calendar grid with program bands */}
      <View style={styles.calendarContainer}>
        {/* Connected program and commit bands layer */}
        <View style={styles.bandsLayer}>
          {/* Render commit bands first (below programs) */}
          {commitBands && Array.isArray(commitBands) && commitBands.map((band) =>
            renderConnectedCommitBand(band, calendarData, commitBands, isDark)
          )}

          {/* Render program bands on top */}
          {programBands && Array.isArray(programBands) && programBands.map((band) => {
            return renderConnectedProgramBand(band, calendarData, programBands, commitBands, stackAssignments, isDark);
          })}
        </View>

        {/* Calendar grid */}
        <View style={styles.calendarGrid}>
          {calendarData && Array.isArray(calendarData) && calendarData.map((day, index) => {
            if (!day || !day.date) return null;
            const isSelected = selectedDate === day.date;

            return (
              <TouchableOpacity
                key={`${day.date}-${index}`}
                style={[
                  styles.dayCell,
                  !day.isCurrentMonth && styles.dayCellInactive,
                  day.isToday && styles.dayCellToday,
                  isSelected && styles.dayCellSelected,
                ]}
                onPress={() => handleDatePress(day)}
                disabled={!day.isCurrentMonth}
              >
                {/* Day number */}
                <Text style={[
                  styles.dayText,
                  !day.isCurrentMonth && styles.dayTextInactive,
                  day.isToday && styles.dayTextToday,
                  isSelected && styles.dayTextSelected,
                ]}>
                  {day.day}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: CALENDAR_PADDING,
    marginVertical: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  navButton: {
    padding: 6,
    borderRadius: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 3,
    borderRadius: 8,
  },
  monthTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  expandIcon: {
    marginLeft: 4,
  },
  dayHeaders: {
    flexDirection: 'row',
    marginBottom: 6,
    width: '100%',
  },
  dayHeader: {
    width: DAY_WIDTH_PERCENTAGE as any,
    alignItems: 'center',
    paddingVertical: Platform.OS === 'web' ? 4 : 6, // Less padding on web
  },
  dayHeaderText: {
    fontSize: Platform.OS === 'web' ? 11 : 12, // Smaller font on web
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
  },
  calendarContainer: {
    position: 'relative',
    width: '100%',

  },
  bandsLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    zIndex: 2,
  },
  dayCell: {
    width: DAY_WIDTH_PERCENTAGE as any,
    height: DAY_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    // Web-specific improvements
    ...Platform.select({
      web: {
        cursor: 'pointer' as any,
        transition: 'background-color 0.2s ease' as any,
        userSelect: 'none' as any,
        minHeight: 32, // Reduced from 40 to match smaller DAY_SIZE
        maxHeight: 42, // Reduced from 50 to match smaller DAY_SIZE
      },
    }),
  },

  dayCellInactive: {
    opacity: 0.3,
    ...Platform.select({
      web: {
        cursor: 'not-allowed' as any,
      },
    }),
  },
  dayCellToday: {
    backgroundColor: colors.primary + '20',
    borderRadius: 8,
  },
  dayCellSelected: {
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  dayCellWithPrograms: {
    // Subtle indication that day has programs
  },
  dayText: {
    fontSize: Platform.OS === 'web' ? 14 : 16, // Smaller font on web
    fontFamily: 'MontserratMedium',
    color: colors.text,
    ...Platform.select({
      web: {
        userSelect: 'none' as any,
        lineHeight: 1.2,
      },
    }),
  },
  dayTextInactive: {
    color: colors.textMuted,
    fontFamily: 'MontserratMedium',
    fontSize: Platform.OS === 'web' ? 14 : 16,
  },
  dayTextToday: {
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  dayTextSelected: {
    color: colors.background,
    fontFamily: 'MontserratBold',
  },
});

// Export optimized component with React.memo for better performance
export const NewCalendar = React.memo(NewCalendarComponent, (prevProps, nextProps) => {
  // Custom comparison function for better memoization
  return (
    prevProps.programs === nextProps.programs &&
    prevProps.commits === nextProps.commits &&
    prevProps.selectedDate === nextProps.selectedDate &&
    prevProps.userDaysData === nextProps.userDaysData &&
    prevProps.onDateSelect === nextProps.onDateSelect
  );
});
