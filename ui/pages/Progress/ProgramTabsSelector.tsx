import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../../shared/contexts/ThemeContext";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface Program {
  programId: string;
  programName: string;
  needsAttention?: boolean;
}

interface Commit {
  commitId: string;
  commitTitle: string;
  status?: string;
}

interface TabItem {
  type: 'program' | 'commit';
  id: string;
  name: string;
  needsAttention?: boolean;
  status?: string;
}

interface ProgramTabsSelectorProps {
  programs: Program[];
  commits?: Commit[];
  selectedIndex: number;
  onSelectProgram: (index: number) => void;
  onSelectCommit?: (index: number) => void;
  style?: any;
}

export const ProgramTabsSelector: React.FC<ProgramTabsSelectorProps> = ({
  programs,
  commits = [],
  selectedIndex,
  onSelectProgram,
  onSelectCommit,
  style,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const scrollViewRef = useRef<ScrollView>(null);
  const tabRefs = useRef<{ [key: number]: View | null }>({});

  // Combine programs and commits into a single array
  const allItems: TabItem[] = [
    ...programs.map(program => ({
      type: 'program' as const,
      id: program.programId,
      name: program.programName,
      needsAttention: program.needsAttention,
    })),
    ...commits.map(commit => ({
      type: 'commit' as const,
      id: commit.commitId,
      name: commit.commitTitle,
      status: commit.status,
    }))
  ];

  // Auto-scroll to selected tab
  useEffect(() => {
    if (scrollViewRef.current && tabRefs.current[selectedIndex]) {
      tabRefs.current[selectedIndex]?.measureLayout(
        scrollViewRef.current as any,
        (x, y, width, height) => {
          const scrollToX = Math.max(
            0,
            x - SCREEN_WIDTH / 2 + width / 2
          );
          scrollViewRef.current?.scrollTo({
            x: scrollToX,
            animated: true,
          });
        },
        () => {
          /* measurement failed */
        }
      );
    }
  }, [selectedIndex]);

  if (allItems.length <= 1) {
    return null;
  }

  const handleTabPress = (index: number) => {
    const item = allItems[index];
    if (item.type === 'program') {
      onSelectProgram(index);
    } else if (item.type === 'commit' && onSelectCommit) {
      onSelectCommit(index);
    }
  };

  const renderTab = (item: TabItem, index: number) => {
    const isSelected = index === selectedIndex;
    const isCommit = item.type === 'commit';

    return (
      <TouchableOpacity
        key={`${item.type}-${item.id}`}
        ref={(ref) => (tabRefs.current[index] = ref)}
        style={[
          styles.tab,
          isSelected && styles.tabSelected,
          isCommit && styles.commitTab,
          isCommit && isSelected && styles.commitTabSelected,
        ]}
        onPress={() => handleTabPress(index)}
        activeOpacity={0.7}
      >
        <View style={styles.tabContent}>
          <Text
            style={[
              styles.tabText,
              isSelected && styles.tabTextSelected,
            ]}
            numberOfLines={1}
          >
            {item.name}
          </Text>
          {item.needsAttention && (
            <View style={styles.attentionIndicator}>
              <MaterialIcons
                name="warning"
                size={12}
                color={
                  isSelected
                    ? colors.background
                    : colors.error
                }
              />
            </View>
          )}
          {isCommit && item.status && (
            <View style={[styles.statusDot, {
              backgroundColor: item.status === 'active' ? '#4CAF50' :
                             item.status === 'completed' ? '#2196F3' :
                             item.status === 'failed' ? '#F44336' : '#9E9E9E'
            }]} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
        bounces={false}
        decelerationRate="fast"
      >
        {allItems.map(renderTab)}
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      marginBottom: 8,
      position: 'relative',
    },
    scrollView: {
      flexGrow: 0,
    },
    scrollContent: {
      paddingHorizontal: 16,
      alignItems: 'center',
    },
    tab: {
      backgroundColor: colors.surface,
      borderRadius: 6,
      paddingHorizontal: 12,
      paddingVertical: 4,
      marginHorizontal: 3,
      minWidth: 70,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: colors.textMuted + '15',
    },
    tabSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    tabContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    tabText: {
      fontSize: 13,
      fontFamily: 'MontserratRegular',
      color: colors.text,
      textAlign: 'center',
    },
    tabTextSelected: {
      fontSize: 13,
      fontFamily: 'MontserratBold',
      color: colors.background,
      textAlign: 'center',
    },
    attentionIndicator: {
      marginLeft: 5,
      alignItems: 'center',
      justifyContent: 'center',
    },
    commitTab: {
      borderStyle: 'dashed',
      borderWidth: 1.5,
    },
    commitTabSelected: {
      borderStyle: 'solid',
    },
    statusDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      marginLeft: 4,
    },
  });

export default ProgramTabsSelector;
