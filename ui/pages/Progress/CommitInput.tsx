import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Modal,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../../shared/contexts/ThemeContext";
import { VerificationInput } from "../../verification/VerificationInput";
import { getCommitVerificationConfig } from "../../../shared/types/verification";
import { firestoreService } from "../../../lib/services/database";
import { AccustomText } from "../../../shared/components/AccustomText";
import CommitSetup from './CommitSetup';
import type {
  VerificationData,
  VerificationResult
} from "../../../shared/types/verification";
import { Commit } from "../../../lib/services/database/types";
import { WeeklyProgressIndicator } from './WeeklyProgressIndicator';
import { getGitHubIntegrationStatus } from "../../../lib/utils/integrationHelpers";
import { useFocusEffect } from 'expo-router';
import { AddToCalendar } from "../../../shared/components/AddToCalendar";
import { createCommitmentCalendarEvent } from "../../../lib/utils/calendarEventUtils";


// Commit Countdown Timer Component
const CommitCountdownTimer: React.FC<{ commit: Commit; colors: any }> = ({ commit, colors }) => {
  const [timeLeft, setTimeLeft] = useState<string>("");
  const [isOverdue, setIsOverdue] = useState<boolean>(false);

  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      let targetTime: Date;

      // Get deadline configuration
      const deadline = commit.schedule.deadline;

      if (!deadline || deadline.type === 'midnight') {
        // Default to midnight
        targetTime = new Date();
        targetTime.setHours(24, 0, 0, 0); // Next midnight
      } else if (deadline.type === 'before' && deadline.time) {
        // Before specific time
        targetTime = new Date();
        const [hours, minutes] = deadline.time.split(':').map(Number);
        targetTime.setHours(hours, minutes, 0, 0);

        // If time has passed today, set for tomorrow
        if (targetTime <= now) {
          targetTime.setDate(targetTime.getDate() + 1);
        }
      } else if (deadline.type === 'after' && deadline.time) {
        // After specific time - show countdown until end of day if we're past the start time
        targetTime = new Date();
        const [hours, minutes] = deadline.time.split(':').map(Number);
        const startTime = new Date();
        startTime.setHours(hours, minutes, 0, 0);

        if (now >= startTime) {
          // We're in the submission window, show countdown to end of day
          targetTime.setHours(24, 0, 0, 0); // Next midnight
        } else {
          // We're before the submission window, show countdown to start time
          targetTime = startTime;
        }
      } else if (deadline.type === 'between' && deadline.endTime) {
        // Between times - show countdown until end time
        targetTime = new Date();
        const [hours, minutes] = deadline.endTime.split(':').map(Number);
        targetTime.setHours(hours, minutes, 0, 0);

        // If time has passed today, set for tomorrow
        if (targetTime <= now) {
          targetTime.setDate(targetTime.getDate() + 1);
        }
      } else {
        // Fallback to midnight
        targetTime = new Date();
        targetTime.setHours(24, 0, 0, 0);
      }

      const diff = targetTime.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeLeft("00:00:00");
        setIsOverdue(true);
        return;
      }

      setIsOverdue(false);
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [commit.schedule.deadline]);

  const getDeadlineText = () => {
    const deadline = commit.schedule.deadline;
    const now = new Date();

    if (!deadline || deadline.type === 'midnight') {
      return 'Time left to submit (by midnight):';
    } else if (deadline.type === 'before' && deadline.time) {
      return `Time left to submit (before ${deadline.time}):`;
    } else if (deadline.type === 'after' && deadline.time) {
      const [hours, minutes] = deadline.time.split(':').map(Number);
      const startTime = new Date();
      startTime.setHours(hours, minutes, 0, 0);

      if (now >= startTime) {
        return `Time left to submit (window open until midnight):`;
      } else {
        return `Time until submission window opens (after ${deadline.time}):`;
      }
    } else if (deadline.type === 'between' && deadline.startTime && deadline.endTime) {
      return `Time left to submit (by ${deadline.endTime}):`;
    }
    return 'Time left to submit:';
  };

  const styles = StyleSheet.create({
    countdownContainer: {
      backgroundColor: colors.surface,
      paddingHorizontal: 12,
      paddingVertical: 10,
      borderRadius: 8,
      marginTop: 8,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: isOverdue ? colors.error + '30' : colors.warning + '30',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 8,
    },
    countdownText: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
    },
    countdownTime: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: isOverdue ? colors.error : colors.warning,
    },
  });

  return (
    <View style={styles.countdownContainer}>
      <Text style={styles.countdownText}>{getDeadlineText()}</Text>
      <Text style={styles.countdownTime}>{timeLeft}</Text>
    </View>
  );
};

interface CommitInputProps {
  commit: Commit;
  userId: string;
  onSubmissionSuccess: () => void;
  onCommitUpdate?: (updatedCommit: Commit) => void;
  isLoading?: boolean;
  refreshTrigger?: number;
  selectedDate?: string; // Optional selected date in YYYY-MM-DD format, defaults to today
}

const CommitInput: React.FC<CommitInputProps> = ({
  commit,
  userId,
  onSubmissionSuccess,
  onCommitUpdate,
  isLoading = false,
  refreshTrigger = 0,
  selectedDate,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // State for setup and verification
  const [currentCommit, setCurrentCommit] = useState<Commit>(commit);
  const [initialVerificationData, setInitialVerificationData] = useState<VerificationData | null>(null);
  const [checkingExistingSubmission, setCheckingExistingSubmission] = useState<boolean>(true);
  const [isVerificationValid, setIsVerificationValid] = useState<boolean>(false);
  const [currentDaySubmission, setCurrentDaySubmission] = useState<string>('');
  const [submissionStatus, setSubmissionStatus] = useState<'upcoming' | 'submitted' | 'verified'>('upcoming');
  const [checkingGitHubIntegration, setCheckingGitHubIntegration] = useState<boolean>(false);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [showTechnicalDetails, setShowTechnicalDetails] = useState<boolean>(false);
  const [showHelpModal, setShowHelpModal] = useState<boolean>(false);

  // Weekly mode specific state
  const [weeklyStatus, setWeeklyStatus] = useState<{
    submittedCount: number;
    pendingCount: number;
    canSubmit: boolean;
    canSubmitToday: boolean;
    nextSubmissionId: string | null;
    todaySubmissionExists: boolean;
  } | null>(null);

  // Update current commit when prop changes
  useEffect(() => {
    setCurrentCommit(commit);
  }, [commit]);

  // Clear verification data when commit changes to prevent data leakage between commits
  useEffect(() => {
    setInitialVerificationData(null);
    setSubmissionStatus('upcoming');
    setCheckingExistingSubmission(true);
  }, [commit.id]);

  // Helper function to check if selected date is in the future
  const isFutureDate = (): boolean => {
    const targetDateStr = selectedDate || new Date().toISOString().split('T')[0];

    // Use the same date comparison logic as the calendar
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const targetDateObj = new Date(targetDateStr + 'T00:00:00');
    targetDateObj.setHours(0, 0, 0, 0);

    return targetDateObj > today;
  };

  // Helper function to check if the commit has started yet
  const hasCommitStarted = (): boolean => {
    const startDate = new Date(commit.schedule.startDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);

    return today >= startDate;
  };

  // Calculate current day for the commit based on selected date or today
  const getCurrentDay = (): string => {
    const startDate = new Date(commit.schedule.startDate);
    // Use selected date if provided, otherwise use today
    const targetDate = selectedDate ? new Date(selectedDate) : new Date();
    targetDate.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);

    if (commit.schedule.frequency === 'once') {
      return 'Day 1';
    } else if (commit.schedule.frequency === 'daily') {
      const daysDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return `Day ${Math.max(1, daysDiff + 1)}`;
    } else if (commit.schedule.frequency === 'weekly') {
      const weeksDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
      return `Week ${Math.max(1, weeksDiff + 1)}`;
    } else if (commit.schedule.frequency === 'monthly') {
      const monthsDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
      return `Month ${Math.max(1, monthsDiff + 1)}`;
    }
    return 'Day 1';
  };



  // Handle setup completion
  const handleSetupComplete = (updatedCommit: Commit) => {
    setCurrentCommit(updatedCommit);
    onCommitUpdate?.(updatedCommit);
  };

  // Check for existing submission on mount and when dependencies change
  useEffect(() => {
    const checkExistingSubmission = async () => {
      if (!currentCommit.id || !userId) {
        setCheckingExistingSubmission(false);
        return;
      }

      setCheckingExistingSubmission(true);
      const daySubmission = getCurrentDay();
      setCurrentDaySubmission(daySubmission);

      // Check if the selected date is in the future using the helper function
      if (isFutureDate()) {
        setSubmissionStatus('submitted'); // Show as submitted to hide submission options
        setInitialVerificationData(null);
        setCheckingExistingSubmission(false);
        return;
      }

      try {
        if (currentCommit.schedule.frequency === 'weekly') {
          // Handle weekly mode with multiple submissions per week
          const timesPerWeek = currentCommit.schedule.timesPerWeek || 1;
          // Pass the target date (selected date or today) to check submissions for that specific date
          const targetDate = selectedDate || new Date().toISOString().split('T')[0];
          const weeklyStatusResult = await firestoreService.commits.getWeeklySubmissionStatus(
            currentCommit.id,
            daySubmission,
            timesPerWeek,
            targetDate
          );

          if (weeklyStatusResult.success && weeklyStatusResult.data) {
            const status = weeklyStatusResult.data;
            setWeeklyStatus(status);

            // Check submission status based on daily and weekly limits
            if (!status.canSubmit) {
              // All weekly submissions are done
              setSubmissionStatus('submitted');
            } else if (status.todaySubmissionExists) {
              // Already submitted today, but can submit more this week
              setSubmissionStatus('submitted');
            } else {
              // Can submit today
              setSubmissionStatus('upcoming');
            }

            // Set initial data only from submissions made on the target date
            const targetDate = selectedDate || new Date().toISOString().split('T')[0];
            const targetDateSubmission = status.submissions
              .filter(s => s.status === 'submitted' && s.timestamp && s.timestamp.split('T')[0] === targetDate)
              .sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime())[0];

            if (targetDateSubmission && targetDateSubmission.attachment) {
              try {
                const parsedData = JSON.parse(targetDateSubmission.attachment);
                setInitialVerificationData(parsedData);
              } catch (e) {
                console.warn('Failed to parse existing submission data:', e);
              }
            } else {
              // Clear initial data if no submission exists for the target date
              setInitialVerificationData(null);
            }
          } else {
            setSubmissionStatus('upcoming');
          }
        } else {
          // Handle daily, monthly, and once modes
          const existingSubmission = await firestoreService.commits.getSubmissionDetails(
            currentCommit.id,
            currentCommit.schedule.frequency,
            daySubmission,
            'Submission 1' // Default to first submission for monthly
          );

          if (existingSubmission.success && existingSubmission.data) {
            const submissionData = existingSubmission.data;

            if (submissionData.status === 'submitted' && submissionData.attachment) {
              try {
                const verificationData = JSON.parse(submissionData.attachment);
                setInitialVerificationData(verificationData);
                setSubmissionStatus('submitted');
              } catch (error) {
                console.error('Error parsing verification data:', error);
                setInitialVerificationData(null);
                setSubmissionStatus('upcoming');
              }
            } else {
              setSubmissionStatus('upcoming');
            }
          } else {
            setSubmissionStatus('upcoming');
          }
        }
      } catch (error) {
        console.error('Error checking existing submission:', error);
      } finally {
        setCheckingExistingSubmission(false);
      }
    };

    checkExistingSubmission();
  }, [currentCommit.id, userId, refreshTrigger, selectedDate]);

  // Check for existing GitHub integration and auto-set setupStatus if needed
  useEffect(() => {
    const checkGitHubIntegration = async () => {
      // Only check for GitHub commits that have setupStatus false
      if (currentCommit.evidence?.type !== 'github' || currentCommit.setupStatus) {
        return;
      }

      setCheckingGitHubIntegration(true);

      try {
        const integrationStatus = await getGitHubIntegrationStatus();

        if (integrationStatus.isConnected) {


          // Update commit setupStatus in database
          const result = await firestoreService.commits.updateCommit(currentCommit.id!, {
            setupStatus: true
          });

          if (result.success) {
            // Update local state
            const updatedCommit = {
              ...currentCommit,
              setupStatus: true
            };
            setCurrentCommit(updatedCommit);
            onCommitUpdate?.(updatedCommit);
          } else {
            console.error('Failed to update commit setupStatus:', result.error);
          }
        }
      } catch (error) {
        console.error('Error checking GitHub integration:', error);
      } finally {
        setCheckingGitHubIntegration(false);
      }
    };

    checkGitHubIntegration();
  }, [currentCommit.id, currentCommit.setupStatus, currentCommit.evidence?.type, onCommitUpdate, refreshTrigger]);

  // Re-check GitHub integration when screen comes into focus (e.g., returning from integrations screen)
  useFocusEffect(
    React.useCallback(() => {
      const recheckGitHubIntegration = async () => {
        // Only recheck for GitHub commits that currently have setupStatus true
        if (currentCommit.evidence?.type !== 'github' || !currentCommit.setupStatus) {
          return;
        }

        try {
          const integrationStatus = await getGitHubIntegrationStatus();

          // If GitHub integration is no longer connected, reset setupStatus
          if (!integrationStatus.isConnected) {


            // Update commit setupStatus in database
            const result = await firestoreService.commits.updateCommit(currentCommit.id!, {
              setupStatus: false
            });

            if (result.success) {
              // Update local state
              const updatedCommit = {
                ...currentCommit,
                setupStatus: false
              };
              setCurrentCommit(updatedCommit);
              onCommitUpdate?.(updatedCommit);
            } else {
              console.error('Failed to reset commit setupStatus:', result.error);
            }
          }
        } catch (error) {
          console.error('Error rechecking GitHub integration:', error);
        }
      };

      recheckGitHubIntegration();
    }, [currentCommit.id, currentCommit.setupStatus, currentCommit.evidence?.type, onCommitUpdate])
  );

  // Refresh commit setupStatus from database when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const refreshCommitFromDatabase = async () => {
        if (!currentCommit.id) {
          return;
        }

        try {
          // Fetch the latest commit data from database
          const result = await firestoreService.commits.getCommitById(currentCommit.id);

          if (result.success && result.data) {
            const latestCommit = result.data;

            // Check if setupStatus has changed
            if (latestCommit.setupStatus !== currentCommit.setupStatus) {

              // Update local state with the latest data
              setCurrentCommit(latestCommit);
              onCommitUpdate?.(latestCommit);
            }
          }
        } catch (error) {
          console.error('Error refreshing commit from database:', error);
        }
      };

      refreshCommitFromDatabase();
    }, [currentCommit.id, currentCommit.setupStatus, onCommitUpdate])
  );

  // Handle verification submission
  const handleVerificationSubmit = async (data: VerificationData): Promise<VerificationResult> => {
    if (!currentCommit.evidence?.type) {
      return { success: false, error: "Evidence type is required" };
    }

    try {
      const serializedData = JSON.stringify(data);
      let submissionId = 'Submission 1'; // Default for non-weekly modes

      // For weekly mode, use the next available submission slot
      if (currentCommit.schedule.frequency === 'weekly' && weeklyStatus?.nextSubmissionId) {
        // Double-check that user can submit today
        if (!weeklyStatus.canSubmitToday) {
          return {
            success: false,
            error: weeklyStatus.todaySubmissionExists
              ? "You have already submitted once today. You can submit again tomorrow."
              : "You have completed all submissions for this week."
          };
        }
        submissionId = weeklyStatus.nextSubmissionId;
      }

      // Update commit submission using the helper method
      const result = await firestoreService.commits.updateSubmissionDetails(
        currentCommit.id!,
        currentCommit.schedule.frequency,
        currentDaySubmission,
        {
          attachment: serializedData,
          status: 'submitted',
          timestamp: new Date().toISOString()
        },
        submissionId
      );

      if (!result.success) {
        return { success: false, error: result.error || "Failed to update submission" };
      }

      // Update UI state
      if (currentCommit.schedule.frequency === 'weekly') {
        // Refresh weekly status after submission
        const timesPerWeek = currentCommit.schedule.timesPerWeek || 1;
        // Pass the target date (selected date or today) to check submissions for that specific date
        const targetDate = selectedDate || new Date().toISOString().split('T')[0];
        const weeklyStatusResult = await firestoreService.commits.getWeeklySubmissionStatus(
          currentCommit.id!,
          currentDaySubmission,
          timesPerWeek,
          targetDate
        );

        if (weeklyStatusResult.success && weeklyStatusResult.data) {
          const updatedStatus = weeklyStatusResult.data;
          setWeeklyStatus(updatedStatus);

          // Update submission status based on daily and weekly limits
          if (!updatedStatus.canSubmit) {
            setSubmissionStatus('submitted'); // All weekly submissions are done
            setInitialVerificationData(data); // Keep the data for preview when all done
          } else if (updatedStatus.todaySubmissionExists) {
            setSubmissionStatus('submitted'); // Already submitted today
            setInitialVerificationData(null); // Clear data since they can submit again tomorrow
          } else {
            setSubmissionStatus('upcoming'); // Can still submit today
            setInitialVerificationData(null); // Clear data so user can submit again
          }
        }
      } else {
        setSubmissionStatus('submitted');
        setInitialVerificationData(data);
      }
      onSubmissionSuccess();

      return { success: true, data };
    } catch (error) {
      console.error("Error during verification submission:", error);
      return { success: false, error: "An error occurred during submission" };
    }
  };

  // Get verification config based on commit evidence type
  const getVerificationConfig = () => {
    const evidenceType = currentCommit.evidence?.type || 'photo';
    return getCommitVerificationConfig(evidenceType);
  };

  // Get contextual help content based on commitment type
  const getHelpContent = () => {
    const evidenceType = currentCommit.evidence?.type || 'photo';
    const frequency = currentCommit.schedule?.frequency || 'daily';
    const deadline = currentCommit.schedule?.deadline;

    let title = "How to Complete Your Commitment";
    let content = "";

    // Evidence-specific instructions
    switch (evidenceType) {
      case 'photo':
        title = "📸 Photo Evidence Instructions";
        content = "Take a clear photo that shows you've completed your commitment. Make sure the photo is well-lit and clearly demonstrates your progress or achievement.";
        break;
      case 'video':
        title = "🎥 Video Evidence Instructions";
        content = "Record a video showing you completing your commitment. Keep it brief but clear, and make sure the video demonstrates your activity or progress.";
        break;
      case 'video-timelapse':
        title = "⏱️ Time-lapse Video Instructions";
        content = "Create a time-lapse video of your commitment activity. This is great for showing progress over time or demonstrating a process.";
        break;
      case 'github':
        title = "💻 GitHub Commit Instructions";
        content = `Make commits to your repository: ${currentCommit.evidence?.config?.repository || 'your connected repository'}. Each commit should represent meaningful progress on your coding commitment.`;
        break;
      case 'gps-checkin':
        title = "📍 GPS Check-in Instructions";
        content = `Visit the specified location: ${currentCommit.evidence?.config?.location?.name || 'your target location'}. When you arrive, submit your evidence to confirm your check-in.`;
        break;
      case 'gps-avoid':
        title = "🚫 Location Avoidance Instructions";
        content = `Stay away from: ${currentCommit.evidence?.config?.location?.name || 'the restricted location'}. Submit your evidence at the end of each period to confirm you avoided this location.`;
        break;
      case 'strava':
        title = "🏃 Strava Activity Instructions";
        content = `Complete your ${currentCommit.evidence?.config?.activityType || 'fitness'} activity and make sure it's recorded on Strava. Your Strava activities will be automatically verified.`;
        break;
      case 'screen-time':
        title = "📱 Screen Time Instructions";
        content = `Monitor your usage of ${currentCommit.evidence?.config?.appName || 'the specified app'}. Submit screenshots of your screen time data to show your progress.`;
        break;
      case 'honor':
        title = "🤝 Honor System Instructions";
        content = "Complete your commitment and submit confirmation based on your personal integrity. Provide any relevant details or notes about your completion.";
        break;
      default:
        content = "Submit evidence showing you've completed your commitment for the day.";
    }

    // Add frequency-specific guidance
    if (frequency === 'weekly' && currentCommit.schedule?.timesPerWeek) {
      content += `\n\n📅 Weekly Schedule: You need to complete this ${currentCommit.schedule.timesPerWeek} time${currentCommit.schedule.timesPerWeek > 1 ? 's' : ''} per week. You can spread these out across any days of the week.`;
    } else if (frequency === 'monthly' && currentCommit.schedule?.timesPerMonth) {
      content += `\n\n📅 Monthly Schedule: You need to complete this ${currentCommit.schedule.timesPerMonth} time${currentCommit.schedule.timesPerMonth > 1 ? 's' : ''} per month. You have flexibility in when you complete these within the month.`;
    } else if (frequency === 'daily') {
      content += "\n\n📅 Daily Schedule: Complete this commitment every day.";
    } else if (frequency === 'once') {
      content += "\n\n📅 One-time Commitment: You only need to complete this once within your commitment period.";
    }

    // Add deadline information
    if (deadline) {
      if (deadline.type === 'midnight') {
        content += "\n\n⏰ Deadline: Submit your evidence by midnight each day.";
      } else if (deadline.type === 'before' && deadline.time) {
        const time = new Date(`2000-01-01T${deadline.time}:00`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        content += `\n\n⏰ Deadline: Submit your evidence before ${time} each day.`;
      } else if (deadline.type === 'after' && deadline.time) {
        const time = new Date(`2000-01-01T${deadline.time}:00`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        content += `\n\n⏰ Submission Window: You can submit evidence after ${time} each day.`;
      } else if (deadline.type === 'between' && deadline.startTime && deadline.endTime) {
        const startTime = new Date(`2000-01-01T${deadline.startTime}:00`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        const endTime = new Date(`2000-01-01T${deadline.endTime}:00`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        content += `\n\n⏰ Submission Window: Submit your evidence between ${startTime} and ${endTime} each day.`;
      }
    }

    // Add tips
    content += "\n\n💡 Tips:\n• Submit evidence as soon as you complete your commitment\n• Make sure your evidence clearly shows your progress\n• If you're having trouble, reach out for support";

    return { title, content };
  };

  const getSubmissionTitle = () => {
    // For weekly mode, show contextual titles based on status
    if (currentCommit.schedule.frequency === 'weekly' && weeklyStatus) {
      if (!weeklyStatus.canSubmit) {
        return 'Weekly Challenge Complete! 🏆';
      } else if (weeklyStatus.todaySubmissionExists) {
        return 'Submission Recorded';
      } else if (weeklyStatus.canSubmitToday) {
        return 'Ready to Submit';
      } else {
        return 'Submission Status';
      }
    }

    // For other modes, show evidence-specific titles
    const evidenceType = currentCommit.evidence?.type || 'photo';

    // Handle all evidence types with a more flexible approach using string matching
    const evidenceTypeStr = evidenceType as string;

    if (evidenceTypeStr === 'photo') {
      return 'Submit your Photo Evidence';
    } else if (evidenceTypeStr === 'video' || evidenceTypeStr.includes('video')) {
      return 'Submit your Video Evidence';
    } else if (evidenceTypeStr.includes('camera')) {
      return 'Submit your Camera Evidence';
    } else if (evidenceTypeStr === 'gps-checkin') {
      return 'Check in at Location';
    } else if (evidenceTypeStr === 'gps-avoid') {
      return 'Confirm Location Avoidance';
    } else if (evidenceTypeStr === 'strava') {
      return 'Submit your Strava Activity';
    } else if (evidenceTypeStr === 'github') {
      return 'GitHub Commit Verification';
    } else if (evidenceTypeStr === 'screen-time') {
      return 'Submit your Screen Time Data';
    } else if (evidenceTypeStr === 'honor') {
      return 'Submit your Honor System Evidence';
    } else {
      return 'Submit your Evidence';
    }
  };

  // Helper function to get ordinal numbers (1st, 2nd, 3rd, 4th, etc.)
  const getOrdinalNumber = (num: number): string => {
    const suffix = ['th', 'st', 'nd', 'rd'];
    const value = num % 100;
    return num + (suffix[(value - 20) % 10] || suffix[value] || suffix[0]);
  };

  // Render submitted status UI
  const renderSubmittedStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="check-circle" size={24} color={colors.primary} />
        <Text style={styles.statusHeaderTitle}>Great job! 🎉</Text>
      </View>
      <Text style={styles.statusSubtitle}>
        {currentCommit.schedule.frequency === 'weekly' && weeklyStatus
          ? !weeklyStatus.canSubmit
            ? `Perfect! All submissions completed this week 🏆`
            : `${getOrdinalNumber(weeklyStatus.submittedCount)} submission recorded! 🎯`
          : `Submission completed successfully`
        }
      </Text>
      <AccustomText
        accustomSize="inherit"
        style={styles.statusDescription}
      >
        Your evidence has been submitted successfully. The Accustom Team will review your progress.
      </AccustomText>

      {/* Show submitted evidence preview */}
      {initialVerificationData && (
        <View style={styles.evidencePreview}>
          <Text style={styles.evidencePreviewTitle}>Submitted Evidence:</Text>
          {initialVerificationData.images && initialVerificationData.images.length > 0 && (
            <Text style={styles.evidencePreviewText}>
              📸 {initialVerificationData.images.length} photo{initialVerificationData.images.length > 1 ? 's' : ''} submitted
            </Text>
          )}
          {initialVerificationData.location && (
            <Text style={styles.evidencePreviewText}>
              📍 Location verified
            </Text>
          )}
          {initialVerificationData.textValue && (
            <Text style={styles.evidencePreviewText}>
              📝 Text submission: {initialVerificationData.textValue.substring(0, 50)}...
            </Text>
          )}
        </View>
      )}
    </View>
  );

  // Render verified status UI
  const renderVerifiedStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="verified" size={24} color={colors.success || '#4CAF50'} />
        <Text style={[styles.statusHeaderTitle, { color: colors.success || '#4CAF50' }]}>
          Verified! ✅
        </Text>
      </View>
      <Text style={styles.statusSubtitle}>
        Submission verified for {currentDaySubmission}
      </Text>
      <AccustomText
        accustomSize="inherit"
        style={styles.statusDescription}
      >
        Your commitment has been reviewed and verified by the Accustom Team. Excellent work!
      </AccustomText>
    </View>
  );

  // Render future date status UI
  const renderFutureDateStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="schedule" size={24} color={colors.textMuted} />
        <Text style={[styles.statusHeaderTitle, { color: colors.textMuted }]}>
          Future Date
        </Text>
      </View>
      <Text style={styles.statusSubtitle}>
        This date hasn't arrived yet
      </Text>
      <Text style={styles.statusDescription}>
        You can only submit evidence for today or past dates. Come back on this date to submit your evidence.
      </Text>
    </View>
  );

  // Render commit not started status UI
  const renderCommitNotStartedStatus = () => {
    const startDate = new Date(commit.schedule.startDate);
    const today = new Date();
    const daysUntilStart = Math.ceil((startDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    return (
      <View style={styles.statusContainer}>
        <View style={styles.statusHeader}>
          <MaterialIcons name="schedule" size={24} color={colors.primary} />
          <Text style={[styles.statusHeaderTitle, { color: colors.text }]}>
            Commit Starts Soon
          </Text>
        </View>
        <Text style={styles.statusSubtitle}>
          Your commit will begin on {startDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
          {daysUntilStart === 1 ? ' (tomorrow)' : ` (in ${daysUntilStart} days)`}.
        </Text>
        <Text style={styles.statusDescription}>
          You'll be able to submit your first evidence then.
        </Text>
      </View>
    );
  };

  // Show loading if checking GitHub integration
  if (checkingGitHubIntegration) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.loadingText}>Checking GitHub integration...</Text>
      </View>
    );
  }

  // Show setup if setupStatus is false
  if (!currentCommit.setupStatus) {
    return (
      <CommitSetup
        commit={currentCommit}
        onSetupComplete={handleSetupComplete}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Commit Info - Always at top */}
      <View style={styles.commitInfo}>
        <View style={styles.commitInfoHeader}>
          {/* Help Button */}
          <TouchableOpacity
            onPress={() => setShowHelpModal(true)}
            style={styles.headerButton}
            activeOpacity={0.7}
          >
            <MaterialIcons name="help-outline" size={20} color={colors.textMuted} />
          </TouchableOpacity>

          <View style={styles.commitInfoText}>
            <Text style={styles.commitTitle}>{currentCommit.title}</Text>
            <Text style={styles.commitFrequency}>
              {currentCommit.schedule.frequency.charAt(0).toUpperCase() + currentCommit.schedule.frequency.slice(1)} • {currentCommit.evidence?.type?.charAt(0).toUpperCase() + currentCommit.evidence?.type?.slice(1) || 'Photo'}
            </Text>
          </View>

          {/* Info/Settings Button */}
          <TouchableOpacity
            onPress={() => setShowSettings(true)}
            style={styles.headerButton}
            activeOpacity={0.7}
          >
            <MaterialIcons name="info" size={20} color={colors.textMuted} />
          </TouchableOpacity>
        </View>
      </View>



      {/* Weekly Progress Indicator - Only for weekly mode */}
      {currentCommit.schedule.frequency === 'weekly' && weeklyStatus && (
        <WeeklyProgressIndicator
          submittedCount={weeklyStatus.submittedCount}
          totalRequired={weeklyStatus.submittedCount + weeklyStatus.pendingCount}
          todaySubmissionExists={weeklyStatus.todaySubmissionExists}
          canSubmitToday={weeklyStatus.canSubmitToday}
          style={styles.weeklyProgress}
        />
      )}

      {/* Status Title */}
      <View style={styles.statusTitleContainer}>
        <Text style={styles.statusTitle}>
          {getSubmissionTitle()}
        </Text>
      </View>

      {/* Countdown Timer - only show for ongoing unsubmitted commits that have started */}
      {!checkingExistingSubmission && hasCommitStarted() && submissionStatus === 'upcoming' &&
       !(currentCommit.schedule.frequency === 'weekly' && weeklyStatus && !weeklyStatus.canSubmitToday) && (
        <CommitCountdownTimer commit={currentCommit} colors={colors} />
      )}

      {checkingExistingSubmission ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : !hasCommitStarted() ? (
        renderCommitNotStartedStatus()
      ) : isFutureDate() ? (
        renderFutureDateStatus()
      ) : submissionStatus === 'verified' ? (
        renderVerifiedStatus()
      ) : submissionStatus === 'submitted' ? (
        // For weekly mode, show submitted status if user cannot submit today or has completed all submissions
        // For other modes, always show submitted status
        currentCommit.schedule.frequency === 'weekly'
          ? (weeklyStatus && (!weeklyStatus.canSubmitToday || !weeklyStatus.canSubmit) ? renderSubmittedStatus() : (
              <VerificationInput
                config={getVerificationConfig()}
                programId={currentCommit.id || ''}
                userId={userId}
                day={parseInt(currentDaySubmission.split(' ')[1]) || 1}
                onSubmit={handleVerificationSubmit}
                onValidationChange={setIsVerificationValid}
                initialData={initialVerificationData}
                disabled={isLoading}
              />
            ))
          : renderSubmittedStatus()
      ) : (
        <VerificationInput
          config={getVerificationConfig()}
          programId={currentCommit.id || ''}
          userId={userId}
          day={parseInt(currentDaySubmission.split(' ')[1]) || 1}
          onSubmit={handleVerificationSubmit}
          onValidationChange={setIsVerificationValid}
          initialData={initialVerificationData}
          disabled={isLoading || (currentCommit.schedule.frequency === 'weekly' && weeklyStatus && !weeklyStatus.canSubmitToday) || false}
        />
      )}

      {/* Commit Info Modal */}
      {showSettings && (
        <Modal
          visible={showSettings}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowSettings(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <View style={styles.modalHeaderContent}>
                <MaterialIcons name="info" size={24} color={colors.primary} />
                <Text style={styles.modalTitle}>Commitment Overview</Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowSettings(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalContent}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.modalScrollContent}
            >
              {/* Hero Section - Most Important Info */}
              <View style={styles.heroCard}>
                <View style={styles.heroHeader}>
                  <Text style={styles.heroTitle}>{currentCommit.title}</Text>
                  <View style={styles.statusBadge}>
                    <Text style={[styles.statusText, {
                      color: currentCommit.status === 'active' ? colors.success :
                             currentCommit.status === 'completed' ? colors.primary :
                             currentCommit.status === 'failed' ? colors.error : colors.textMuted
                    }]}>
                      {currentCommit.status.toUpperCase()}
                    </Text>
                  </View>
                </View>

                {currentCommit.description && (
                  <Text style={styles.heroDescription}>{currentCommit.description}</Text>
                )}

                <View style={styles.heroStats}>
                  <View style={styles.heroStat}>
                    <Text style={styles.heroStatLabel}>Current</Text>
                    <Text style={styles.heroStatValue}>{getCurrentDay()}</Text>
                  </View>
                  <View style={styles.heroStat}>
                    <Text style={styles.heroStatLabel}>Status</Text>
                    <Text style={[styles.heroStatValue, {
                      color: submissionStatus === 'submitted' ? colors.success :
                             submissionStatus === 'verified' ? colors.primary :
                             submissionStatus === 'upcoming' ? colors.warning : colors.textMuted
                    }]}>
                      {submissionStatus === 'submitted' ? 'Done' :
                       submissionStatus === 'verified' ? 'Verified' :
                       submissionStatus === 'upcoming' ? 'Pending' : 'Unknown'}
                    </Text>
                  </View>
                  <View style={styles.heroStat}>
                    <Text style={styles.heroStatLabel}>Days Active</Text>
                    <Text style={styles.heroStatValue}>
                      {(() => {
                        const startDate = new Date(currentCommit.schedule.startDate);
                        const today = new Date();
                        const daysDiff = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                        return Math.max(0, daysDiff);
                      })()}
                    </Text>
                  </View>
                </View>
              </View>

              {/* Quick Overview Cards */}
              <View style={styles.quickOverview}>
                <View style={styles.quickCard}>
                  <MaterialIcons name="schedule" size={20} color={colors.primary} />
                  <Text style={styles.quickCardLabel}>Frequency</Text>
                  <Text style={styles.quickCardValue}>
                    {currentCommit.schedule?.frequency?.charAt(0).toUpperCase() + currentCommit.schedule?.frequency?.slice(1)}
                  </Text>
                </View>

                <View style={styles.quickCard}>
                  <MaterialIcons name="camera-alt" size={20} color={colors.primary} />
                  <Text style={styles.quickCardLabel}>Evidence</Text>
                  <Text style={styles.quickCardValue}>
                    {(() => {
                      const type = currentCommit.evidence?.type;
                      switch (type) {
                        case 'photo': return 'Photo';
                        case 'video': return 'Video';
                        case 'github': return 'GitHub';
                        case 'gps-checkin': return 'GPS';
                        case 'strava': return 'Strava';
                        default: return type?.charAt(0).toUpperCase() + type?.slice(1) || 'Photo';
                      }
                    })()}
                  </Text>
                </View>

                <View style={styles.quickCard}>
                  <MaterialIcons name="notifications" size={20} color={colors.primary} />
                  <Text style={styles.quickCardLabel}>Reminders</Text>
                  <Text style={styles.quickCardValue}>
                    {(currentCommit as any).notificationPreferences?.reminderTime ?
                      new Date(`2000-01-01T${(currentCommit as any).notificationPreferences.reminderTime}:00`).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                      }) : '7:00 PM'
                    }
                  </Text>
                </View>
              </View>

              {/* Calendar Integration */}
              <View style={styles.configCard}>
                <Text style={styles.configCardTitle}>📅 Calendar Integration</Text>
                <View style={styles.calendarSection}>
                  <Text style={styles.calendarDescription}>
                    Add this commitment to your calendar to get reminders and stay on track with your goals.
                  </Text>
                  <AddToCalendar
                    event={createCommitmentCalendarEvent(currentCommit)}
                    onSuccess={async (eventId) => {
                      // Update the commit with calendar info
                      const calendarData = {
                        eventId,
                        addedAt: new Date().toISOString(),
                        isAdded: true,
                      };

                      const updatedCommit = {
                        ...currentCommit,
                        calendar: calendarData
                      };

                      // Update local state immediately
                      setCurrentCommit(updatedCommit);
                      onCommitUpdate?.(updatedCommit);

                      // Update Firebase directly to ensure persistence
                      if (currentCommit.id) {
                        try {
                          await firestoreService.commits.updateCommit(currentCommit.id, {
                            calendar: calendarData
                          });
                        } catch (error) {
                          console.error('Failed to update calendar status in Firebase:', error);
                        }
                      }
                    }}
                    onError={(error) => {
                      console.error('Calendar integration error:', error);
                    }}
                    isAdded={false} // Always allow adding to calendar
                    buttonText={currentCommit.calendar?.isAdded ? "Add to Calendar Again" : "Add to Calendar"}
                    showWarning={currentCommit.calendar?.isAdded || false}
                    warningMessage={currentCommit.calendar?.isAdded ?
                      `You already added this commitment to your calendar on ${new Date(currentCommit.calendar.addedAt || '').toLocaleDateString()}. Adding again will create a duplicate event.` :
                      undefined
                    }
                    style={styles.calendarButton}
                    variant="outline"
                    size="medium"
                  />
                </View>
              </View>

              {/* Schedule & Timeline */}
              <View style={styles.configCard}>
                <Text style={styles.configCardTitle}>📅 Schedule & Timeline</Text>

                <View style={styles.timelineContainer}>
                  {/* Start & End Dates - Combined */}
                  <View style={styles.timelineItem}>
                    <View style={styles.timelineIcon}>
                      <MaterialIcons name="date-range" size={16} color={colors.primary} />
                    </View>
                    <View style={styles.timelineContent}>
                      <Text style={styles.timelineLabel}>Duration</Text>
                      <Text style={styles.timelineValue}>
                        {new Date(currentCommit.schedule?.startDate).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric',
                        })}
                        {currentCommit.schedule?.endDate && (
                          ` → ${new Date(currentCommit.schedule.endDate).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric',
                          })}`
                        )}
                      </Text>
                    </View>
                  </View>

                  {/* Frequency & Daily Deadline - Combined */}
                  <View style={styles.timelineItem}>
                    <View style={styles.timelineIcon}>
                      <MaterialIcons name="schedule" size={16} color={colors.warning} />
                    </View>
                    <View style={styles.timelineContent}>
                      <Text style={styles.timelineLabel}>Schedule</Text>
                      <Text style={styles.timelineValue}>
                        {currentCommit.schedule?.frequency?.charAt(0).toUpperCase() + currentCommit.schedule?.frequency?.slice(1)}
                        {currentCommit.schedule?.frequency === 'weekly' && currentCommit.schedule?.timesPerWeek &&
                          ` (${currentCommit.schedule.timesPerWeek}x/week)`
                        }
                        {currentCommit.schedule?.frequency === 'monthly' && currentCommit.schedule?.timesPerMonth &&
                          ` (${currentCommit.schedule.timesPerMonth}x/month)`
                        }
                        {currentCommit.schedule?.deadline && (
                          ` • ${
                            currentCommit.schedule.deadline.type === 'midnight' ? 'by midnight' :
                            currentCommit.schedule.deadline.type === 'before' && currentCommit.schedule.deadline.time ?
                              `before ${new Date(`2000-01-01T${currentCommit.schedule.deadline.time}:00`).toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                              })}` :
                            currentCommit.schedule.deadline.type === 'after' && currentCommit.schedule.deadline.time ?
                              `after ${new Date(`2000-01-01T${currentCommit.schedule.deadline.time}:00`).toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                              })}` :
                            currentCommit.schedule.deadline.type === 'between' && currentCommit.schedule.deadline.startTime && currentCommit.schedule.deadline.endTime ?
                              `${new Date(`2000-01-01T${currentCommit.schedule.deadline.startTime}:00`).toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                              })}-${new Date(`2000-01-01T${currentCommit.schedule.deadline.endTime}:00`).toLocaleTimeString('en-US', {
                                hour: 'numeric',
                                minute: '2-digit',
                                hour12: true
                              })}` : ''
                          }`
                        )}
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Progress Summary */}
                <View style={styles.progressSummary}>
                  <Text style={styles.progressTitle}>Progress Summary</Text>
                  <View style={styles.progressStats}>
                    <View style={styles.progressStat}>
                      <Text style={styles.progressStatValue}>
                        {(() => {
                          const freq = currentCommit.schedule?.frequency;
                          const duration = currentCommit.schedule?.duration || 1;

                          if (freq === 'once') return '1';
                          if (freq === 'daily') return `${duration}`;
                          if (freq === 'weekly') {
                            const timesPerWeek = currentCommit.schedule?.timesPerWeek || 1;
                            return `${duration * timesPerWeek}`;
                          }
                          if (freq === 'monthly') {
                            const timesPerMonth = currentCommit.schedule?.timesPerMonth || 1;
                            return `${duration * timesPerMonth}`;
                          }
                          return '?';
                        })()}
                      </Text>
                      <Text style={styles.progressStatLabel}>Total Expected</Text>
                    </View>

                    {currentCommit.schedule?.endDate && (
                      <View style={styles.progressStat}>
                        <Text style={styles.progressStatValue}>
                          {(() => {
                            const endDate = new Date(currentCommit.schedule.endDate);
                            const today = new Date();
                            const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                            return Math.max(0, daysLeft);
                          })()}
                        </Text>
                        <Text style={styles.progressStatLabel}>Days Left</Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>



              {/* Weekly Progress for weekly commits */}
              {currentCommit.schedule.frequency === 'weekly' && weeklyStatus && (
                <View style={styles.configCard}>
                  <Text style={styles.configCardTitle}>📊 Weekly Progress</Text>

                  <View style={styles.weeklyProgressContainer}>
                    <View style={styles.weeklyProgressBar}>
                      <View
                        style={[
                          styles.weeklyProgressFill,
                          {
                            width: `${(weeklyStatus.submittedCount / (weeklyStatus.submittedCount + weeklyStatus.pendingCount)) * 100}%`,
                            backgroundColor: colors.success
                          }
                        ]}
                      />
                    </View>
                    <Text style={styles.weeklyProgressText}>
                      {weeklyStatus.submittedCount} of {weeklyStatus.submittedCount + weeklyStatus.pendingCount} completed this week
                    </Text>

                    <View style={styles.weeklyStatusRow}>
                      <Text style={[styles.weeklyStatusText, {
                        color: weeklyStatus.canSubmitToday ? colors.success : colors.textMuted
                      }]}>
                        {weeklyStatus.canSubmitToday ? '✅ Can submit today' : '❌ Cannot submit today'}
                        {weeklyStatus.todaySubmissionExists ? ' (Already done today)' : ''}
                      </Text>
                    </View>
                  </View>
                </View>
              )}

              {/* Collapsible Technical Details */}
              <TouchableOpacity
                style={styles.technicalToggle}
                onPress={() => setShowTechnicalDetails(!showTechnicalDetails)}
              >
                <MaterialIcons name="info-outline" size={16} color={colors.textMuted} />
                <Text style={styles.technicalToggleText}>Technical Details</Text>
                <MaterialIcons
                  name={showTechnicalDetails ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                  size={16}
                  color={colors.textMuted}
                />
              </TouchableOpacity>

              {/* Technical Details Section - Collapsible */}
              {showTechnicalDetails && (
                <View style={styles.configCard}>
                  <Text style={styles.configCardTitle}>⚙️ Technical Information</Text>

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>Commitment ID</Text>
                    <Text style={styles.configValue}>{currentCommit.id}</Text>
                  </View>

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>User ID</Text>
                    <Text style={styles.configValue}>{currentCommit.userId}</Text>
                  </View>

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>Setup Status</Text>
                    <Text style={[styles.configValue, {
                      color: currentCommit.setupStatus ? colors.success : colors.warning
                    }]}>
                      {currentCommit.setupStatus ? 'Complete ✅' : 'Incomplete ⚠️'}
                    </Text>
                  </View>

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>Timezone</Text>
                    <Text style={styles.configValue}>{currentCommit.timezone || 'UTC'}</Text>
                  </View>

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>Created</Text>
                    <Text style={styles.configValue}>
                      {currentCommit.createdAt ? (() => {
                        const date = typeof currentCommit.createdAt === 'string'
                          ? new Date(currentCommit.createdAt)
                          : currentCommit.createdAt.toDate ? currentCommit.createdAt.toDate() : new Date();
                        return date.toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: 'numeric',
                          minute: '2-digit',
                          hour12: true
                        });
                      })() : 'Unknown'}
                    </Text>
                  </View>

                  {currentCommit.updatedAt && (
                    <View style={styles.configSection}>
                      <Text style={styles.configLabel}>Last Updated</Text>
                      <Text style={styles.configValue}>
                        {(() => {
                          const date = typeof currentCommit.updatedAt === 'string'
                            ? new Date(currentCommit.updatedAt)
                            : currentCommit.updatedAt.toDate ? currentCommit.updatedAt.toDate() : new Date();
                          return date.toLocaleDateString('en-US', {
                            weekday: 'short',
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                          });
                        })()}
                      </Text>
                    </View>
                  )}

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>Database Collection</Text>
                    <Text style={styles.configValue}>commits</Text>
                  </View>

                  <View style={styles.configSection}>
                    <Text style={styles.configLabel}>Current Progress Data</Text>
                    <Text style={styles.configValue}>
                      Current: {getCurrentDay()}{'\n'}
                      Status: {submissionStatus}{'\n'}
                      Days Active: {(() => {
                        const startDate = new Date(currentCommit.schedule.startDate);
                        const today = new Date();
                        const daysDiff = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                        return Math.max(0, daysDiff);
                      })()}
                      {currentCommit.schedule.endDate && `\nDays Remaining: ${(() => {
                        const endDate = new Date(currentCommit.schedule.endDate);
                        const today = new Date();
                        const daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                        return Math.max(0, daysLeft);
                      })()}`}
                    </Text>
                  </View>

                  {/* Weekly Status Details for weekly commits */}
                  {currentCommit.schedule.frequency === 'weekly' && weeklyStatus && (
                    <View style={styles.configSection}>
                      <Text style={styles.configLabel}>Weekly Status Details</Text>
                      <Text style={styles.configValue}>
                        Submitted: {weeklyStatus.submittedCount}{'\n'}
                        Pending: {weeklyStatus.pendingCount}{'\n'}
                        Can Submit: {weeklyStatus.canSubmit ? 'Yes' : 'No'}{'\n'}
                        Can Submit Today: {weeklyStatus.canSubmitToday ? 'Yes' : 'No'}{'\n'}
                        Today Submission Exists: {weeklyStatus.todaySubmissionExists ? 'Yes' : 'No'}
                        {weeklyStatus.nextSubmissionId && `\nNext Submission ID: ${weeklyStatus.nextSubmissionId}`}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </ScrollView>
          </View>
        </Modal>
      )}

      {/* Help Modal */}
      {showHelpModal && (
        <Modal
          visible={showHelpModal}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowHelpModal(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <View style={styles.modalHeaderContent}>
                <MaterialIcons name="help-outline" size={24} color={colors.primary} />
                <Text style={styles.modalTitle}>{getHelpContent().title}</Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowHelpModal(false)}
                style={styles.modalCloseButton}
              >
                <MaterialIcons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalContent}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.modalScrollContent}
            >
              <View style={styles.helpCard}>
                <Text style={styles.helpContent}>{getHelpContent().content}</Text>
              </View>

              {/* Current Status */}
              <View style={styles.helpCard}>
                <Text style={styles.helpSectionTitle}>📊 Your Current Status</Text>
                <Text style={styles.helpStatusText}>
                  You are on {getCurrentDay()} of your commitment.
                  {submissionStatus === 'submitted' && " ✅ You've already submitted evidence for today!"}
                  {submissionStatus === 'verified' && " 🎯 Your evidence has been verified!"}
                  {submissionStatus === 'upcoming' && " ⏳ You still need to submit evidence for today."}
                </Text>

                {/* Weekly progress for weekly commits */}
                {currentCommit.schedule.frequency === 'weekly' && weeklyStatus && (
                  <Text style={styles.helpStatusText}>
                    This week: {weeklyStatus.submittedCount} of {weeklyStatus.submittedCount + weeklyStatus.pendingCount} submissions completed.
                    {weeklyStatus.canSubmitToday ? " You can still submit today!" : " You cannot submit more today."}
                  </Text>
                )}
              </View>

              {/* Quick Actions */}
              <View style={styles.helpCard}>
                <Text style={styles.helpSectionTitle}>🚀 Quick Actions</Text>
                <TouchableOpacity
                  style={styles.helpActionButton}
                  onPress={() => {
                    setShowHelpModal(false);
                    // Focus will return to the main submission interface
                  }}
                >
                  <MaterialIcons name="camera-alt" size={20} color={colors.primary} />
                  <Text style={styles.helpActionText}>Submit Evidence Now</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </Modal>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    margin: 16,
    borderWidth: 1,
    borderColor: colors.border || colors.textMuted + '20',
    borderStyle: 'dashed',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
  },
  dayIndicator: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: colors.primary,
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  weeklyProgress: {
    marginVertical: 14,
    paddingHorizontal: 4,
  },
  commitInfo: {
    marginBottom: 8,
    paddingBottom: 14,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  commitInfoHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  commitInfoText: {
    flex: 1,
    paddingRight: 8,
  },
  statusTitleContainer: {
    marginBottom: 14,
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: 0.3,
    lineHeight: 24,
  },
  commitTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 5,
    textAlign: 'center',
  },
  commitFrequency: {
    fontSize: 13,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 8,
  },
  statusContainer: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusHeaderTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginLeft: 8,
  },
  statusSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  statusDescription: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  evidencePreview: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    width: '100%',
  },
  evidencePreviewTitle: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
  },
  evidencePreviewText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    marginBottom: 4,
  },
  headerButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: colors.surface,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: 8,
    borderRadius: 6,
  },
  modalContent: {
    flex: 1,
  },
  modalScrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  configCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  configCardTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 16,
  },
  configSection: {
    marginBottom: 12,
  },
  configLabel: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  configValue: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    lineHeight: 20,
  },
  configSubValue: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    lineHeight: 18,
    marginTop: 2,
  },
  // Hero section styles
  heroCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  heroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  heroTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    marginRight: 12,
  },
  statusBadge: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 11,
    fontFamily: 'MontserratBold',
    letterSpacing: 0.5,
  },
  heroDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    lineHeight: 20,
    marginBottom: 16,
  },
  heroStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  heroStat: {
    alignItems: 'center',
  },
  heroStatLabel: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
    marginBottom: 4,
  },
  heroStatValue: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  // Quick overview styles
  quickOverview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    gap: 8,
  },
  quickCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  quickCardLabel: {
    fontSize: 11,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
    marginTop: 6,
    marginBottom: 2,
    textAlign: 'center',
  },
  quickCardValue: {
    fontSize: 13,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
  },
  // Timeline styles
  timelineContainer: {
    marginBottom: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  timelineContent: {
    flex: 1,
  },
  timelineLabel: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
    marginBottom: 2,
  },
  timelineValue: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
  },
  // Progress summary styles
  progressSummary: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  progressTitle: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  progressStat: {
    alignItems: 'center',
  },
  progressStatValue: {
    fontSize: 24,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 4,
  },
  progressStatLabel: {
    fontSize: 11,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
    textAlign: 'center',
  },
  // Evidence and notification styles
  evidenceRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  evidenceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  evidenceContent: {
    flex: 1,
  },
  evidenceType: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 4,
  },
  evidenceDetail: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    lineHeight: 18,
  },
  notificationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  notificationText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    flex: 1,
    lineHeight: 18,
  },
  // Weekly progress styles
  weeklyProgressContainer: {
    alignItems: 'center',
  },
  weeklyProgressBar: {
    width: '100%',
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  weeklyProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  weeklyProgressText: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  weeklyStatusRow: {
    alignItems: 'center',
  },
  weeklyStatusText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
  },
  // Technical details toggle
  technicalToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    gap: 6,
  },
  technicalToggleText: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.textMuted,
  },
  // Help modal styles
  helpCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  helpContent: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    lineHeight: 22,
  },
  helpSectionTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
  },
  helpStatusText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  helpActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary + '20',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  helpActionText: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: colors.primary,
  },
  // Calendar integration styles
  calendarSection: {
    alignItems: 'center',
    gap: 12,
  },
  calendarDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 4,
  },
  calendarButton: {
    minWidth: 160,
  },
});

export default CommitInput;
