import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { githubService } from "../../lib/services/githubService";
import { SuccessModal, ErrorModal } from "../../shared/components/modals";

export const GitHubAuthTest: React.FC = () => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [authStatus, setAuthStatus] = useState<string>('Not authenticated');

  // Modal states for replacing Alert dialogs
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '' });
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });

  const styles = createStyles(colors);

  const handleTestAuth = async () => {
    try {
      setLoading(true);
      setAuthStatus('Starting authentication...');


      
      await githubService.initialize();
      const result = await githubService.authenticate();

      if (result.success) {
        setAuthStatus('✅ Authentication successful!');
        setSuccessModalData({
          title: 'Success',
          message: 'GitHub authentication completed successfully!'
        });
        setSuccessModalVisible(true);

        // Try to get user data
        const userData = await githubService.getUserData();
        if (userData) {
          setAuthStatus(`✅ Authenticated as: ${userData.login}`);
        }
      } else {
        setAuthStatus(`❌ Authentication failed: ${result.error}`);
        setErrorModalData({
          title: 'Authentication Failed',
          message: result.error || 'Unknown error occurred'
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error('🔥 GitHub auth test error:', error);
      setAuthStatus(`💥 Error: ${error}`);
      setErrorModalData({
        title: 'Error',
        message: `An unexpected error occurred: ${error}`
      });
      setErrorModalVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckStatus = async () => {
    try {
      await githubService.initialize();
      const isAuth = githubService.isAuthenticated();
      
      if (isAuth) {
        const userData = await githubService.getUserData();
        setAuthStatus(`✅ Already authenticated as: ${userData?.login || 'Unknown'}`);
      } else {
        setAuthStatus('❌ Not authenticated');
      }
    } catch (error) {
      setAuthStatus(`💥 Error checking status: ${error}`);
    }
  };

  const handleClearAuth = async () => {
    try {
      await githubService.clearAuthentication();
      setAuthStatus('🧹 Authentication cleared');
      setSuccessModalData({
        title: 'Cleared',
        message: 'GitHub authentication has been cleared'
      });
      setSuccessModalVisible(true);
    } catch (error) {
      setAuthStatus(`💥 Error clearing auth: ${error}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>GitHub OAuth Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Status:</Text>
        <Text style={styles.statusText}>{authStatus}</Text>
      </View>

      <TouchableOpacity
        style={[styles.button, styles.primaryButton, loading && styles.disabledButton]}
        onPress={handleTestAuth}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Test GitHub Auth</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.secondaryButton]}
        onPress={handleCheckStatus}
        disabled={loading}
      >
        <Text style={[styles.buttonText, { color: colors.primary }]}>Check Status</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.dangerButton]}
        onPress={handleClearAuth}
        disabled={loading}
      >
        <Text style={styles.buttonText}>Clear Auth</Text>
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          This test will open a WebView modal for GitHub authentication on mobile,
          or a popup window on web. Check the console for detailed logs.
        </Text>
      </View>

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  statusContainer: {
    backgroundColor: colors.card,
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 14,
    color: colors.textMuted,
    fontFamily: 'monospace',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  dangerButton: {
    backgroundColor: colors.error,
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  infoContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 14,
    color: colors.textMuted,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default GitHubAuthTest;
