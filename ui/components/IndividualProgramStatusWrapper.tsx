/**
 * Individual Program Status Wrapper
 * 
 * This component wraps existing UI components and provides individual program status
 * instead of centralized program status, making the transition seamless.
 */

import React, { useMemo } from 'react';
import { Participant } from "../../lib/services/database/types";
import { Program } from "../../shared/types/CommonInterface";
import {
  useIndividualProgramStatus,
  useProgramDay,
  useIsProgramActive,
  useProgramTimeline
} from "../../lib/hooks/useIndividualProgramStatus";
import {
  calculateTransitionalProgramStatus,
  TransitionalProgramStatus
} from "../../lib/utils/individualProgramStatus";

interface IndividualProgramStatusWrapperProps {
  program: Program;
  participant: Participant | null;
  userId: string;
  children: (props: {
    // Enhanced program object with individual status
    enhancedProgram: Program;
    // Individual status information
    individualStatus: 'upcoming' | 'ongoing' | 'ended';
    currentDay: number;
    statusMessage: string;
    isActive: boolean;
    // Timeline information
    timeline: {
      startDate: Date;
      endDate: Date;
      totalDays: number;
      daysCompleted: number;
      daysRemaining: number;
      progressPercentage: number;
    } | null;
    // Transitional status information
    transitionalStatus: TransitionalProgramStatus | null;
    isTransitional: boolean;
    // Loading states
    loading: boolean;
    error: string | null;
  }) => React.ReactNode;
}

export const IndividualProgramStatusWrapper: React.FC<IndividualProgramStatusWrapperProps> = ({
  program,
  participant,
  userId,
  children,
}) => {
  // Get individual program status
  const { statusInfo, loading, error } = useIndividualProgramStatus(
    program.id,
    userId,
    participant || undefined
  );

  // Get program day information
  const dayInfo = useProgramDay(participant);

  // Check if program is active
  const isActive = useIsProgramActive(participant);

  // Get timeline information
  const timeline = useProgramTimeline(participant);

  // Calculate transitional status
  const transitionalStatus = useMemo((): TransitionalProgramStatus | null => {
    if (!participant || !program.status) {
      return null;
    }

    return calculateTransitionalProgramStatus(
      participant,
      program.status as 'upcoming' | 'ongoing' | 'ended' | 'disqualified'
    );
  }, [participant, program.status]);

  // Create enhanced program object with individual status
  const enhancedProgram = useMemo((): Program => {
    if (!participant || !statusInfo) {
      return program; // Fallback to original program
    }

    return {
      ...program,
      // Override status with individual status
      status: statusInfo.status as Program['status'],
      // Add individual timeline dates if available
      startDate: participant.personalStartDate ?
        new Date(participant.personalStartDate).toISOString().split('T')[0] :
        program.startDate,
      endDate: participant.personalEndDate ?
        new Date(participant.personalEndDate).toISOString().split('T')[0] :
        program.endDate,
    };
  }, [program, participant, statusInfo]);

  // Extract individual status information
  const individualStatus = statusInfo?.status || 'upcoming';
  const currentDay = dayInfo?.currentDay || 1;
  const statusMessage = transitionalStatus?.transitionalMessage ||
                       statusInfo?.message ||
                       dayInfo?.message || '';
  const isTransitional = transitionalStatus?.isTransitional || false;

  return (
    <>
      {children({
        enhancedProgram,
        individualStatus,
        currentDay,
        statusMessage,
        isActive,
        timeline,
        transitionalStatus,
        isTransitional,
        loading,
        error,
      })}
    </>
  );
};

/**
 * Higher-order component version for easier integration
 */
export const withIndividualProgramStatus = <P extends object>(
  Component: React.ComponentType<P & {
    program: Program;
    individualStatus?: 'upcoming' | 'ongoing' | 'ended';
    currentDay?: number;
    statusMessage?: string;
    isActive?: boolean;
  }>
) => {
  return React.forwardRef<any, P & {
    program: Program;
    participant: Participant | null;
    userId: string;
  }>((props, ref) => {
    const { program, participant, userId, ...otherProps } = props;

    return (
      <IndividualProgramStatusWrapper
        program={program}
        participant={participant}
        userId={userId}
      >
        {({ enhancedProgram, individualStatus, currentDay, statusMessage, isActive }) => (
          <Component
            {...(otherProps as P)}
            ref={ref}
            program={enhancedProgram}
            individualStatus={individualStatus}
            currentDay={currentDay}
            statusMessage={statusMessage}
            isActive={isActive}
          />
        )}
      </IndividualProgramStatusWrapper>
    );
  });
};

/**
 * Hook to get individual program status for a specific program and user
 */
export const useIndividualProgramStatusForProgram = (
  program: Program,
  participant: Participant | null,
  userId: string
) => {
  const { statusInfo, loading, error } = useIndividualProgramStatus(
    program.id,
    userId,
    participant || undefined
  );

  const dayInfo = useProgramDay(participant);
  const isActive = useIsProgramActive(participant);
  const timeline = useProgramTimeline(participant);

  // Calculate transitional status
  const transitionalStatus = useMemo((): TransitionalProgramStatus | null => {
    if (!participant || !program.status) {
      return null;
    }

    return calculateTransitionalProgramStatus(
      participant,
      program.status as 'upcoming' | 'ongoing' | 'ended' | 'disqualified'
    );
  }, [participant, program.status]);

  const enhancedProgram = useMemo((): Program => {
    if (!participant || !statusInfo) {
      return program;
    }

    return {
      ...program,
      status: statusInfo.status as Program['status'],
      startDate: participant.personalStartDate ?
        new Date(participant.personalStartDate).toISOString().split('T')[0] :
        program.startDate,
      endDate: participant.personalEndDate ?
        new Date(participant.personalEndDate).toISOString().split('T')[0] :
        program.endDate,
    };
  }, [program, participant, statusInfo]);

  return {
    enhancedProgram,
    individualStatus: statusInfo?.status || 'upcoming',
    currentDay: dayInfo?.currentDay || 1,
    statusMessage: transitionalStatus?.transitionalMessage ||
                   statusInfo?.message ||
                   dayInfo?.message || '',
    isActive,
    timeline,
    transitionalStatus,
    isTransitional: transitionalStatus?.isTransitional || false,
    loading,
    error,
  };
};
