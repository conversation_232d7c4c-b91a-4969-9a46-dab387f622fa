/**
 * Commit Scheduling Status Component
 * Shows scheduling information, verification status, and next deadlines
 */

import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { commitSchedulingService } from "../../lib/services/commitSchedulingService";

interface CommitSchedulingStatusProps {
  commitId: string;
  userId: string;
  commit: any; // Full commit object
  style?: any;
}

export const CommitSchedulingStatus: React.FC<CommitSchedulingStatusProps> = ({
  commitId,
  userId,
  commit,
  style
}) => {
  const { colors, designSystem } = useTheme();
  const [schedulingStatus, setSchedulingStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    loadSchedulingStatus();
  }, [commitId, userId]);

  const loadSchedulingStatus = async () => {
    try {
      setLoading(true);
      const status = await commitSchedulingService.getCommitSchedulingStatus(commitId, userId);
      setSchedulingStatus(status);
    } catch (error) {
      console.error('Failed to load scheduling status:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNextDeadline = () => {
    if (!commit?.schedule?.deadline) return 'Midnight';
    
    const deadline = commit.schedule.deadline;
    switch (deadline.type) {
      case 'before':
        return `Before ${deadline.time || '12:00 AM'}`;
      case 'after':
        return `After ${deadline.time || '12:00 AM'}`;
      case 'between':
        return `Between ${deadline.time || '9:00 AM'} - ${deadline.endTime || '6:00 PM'}`;
      case 'midnight':
      default:
        return 'Midnight';
    }
  };

  const getVerificationStatusIcon = () => {
    if (!commit?.verification?.lastAttempt) {
      return <MaterialIcons name="schedule" size={16} color={colors.textMuted} />;
    }

    const lastAttempt = commit.verification.lastAttempt;
    if (lastAttempt.success && lastAttempt.verified) {
      return <MaterialIcons name="check-circle" size={16} color={colors.success} />;
    } else if (lastAttempt.success && !lastAttempt.verified) {
      return <MaterialIcons name="info" size={16} color={colors.warning} />;
    } else {
      return <MaterialIcons name="error" size={16} color={colors.error} />;
    }
  };

  const getVerificationStatusText = () => {
    if (!commit?.verification?.lastAttempt) {
      return 'No verification attempts yet';
    }

    const lastAttempt = commit.verification.lastAttempt;
    const timeAgo = new Date(lastAttempt.timestamp).toLocaleTimeString();
    
    if (lastAttempt.success && lastAttempt.verified) {
      return `Verified at ${timeAgo}`;
    } else if (lastAttempt.success && !lastAttempt.verified) {
      return `Requirements not met at ${timeAgo}`;
    } else {
      return `Failed at ${timeAgo}`;
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, style]}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textMuted }]}>
          Loading scheduling status...
        </Text>
      </View>
    );
  }

  if (!schedulingStatus?.isScheduled && !commit?.scheduling?.enabled) {
    return null; // Don't show if scheduling is not enabled
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.surface, borderColor: colors.border }, style]}>
      {/* Header */}
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setExpanded(!expanded)}
        activeOpacity={0.7}
      >
        <View style={styles.headerLeft}>
          <MaterialCommunityIcons 
            name="clock-outline" 
            size={20} 
            color={colors.primary} 
          />
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Scheduling Active
          </Text>
        </View>
        <MaterialIcons 
          name={expanded ? "expand-less" : "expand-more"} 
          size={24} 
          color={colors.textMuted} 
        />
      </TouchableOpacity>

      {/* Quick Status */}
      <View style={styles.quickStatus}>
        <View style={styles.statusItem}>
          <Text style={[styles.statusLabel, { color: colors.textMuted }]}>Next Deadline</Text>
          <Text style={[styles.statusValue, { color: colors.text }]}>
            {formatNextDeadline()}
          </Text>
        </View>
        
        {commit?.verification?.autoVerificationEnabled && (
          <View style={styles.statusItem}>
            <Text style={[styles.statusLabel, { color: colors.textMuted }]}>Auto-Verification</Text>
            <View style={styles.verificationStatus}>
              {getVerificationStatusIcon()}
              <Text style={[styles.statusValue, { color: colors.text, marginLeft: 4 }]}>
                {commit.verification.type.charAt(0).toUpperCase() + commit.verification.type.slice(1)}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Expanded Details */}
      {expanded && (
        <View style={[styles.expandedContent, { borderTopColor: colors.border }]}>
          {/* Scheduling Details */}
          <View style={styles.detailSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Scheduling Details</Text>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Frequency:</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {commit.schedule?.frequency?.charAt(0).toUpperCase() + commit.schedule?.frequency?.slice(1) || 'Daily'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Timezone:</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {commit.timezone || 'UTC'}
              </Text>
            </View>

            {schedulingStatus?.nextVerificationTime && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Next Check:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {new Date(schedulingStatus.nextVerificationTime).toLocaleString()}
                </Text>
              </View>
            )}
          </View>

          {/* Verification Details */}
          {commit?.verification?.autoVerificationEnabled && (
            <View style={styles.detailSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Auto-Verification</Text>
              
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Type:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {commit.verification.type.charAt(0).toUpperCase() + commit.verification.type.slice(1)}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Status:</Text>
                <View style={styles.verificationStatus}>
                  {getVerificationStatusIcon()}
                  <Text style={[styles.detailValue, { color: colors.text, marginLeft: 4 }]}>
                    {getVerificationStatusText()}
                  </Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Total Attempts:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {commit.verification.totalAttempts || 0}
                </Text>
              </View>
            </View>
          )}

          {/* Scheduler Jobs */}
          {schedulingStatus?.schedulerJobIds?.length > 0 && (
            <View style={styles.detailSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Active Jobs</Text>
              <Text style={[styles.detailValue, { color: colors.textMuted }]}>
                {schedulingStatus.schedulerJobIds.length} scheduler jobs running
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = {
  container: {
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 8,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  quickStatus: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusItem: {
    flex: 1,
  },
  statusLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  verificationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandedContent: {
    borderTopWidth: 1,
    padding: 16,
  },
  detailSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailLabel: {
    fontSize: 13,
    flex: 1,
  },
  detailValue: {
    fontSize: 13,
    flex: 2,
    textAlign: 'right',
  },
  loadingText: {
    fontSize: 14,
    marginLeft: 8,
  },
};
