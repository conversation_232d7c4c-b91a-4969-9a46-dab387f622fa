/**
 * Individual Program Day Renderer
 * 
 * This component renders program days using individual user program status
 * instead of centralized program status.
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { Program, DayTile, ParticipantList } from "../../shared/types/CommonInterface";
import { Participant } from "../../lib/services/database/types";
import { ContentArea } from "../pages/Progress/ContentArea";
import ParticipantsStatusDashboard from "../pages/Progress/ParticipantsStatusDashboard";
import { InfoButton } from "../pages/Progress/InfoButton";
import CategorySetup from "../pages/Progress/CategorySetup";
import ProgramEnd from "../pages/Progress/programEnd/ProgramEnd";
import { useRouter } from 'expo-router';
import { DisqualifiedMessage } from "../../shared/components/program/DisqualifiedMessage";
import { IndividualProgramStatusWrapper } from './IndividualProgramStatusWrapper';
import { TransitionalProgramStatusComponent, TransitionalProgramStatusBanner } from './TransitionalProgramStatus';

interface IndividualProgramDayRendererProps {
  program: Program;
  participant: Participant | null;
  programDay: number;
  userId: string;
  userName?: string;
  participants: ParticipantList[];
  allParticipants?: ParticipantList[];
  userDays: DayTile[];
  loading?: boolean;
  submissionTriggered?: boolean;
  onSubmissionTrigger?: () => void;
  showProgramInfo?: boolean;
  needsAttention?: boolean;
  onProgramAttention?: (programId: string) => void;
  attentionReason?: string;
  setupStatus?: boolean | null;
  onSetupStatusUpdate?: (status: boolean) => void;
  onProgramUpdate?: (program: Program) => void;
  onSignedUpProgramsUpdate?: (programs: Program[]) => void;
  disqualificationReason?: string;
  onArchive?: () => void;
  archiveLoading?: boolean;
  setupLoading?: boolean;
  endedProgramParticipants?: number;
  isCalendarView?: boolean;
}

export const IndividualProgramDayRenderer: React.FC<IndividualProgramDayRendererProps> = (props) => {
  const {
    program,
    participant,
    programDay,
    userId,
    userName,
    participants,
    allParticipants,
    userDays,
    loading = false,
    submissionTriggered = false,
    onSubmissionTrigger,
    showProgramInfo = true,
    needsAttention = false,
    onProgramAttention,
    attentionReason,
    setupStatus,
    onSetupStatusUpdate,
    onProgramUpdate,
    onSignedUpProgramsUpdate,
    disqualificationReason,
    onArchive,
    archiveLoading = false,
    setupLoading = false,
    endedProgramParticipants,
    isCalendarView = false,
  } = props;

  const { colors } = useTheme();
  const styles = createStyles(colors);
  const router = useRouter();

  return (
    <IndividualProgramStatusWrapper
      program={program}
      participant={participant}
      userId={userId}
    >
      {({
        enhancedProgram,
        individualStatus,
        currentDay,
        statusMessage,
        isActive,
        timeline,
        transitionalStatus,
        isTransitional,
        loading: statusLoading,
        error: statusError
      }) => {
        // Show loading state if status is still loading
        if (statusLoading && !participant) {
          return (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.loadingText}>Loading program status...</Text>
            </View>
          );
        }

        // Show error state if there's an error
        if (statusError) {
          return (
            <View style={styles.errorContainer}>
              <MaterialIcons name="error-outline" size={48} color={colors.error} />
              <Text style={styles.errorTitle}>Status Error</Text>
              <Text style={styles.errorMessage}>{statusError}</Text>
            </View>
          );
        }

        // Calculate if this is a future day in calendar view
        const isFutureDay = isCalendarView && programDay > currentDay;

        // Calculate money stats
        const calculateMoneyStats = () => {
          const totalParticipants = allParticipants?.length || participants.length;
          const betAmount = enhancedProgram.betAmount || 0;
          const totalPool = betAmount * totalParticipants;

          // Count active and disqualified participants
          const activeCount = participants.filter(p => !p.disqualified).length;
          const disqualifiedCount = participants.filter(p => p.disqualified).length;

          return {
            activeCount,
            activeMoney: activeCount * betAmount,
            disqualifiedCount,
            disqualifiedMoney: disqualifiedCount * betAmount,
            userMoneyBack: 0, // Will be calculated when program ends
            userShare: 0, // Will be calculated when program ends
          };
        };

        // Render setup required message
        const renderSetupRequired = () => (
          <View style={styles.setupContainer}>
            <View style={styles.setupContent}>
              <MaterialIcons name="settings" size={48} color={colors.primary} />
              <Text style={styles.setupTitle}>Setup Required</Text>
              <Text style={styles.setupMessage}>
                Complete your challenge setup to get started.
              </Text>
              
              {setupLoading ? (
                <View style={styles.setupLoadingContainer}>
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text style={styles.setupLoadingText}>Setting up...</Text>
                </View>
              ) : (
                <CategorySetup
                  category={enhancedProgram.category}
                  selectedProgram={enhancedProgram}
                  updateSetupStatus={(updatedSetupStatus) => {
                    onSetupStatusUpdate?.(updatedSetupStatus);
                  }}
                  updateSelectedProgram={(updatedSelectedProgram) => {
                    onProgramUpdate?.(updatedSelectedProgram);
                  }}
                  updateSignedUpProgram={(updateSignedUpProgram) => {
                    onSignedUpProgramsUpdate?.(updateSignedUpProgram);
                  }}
                />
              )}
            </View>
          </View>
        );

        // Handle different individual program statuses
        if (individualStatus === 'upcoming' && setupStatus === false) {
          return renderSetupRequired();
        }

        if (individualStatus === 'upcoming' && setupStatus === true) {
          // Show "Get Ready!" message for upcoming programs with completed setup
          return (
            <View style={styles.messageContainer}>
              <Text style={styles.messageTitle}>Get Ready!</Text>
              <Text style={styles.messageSubtitle}>
                Your challenge kicks off soon. Focus up and prepare to give it your best!
              </Text>
              <Text style={styles.statusMessage}>{statusMessage}</Text>
              {timeline && (
                <Text style={styles.timelineInfo}>
                  Starts: {timeline.startDate.toLocaleDateString()}
                </Text>
              )}
              <InfoButton selectedProgramId={enhancedProgram.id} />
            </View>
          );
        }

        if (individualStatus === 'ended') {
          // Check if we need to show waiting message instead of results
          if (transitionalStatus?.showWaitingMessage) {
            return (
              <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                <TransitionalProgramStatusComponent
                  transitionalStatus={transitionalStatus}
                  loading={statusLoading}
                />
              </ScrollView>
            );
          }

          // Show full ProgramEnd component for ended programs
          return (
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              <ProgramEnd
                programId={enhancedProgram.id}
                duration={typeof enhancedProgram.duration === 'string'
                  ? parseInt(enhancedProgram.duration, 10)
                  : enhancedProgram.duration || 0}
                currentUserId={userId}
                betAmount={Number(enhancedProgram.betAmount)}
                totalPlayers={endedProgramParticipants || 0}
              />
            </ScrollView>
          );
        }

        // Handle disqualified participants
        if (participant?.disqualified) {
          return (
            <View style={styles.disqualifiedContainer}>
              <DisqualifiedMessage
                disqualificationReason={disqualificationReason || participant.disqualifyReason || 'You have been disqualified from this challenge.'}
              />
            </View>
          );
        }

        // Main ongoing program content
        return (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Status Message */}
            <View style={styles.statusContainer}>
              <Text style={styles.statusMessage}>{statusMessage}</Text>
              {timeline && (
                <Text style={styles.progressInfo}>
                  Progress: {Math.round(timeline.progressPercentage)}% complete
                </Text>
              )}
            </View>

            {/* Program Info */}
            {showProgramInfo && (
              <View style={styles.section}>
                <InfoButton selectedProgramId={enhancedProgram.id} />
              </View>
            )}

            {/* Attention Banner */}
            {needsAttention && onProgramAttention && (
              <TouchableOpacity
                style={styles.attentionBanner}
                onPress={() => onProgramAttention(enhancedProgram.id)}
              >
                <MaterialIcons name="warning" size={20} color={colors.warning} />
                <Text style={styles.attentionText}>
                  {attentionReason || 'This program needs your attention'}
                </Text>
                <MaterialIcons name="chevron-right" size={20} color={colors.warning} />
              </TouchableOpacity>
            )}

            {/* Transitional Status Banner - only for ongoing programs that need waiting message */}
            {transitionalStatus?.showWaitingMessage && individualStatus === 'ongoing' && (
              <View style={styles.section}>
                <TransitionalProgramStatusBanner
                  transitionalStatus={transitionalStatus}
                  loading={statusLoading}
                />
              </View>
            )}

            {/* Content Area for ongoing programs */}
            {individualStatus === 'ongoing' && (
              <View style={styles.section}>
                {loading && userDays.length === 0 && participants.length === 0 ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={styles.loadingText}>Loading submission status...</Text>
                  </View>
                ) : (
                  <ContentArea
                    userId={userId}
                    selectedProgram={enhancedProgram}
                    selectedDay={programDay}
                    currentDay={currentDay}
                    days={userDays}
                    participantsList={participants}
                    submissionTriggered={submissionTriggered}
                    loadingStatuses={[
                      loading && (userDays.length === 0 || participants.length === 0)
                    ]}
                    submissionTrigger={onSubmissionTrigger || (() => {})}
                  />
                )}
              </View>
            )}

            {/* Participants Dashboard - Hide for future days in calendar view */}
            {individualStatus === 'ongoing' && !isFutureDay && (
              participants.length > 0 ? (
                <View style={styles.section}>
                  <ParticipantsStatusDashboard
                    key={`participants-${enhancedProgram.id}-${programDay}`}
                    participants={participants}
                    currentUserId={userId}
                    today={programDay === currentDay}
                    moneyStats={calculateMoneyStats()}
                  />
                </View>
              ) : loading ? (
                <View style={styles.section}>
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={styles.loadingText}>Loading challenge overview...</Text>
                  </View>
                </View>
              ) : null
            )}
          </ScrollView>
        );
      }}
    </IndividualProgramStatusWrapper>
  );
};

// Styles (reusing from original ProgramDayRenderer)
const createStyles = (colors: any) => StyleSheet.create({
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 16,
  },
  statusContainer: {
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusMessage: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
  },
  progressInfo: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  timelineInfo: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  messageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  messageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  messageSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  setupContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  setupContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  setupTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  setupMessage: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  setupLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  setupLoadingText: {
    marginLeft: 8,
    color: colors.textSecondary,
  },
  disqualifiedContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 8,
    color: colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.error,
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  attentionBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warningBackground,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  attentionText: {
    flex: 1,
    marginLeft: 8,
    color: colors.warning,
    fontWeight: '500',
  },
});
