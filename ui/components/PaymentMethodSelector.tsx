// Payment Method Selector Component
// Shows appropriate payment methods based on purchase type and platform

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';
import { MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { useTheme } from '../../shared/contexts/ThemeContext';
import {
  PaymentType,
  PaymentProvider,
  getAvailablePaymentMethods,
  getPrimaryPaymentProvider,
  isPaymentMethodSupported,
} from '../../lib/config/payments';

interface PaymentMethodOption {
  provider: PaymentProvider;
  name: string;
  icon: string;
  iconFamily: 'MaterialIcons' | 'FontAwesome';
  description: string;
  recommended?: boolean;
}

interface PaymentMethodSelectorProps {
  paymentType: PaymentType;
  selectedProvider?: PaymentProvider;
  onProviderSelect: (provider: PaymentProvider) => void;
  disabled?: boolean;
  showRecommended?: boolean;
  darkTheme?: boolean; // Add dark theme support
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  paymentType,
  selectedProvider,
  onProviderSelect,
  disabled = false,
  showRecommended = true,
  darkTheme = false,
}) => {
  const { colors } = useTheme();

  // Use dark theme colors when specified
  const themeColors = darkTheme ? {
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.7)',
    primary: '#FFD700',
    surface: 'rgba(255, 255, 255, 0.05)',
    border: 'rgba(255, 215, 0, 0.3)',
  } : colors;

  // Get available payment methods for this payment type and platform
  const currentPlatform = Platform.OS as 'ios' | 'android' | 'web';
  const availableProviders = getAvailablePaymentMethods(paymentType, currentPlatform);
  const recommendedProvider = getPrimaryPaymentProvider(paymentType, currentPlatform);

  // Define payment method options
  const getPaymentMethodOptions = (): PaymentMethodOption[] => {
    const options: PaymentMethodOption[] = [];

    if (availableProviders.includes(PaymentProvider.STRIPE)) {
      options.push({
        provider: PaymentProvider.STRIPE,
        name: 'Credit/Debit Card',
        icon: 'credit-card',
        iconFamily: 'MaterialIcons',
        description: 'Visa, Mastercard, American Express',
        recommended: recommendedProvider === PaymentProvider.STRIPE,
      });
    }

    if (availableProviders.includes(PaymentProvider.APPLE_APP_STORE)) {
      options.push({
        provider: PaymentProvider.APPLE_APP_STORE,
        name: 'Apple App Store',
        icon: 'apple',
        iconFamily: 'FontAwesome',
        description: 'Use your Apple ID payment method',
        recommended: recommendedProvider === PaymentProvider.APPLE_APP_STORE,
      });
    }

    if (availableProviders.includes(PaymentProvider.GOOGLE_PLAY)) {
      options.push({
        provider: PaymentProvider.GOOGLE_PLAY,
        name: 'Google Play Store',
        icon: 'google',
        iconFamily: 'FontAwesome',
        description: 'Use your Google Play payment method',
        recommended: recommendedProvider === PaymentProvider.GOOGLE_PLAY,
      });
    }

    return options;
  };

  const paymentOptions = getPaymentMethodOptions();

  // If only one option is available, auto-select it
  React.useEffect(() => {
    if (paymentOptions.length === 1 && !selectedProvider) {
      onProviderSelect(paymentOptions[0].provider);
    }
  }, [paymentOptions.length, selectedProvider, onProviderSelect]);

  const renderPaymentOption = (option: PaymentMethodOption) => {
    const isSelected = selectedProvider === option.provider;
    const isRecommended = option.recommended && showRecommended;

    return (
      <TouchableOpacity
        key={option.provider}
        style={[
          styles.paymentOption,
          {
            borderColor: isSelected ? themeColors.primary : themeColors.border,
            backgroundColor: isSelected ? `${themeColors.primary}20` : themeColors.surface,
          },
          disabled && styles.disabled,
        ]}
        onPress={() => !disabled && onProviderSelect(option.provider)}
        disabled={disabled}
      >
        <View style={styles.paymentOptionContent}>
          <View style={styles.paymentOptionLeft}>
            <View style={[styles.iconContainer, { backgroundColor: themeColors.surface }]}>
              {option.iconFamily === 'MaterialIcons' ? (
                <MaterialIcons
                  name={option.icon as any}
                  size={24}
                  color={isSelected ? themeColors.primary : themeColors.text}
                />
              ) : (
                <FontAwesome
                  name={option.icon as any}
                  size={24}
                  color={isSelected ? themeColors.primary : themeColors.text}
                />
              )}
            </View>
            <View style={styles.paymentOptionText}>
              <View style={styles.paymentOptionHeader}>
                <Text style={[styles.paymentOptionName, { color: themeColors.text }]}>
                  {option.name}
                </Text>
                {isRecommended && (
                  <View style={[styles.recommendedBadge, { backgroundColor: themeColors.primary }]}>
                    <Text style={[styles.recommendedText, { color: darkTheme ? '#000' : themeColors.surface }]}>
                      Recommended
                    </Text>
                  </View>
                )}
              </View>
              <Text style={[styles.paymentOptionDescription, { color: themeColors.textSecondary }]}>
                {option.description}
              </Text>
            </View>
          </View>
          <View style={styles.radioButton}>
            <View
              style={[
                styles.radioOuter,
                {
                  borderColor: isSelected ? themeColors.primary : themeColors.border,
                  backgroundColor: isSelected ? themeColors.primary : 'transparent',
                },
              ]}
            >
              {isSelected && (
                <View style={[styles.radioInner, { backgroundColor: darkTheme ? '#000' : themeColors.surface }]} />
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const getPaymentTypeLabel = () => {
    switch (paymentType) {
      case PaymentType.COMMIT:
        return 'Commitment Payment';
      case PaymentType.POOL:
        return 'Pool Entry Payment';
      case PaymentType.PREMIUM_SUBSCRIPTION:
        return 'Premium Subscription';
      case PaymentType.GRACE_DAY:
        return 'Grace Day Purchase';
      default:
        return 'Payment';
    }
  };

  if (paymentOptions.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={[styles.noOptionsText, { color: darkTheme ? '#FF6B6B' : colors.error }]}>
          No payment methods available for this purchase type.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Payment Method for {getPaymentTypeLabel()}
      </Text>
      <View style={styles.paymentOptions}>
        {paymentOptions.map(renderPaymentOption)}
      </View>
      {paymentType === PaymentType.PREMIUM_SUBSCRIPTION && (
        <Text style={[styles.subscriptionNote, { color: themeColors.textSecondary }]}>
          Subscriptions can be managed through your {Platform.OS === 'ios' ? 'Apple ID' : 'Google Play'} account settings.
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  paymentOptions: {
    gap: 12,
  },
  paymentOption: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    marginBottom: 4,
  },
  paymentOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  paymentOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  paymentOptionText: {
    flex: 1,
  },
  paymentOptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  paymentOptionName: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  recommendedBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  recommendedText: {
    fontSize: 12,
    fontWeight: '600',
  },
  paymentOptionDescription: {
    fontSize: 14,
  },
  radioButton: {
    marginLeft: 12,
  },
  radioOuter: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  disabled: {
    opacity: 0.5,
  },
  noOptionsText: {
    textAlign: 'center',
    fontSize: 16,
    fontStyle: 'italic',
    marginVertical: 20,
  },
  subscriptionNote: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
});
