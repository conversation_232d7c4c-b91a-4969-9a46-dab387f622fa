import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
  Modal,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { Program } from "../../shared/types/CommonInterface";
import CategorySetup from "../pages/Progress/CategorySetup";
import CommitSetup from "../pages/Progress/CommitSetup";

interface SetupItem {
  id: string;
  title: string;
  startDate: string;
  type: 'program' | 'commit';
}

interface SetupBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  programs: Program[];
  commits: any[];
  onProgramSetup: (programId: string) => void;
  onCommitSetup?: (commitId: string) => void;
  onSetupComplete?: () => void; // Callback when setup is completed
}

export const SetupBottomSheet: React.FC<SetupBottomSheetProps> = ({
  visible,
  onClose,
  programs,
  commits,
  onProgramSetup,
  onCommitSetup,
  onSetupComplete,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // State for setup flow
  const [currentSetupItem, setCurrentSetupItem] = useState<SetupItem | null>(null);
  const [isSettingUp, setIsSettingUp] = useState(false);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const today = new Date();
    const diffTime = date.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Tomorrow';
    if (diffDays <= 7) return `In ${diffDays} days`;
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
    });
  };

  const handleItemPress = (item: SetupItem) => {
    setCurrentSetupItem(item);
    setIsSettingUp(true);
  };

  const handleSetupComplete = (item: SetupItem) => {
    setCurrentSetupItem(null);
    setIsSettingUp(false);
    onSetupComplete?.();

    // Show success message
    Alert.alert(
      "Setup Complete!",
      `${item.title} has been set up successfully.`,
      [{ text: "OK" }]
    );
  };



  const handleSetupCancel = () => {
    setCurrentSetupItem(null);
    setIsSettingUp(false);
  };

  const setupItems: SetupItem[] = [
    ...programs.map(program => ({
      id: program.id,
      title: program.name,
      startDate: program.startDate,
      type: 'program' as const,
    })),
    ...commits.map(commit => ({
      id: commit.id,
      title: commit.commitment || 'Personal Commitment',
      startDate: commit.schedule?.startDate || '',
      type: 'commit' as const,
    })),
  ].sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());



  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        />

        <View style={styles.bottomSheet}>
          {/* Handle */}
          <View style={styles.handle} />

          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              {isSettingUp && currentSetupItem ? (
                <TouchableOpacity onPress={handleSetupCancel} style={styles.backButton}>
                  <MaterialIcons name="arrow-back" size={24} color={colors.primary} />
                </TouchableOpacity>
              ) : (
                <MaterialIcons name="settings" size={24} color={colors.primary} />
              )}
              <Text style={styles.title}>
                {isSettingUp && currentSetupItem ? `Setup ${currentSetupItem.title}` : 'Setup Required'}
              </Text>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color={colors.textMuted} />
            </TouchableOpacity>
          </View>

          {isSettingUp && currentSetupItem ? (
            // Show setup component
            <View style={styles.setupContainer}>
              {currentSetupItem.type === 'program' ? (
                <CategorySetup
                  category={programs.find(p => p.id === currentSetupItem.id)?.category}
                  selectedProgram={programs.find(p => p.id === currentSetupItem.id)!}
                  updateSetupStatus={(updatedSetupStatus) => {
                    if (updatedSetupStatus) {
                      handleSetupComplete(currentSetupItem);
                    }
                  }}
                  updateSelectedProgram={(updatedProgram) => {
                    handleSetupComplete(currentSetupItem);
                  }}
                  updateSignedUpProgram={() => {
                    // This callback is handled at the parent level
                  }}
                />
              ) : (
                <CommitSetup
                  commit={commits.find(c => c.id === currentSetupItem.id)!}
                  onSetupComplete={(commit) => handleSetupComplete(currentSetupItem)}
                />
              )}
            </View>
          ) : (
            // Show items list
            <>
              <Text style={styles.subtitle}>
                {setupItems.length} item{setupItems.length !== 1 ? 's' : ''} starting soon need{setupItems.length === 1 ? 's' : ''} to be set up
              </Text>

              <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {setupItems.length > 0 ? (
                  setupItems.map((item, index) => (
                    <TouchableOpacity
                      key={item.id}
                      style={[
                        styles.item,
                        index === setupItems.length - 1 && styles.lastItem
                      ]}
                      onPress={() => handleItemPress(item)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.itemLeft}>
                        <View style={[
                          styles.typeIcon,
                          { backgroundColor: item.type === 'program' ? colors.primary + '20' : colors.secondary + '20' }
                        ]}>
                          <MaterialIcons
                            name={item.type === 'program' ? 'groups' : 'person'}
                            size={20}
                            color={item.type === 'program' ? colors.primary : colors.secondary}
                          />
                        </View>
                        <View style={styles.itemContent}>
                          <Text style={styles.itemTitle}>{item.title}</Text>
                          <Text style={styles.itemDate}>
                            Starts {formatDate(item.startDate)}
                          </Text>
                        </View>
                      </View>
                      <MaterialIcons name="arrow-forward-ios" size={16} color={colors.textMuted} />
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.emptyState}>
                    <Text style={styles.emptyStateText}>No items need setup right now</Text>
                  </View>
                )}
              </ScrollView>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: Dimensions.get('window').height * 0.5,
    maxHeight: Dimensions.get('window').height * 0.8,
    paddingBottom: 34, // Safe area padding
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: colors.textMuted + '40',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    marginLeft: 8,
  },
  closeButton: {
    padding: 4,
  },
  backButton: {
    padding: 4,
    marginRight: 8,
  },
  setupContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    paddingHorizontal: 20,
    marginBottom: 20,
    lineHeight: 22,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 0,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  lastItem: {
    borderBottomWidth: 0,
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    marginBottom: 2,
  },
  itemDate: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
  },
});
