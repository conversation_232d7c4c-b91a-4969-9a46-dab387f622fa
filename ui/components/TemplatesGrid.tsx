import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../shared/contexts/ThemeContext";
import { EvidenceType, ReportingFrequency } from "../../shared/types/customStake";

export interface CommitTemplate {
  id: string;
  title: string;
  description: string;
  icon: string;
  evidenceType: EvidenceType;
  reportingFrequency: ReportingFrequency;
  timesPerWeek?: number;
  timesPerMonth?: number;
  weekLength?: number;
  amountPerReport: number;
  category: string;
}

// Predefined templates for common habits
export const COMMIT_TEMPLATES: CommitTemplate[] = [
  {
    id: 'gym-workout',
    title: 'Go to the gym',
    description: 'Build strength and stay fit with regular gym sessions',
    icon: 'fitness-center',
    evidenceType: 'gps-checkin',
    reportingFrequency: 'weekly',
    timesPerWeek: 3,
    weekLength: 4,
    amountPerReport: 10,
    category: 'Fitness'
  },
  {
    id: 'daily-reading',
    title: 'Read for 30 minutes',
    description: 'Expand your knowledge with daily reading habits',
    icon: 'book',
    evidenceType: 'photo',
    reportingFrequency: 'daily',
    weekLength: 4,
    amountPerReport: 5,
    category: 'Learning'
  },
  {
    id: 'meditation',
    title: 'Meditate every morning',
    description: 'Start your day with mindfulness and inner peace',
    icon: 'self-improvement',
    evidenceType: 'photo',
    reportingFrequency: 'daily',
    weekLength: 4,
    amountPerReport: 3,
    category: 'Wellness'
  },
  {
    id: 'coding-practice',
    title: 'Code for 1 hour daily',
    description: 'Improve programming skills with consistent practice',
    icon: 'code',
    evidenceType: 'github',
    reportingFrequency: 'daily',
    weekLength: 4,
    amountPerReport: 8,
    category: 'Learning'
  },
  {
    id: 'healthy-cooking',
    title: 'Cook healthy meals',
    description: 'Prepare nutritious home-cooked meals',
    icon: 'restaurant',
    evidenceType: 'photo',
    reportingFrequency: 'weekly',
    timesPerWeek: 5,
    weekLength: 4,
    amountPerReport: 7,
    category: 'Health'
  },
  {
    id: 'morning-run',
    title: 'Go for a morning run',
    description: 'Start your day with energizing cardio exercise',
    icon: 'directions-run',
    evidenceType: 'photo',
    reportingFrequency: 'weekly',
    timesPerWeek: 4,
    weekLength: 4,
    amountPerReport: 6,
    category: 'Fitness'
  },
  {
    id: 'journaling',
    title: 'Write in journal daily',
    description: 'Reflect on your day and track personal growth',
    icon: 'edit',
    evidenceType: 'photo',
    reportingFrequency: 'daily',
    weekLength: 4,
    amountPerReport: 4,
    category: 'Wellness'
  },
  {
    id: 'language-learning',
    title: 'Practice Spanish for 20 minutes',
    description: 'Build fluency with consistent language practice',
    icon: 'translate',
    evidenceType: 'photo',
    reportingFrequency: 'daily',
    weekLength: 4,
    amountPerReport: 5,
    category: 'Learning'
  }
];

// Internal TemplateCard component
interface TemplateCardProps {
  template: CommitTemplate;
  onPress: (template: CommitTemplate) => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({ template, onPress }) => {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors, designSystem, isDark);

  const getEvidenceIcon = (evidenceType: EvidenceType) => {
    switch (evidenceType) {
      case 'photo':
        return 'camera-alt';
      case 'gps-checkin':
        return 'location-on';
      // 'strava' removed as it's not in EvidenceType
      case 'github':
        return 'code';
      default:
        return 'assignment';
    }
  };

  const getEvidenceLabel = (evidenceType: EvidenceType) => {
    switch (evidenceType) {
      case 'gps-checkin':
        return 'GPS Check-in';
      case 'github':
        return 'GitHub';
      // 'strava' removed as it's not in EvidenceType
      default:
        return evidenceType.charAt(0).toUpperCase() + evidenceType.slice(1);
    }
  };

  const getFrequencyText = () => {
    if (template.reportingFrequency === 'daily') {
      return 'Every day';
    } else if (template.reportingFrequency === 'weekly') {
      return `${template.timesPerWeek} times a week`;
    } else {
      return `${template.timesPerMonth} times a month`;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.templateCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
      onPress={() => onPress(template)}
      activeOpacity={0.8}
    >
      <Text style={[styles.templateTitle, { color: colors.text }]} numberOfLines={2}>
        {template.title}
      </Text>

      <View style={styles.templateDetails}>
        <View style={styles.templateDetailRow}>
          <Text style={[styles.templateFrequencyText, { color: colors.textSecondary }]}>
            {getFrequencyText()}
          </Text>
        </View>

        <View style={styles.templateDetailRow}>
          <MaterialIcons
            name={getEvidenceIcon(template.evidenceType)}
            size={14}
            color={colors.textMuted}
          />
          <Text style={[styles.templateDetailText, { color: colors.textMuted }]}>
            {getEvidenceLabel(template.evidenceType)}
          </Text>
        </View>
      </View>

      <View style={[styles.templateIconContainer, { backgroundColor: colors.primary + '20' }]}>
        <MaterialIcons name={template.icon as any} size={20} color={colors.primary} />
      </View>
    </TouchableOpacity>
  );
};

interface TemplatesGridProps {
  onTemplateSelect: (template: CommitTemplate) => void;
}

const TemplatesGrid: React.FC<TemplatesGridProps> = ({ onTemplateSelect }) => {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors, designSystem, isDark);

  return (
    <View style={styles.templatesGrid}>
      {COMMIT_TEMPLATES.map((template) => (
        <TemplateCard
          key={template.id}
          template={template}
          onPress={onTemplateSelect}
        />
      ))}
    </View>
  );
};

const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  templatesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  templateCard: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    backgroundColor: colors.card,
    borderColor: colors.border,
    ...designSystem.shadows.sm,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    minHeight: 120,
    position: 'relative',
  },
  templateIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    position: 'absolute',
    bottom: 12,
    right: 12,
  },
  templateTitle: {
    fontSize: 13,
    fontFamily: 'MontserratBold',
    marginBottom: 12,
    lineHeight: 17,
    color: colors.text,
  },
  templateDetails: {
    gap: 8,
    marginTop: 'auto',
  },
  templateFrequencyText: {
    fontSize: 13,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    marginBottom: 4,
  },
  templateDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  templateDetailText: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
  },
});

export default TemplatesGrid;
