{"expo": {"name": "Accustom", "slug": "habitosphere", "version": "1.0.0", "orientation": "portrait", "sdkVersion": "53.0.0", "icon": "assets/images/new_logo.png", "scheme": "accustom", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCalendarsUsageDescription": "This app needs access to your calendar to add commitment reminders and schedule events.", "NSCalendarsWriteOnlyAccessUsageDescription": "This app needs access to write to your calendar to add commitment reminders and schedule events."}}, "android": {"softwareKeyboardLayoutMode": "pan", "adaptiveIcon": {"foregroundImage": "./assets/images/new_logo.png", "backgroundColor": "#000000"}, "package": "com.varunveeraa.accustom", "permissions": ["android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR"]}, "web": {"bundler": "metro", "output": "server", "favicon": "./assets/images/new_logo.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "assets/images/new_logo.png", "resizeMode": "contain", "backgroundColor": "#000000", "dark": {"image": "assets/images/new_logo.png", "backgroundColor": "#000000"}, "android": {"backgroundColor": "#000000"}, "ios": {"backgroundColor": "#000000"}}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#FFCC00", "defaultChannel": "default"}], "expo-font", "expo-maps", "expo-calendar"], "splash": {"image": "assets/images/new_logo.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "b22ed3c5-6b08-43ea-8c7f-799a4202d79d"}}, "owner": "varunve<PERSON><PERSON>"}}