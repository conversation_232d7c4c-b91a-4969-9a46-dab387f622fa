#!/bin/bash

# Deploy Email Branding Updates
# This script deploys the updated email templates with ACCUSTOM branding

echo "🚀 Deploying ACCUSTOM Email Branding Updates..."

# Change to backend directory
cd "$(dirname "$0")/.."

echo "📁 Current directory: $(pwd)"

# Check if we're in the right directory
if [ ! -f "firebase.json" ]; then
    echo "❌ Error: firebase.json not found. Make sure you're in the backend directory."
    exit 1
fi

echo "🔧 Building TypeScript files..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix TypeScript errors first."
    exit 1
fi

echo "📤 Deploying Firebase Functions..."
firebase deploy --only functions:testEmailConfig,functions:sendTestEmailToAdmin,functions:sendEmailNotification

if [ $? -eq 0 ]; then
    echo "✅ Email branding deployment successful!"
    echo ""
    echo "🎨 Updated features:"
    echo "   • Gold color scheme (#D4AF37, #FFD700)"
    echo "   • Montserrat typography"
    echo "   • ACCUSTOM branding (uppercase)"
    echo "   • Logo placeholder in header"
    echo "   • Luxury styling matching your app"
    echo ""
    echo "🧪 To test the new branding:"
    echo "   node scripts/testEmailBranding.js"
    echo ""
    echo "📧 Or test via Firebase Functions:"
    echo "   firebase functions:shell"
    echo "   > sendTestEmailToAdmin({adminEmail: '<EMAIL>'})"
else
    echo "❌ Deployment failed. Check the error messages above."
    exit 1
fi
