/**
 * Timezone Compliance Validation Script
 * Comprehensive validation of all timezone fixes and scheduled job compliance
 */

import { NotificationService } from '../src/services/notificationService';
import { ProgramService } from '../src/services/programService';
import { CommitService } from '../src/services/commitService';
import { CommitNotificationService } from '../src/services/commitNotificationService';
import {
  isValidTimezone,
  getSafeTimezone,
  getCurrentDateInTimezone,
  convertDateToTimezone,
  extractParticipantTimezones
} from '../src/utils/timezoneUtils';

interface ValidationResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

class TimezoneComplianceValidator {
  private results: ValidationResult[] = [];
  
  private notificationService: NotificationService;
  private programService: ProgramService;
  private commitService: CommitService;
  private commitNotificationService: CommitNotificationService;

  constructor() {
    this.notificationService = new NotificationService();
    this.programService = new ProgramService();
    this.commitService = new CommitService();
    this.commitNotificationService = new CommitNotificationService();
  }

  /**
   * Run all validation tests
   */
  async runAllValidations(): Promise<ValidationResult[]> {
    console.log('🔍 Starting Timezone Compliance Validation...');
    console.log('='.repeat(50));

    // Core timezone utility validations
    await this.validateTimezoneUtils();
    
    // Service-level validations
    await this.validateNotificationService();
    await this.validateProgramService();
    await this.validateCommitService();
    await this.validateCommitNotificationService();
    
    // Cross-service consistency validations
    await this.validateCrossServiceConsistency();
    
    // Edge case validations
    await this.validateEdgeCases();
    
    // Performance validations
    await this.validatePerformance();

    this.generateValidationReport();
    return this.results;
  }

  private async validateTimezoneUtils() {
    console.log('\n🛠️  Validating Timezone Utils...');

    // Test timezone validation
    this.addResult({
      test: 'Timezone Validation',
      passed: isValidTimezone('America/New_York') && 
              isValidTimezone('Europe/London') && 
              !isValidTimezone('Invalid/Timezone'),
      message: 'Timezone validation works correctly'
    });

    // Test safe timezone fallback
    this.addResult({
      test: 'Safe Timezone Fallback',
      passed: getSafeTimezone('Invalid/Timezone') === 'UTC' &&
              getSafeTimezone('America/New_York') === 'America/New_York',
      message: 'Safe timezone fallback works correctly'
    });

    // Test date conversion
    const testDate = new Date('2024-01-15T12:00:00Z');
    const nyDate = convertDateToTimezone(testDate, 'America/New_York');
    const londonDate = convertDateToTimezone(testDate, 'Europe/London');
    
    this.addResult({
      test: 'Date Conversion',
      passed: nyDate instanceof Date && 
              londonDate instanceof Date &&
              nyDate.getHours() === 0 && 
              londonDate.getHours() === 0,
      message: 'Date conversion returns date-only objects',
      details: {
        nyDate: nyDate.toISOString(),
        londonDate: londonDate.toISOString()
      }
    });

    // Test participant timezone extraction
    const testParticipants = [
      { timezone: 'America/New_York' },
      { timezone: 'Europe/London' },
      { timezone: 'Invalid/Timezone' },
      { timezone: '' }
    ];
    
    const extractedTimezones = extractParticipantTimezones(testParticipants);
    
    this.addResult({
      test: 'Participant Timezone Extraction',
      passed: extractedTimezones.length === 2 &&
              extractedTimezones.includes('America/New_York') &&
              extractedTimezones.includes('Europe/London') &&
              !extractedTimezones.includes('Invalid/Timezone'),
      message: 'Participant timezone extraction filters invalid timezones',
      details: { extractedTimezones }
    });
  }

  private async validateNotificationService() {
    console.log('\n📬 Validating Notification Service...');

    // Test hours until deadline calculation
    try {
      const hours = (this.notificationService as any).calculateHoursUntilDeadline('America/New_York');
      
      this.addResult({
        test: 'Hours Until Deadline Calculation',
        passed: typeof hours === 'number' && hours >= 0 && hours <= 24,
        message: 'Hours until deadline calculation returns valid range',
        details: { hours }
      });
    } catch (error) {
      this.addResult({
        test: 'Hours Until Deadline Calculation',
        passed: false,
        message: `Error in calculation: ${error.message}`
      });
    }

    // Test date conversion in service
    try {
      const testDate = new Date('2024-01-15T12:00:00Z');
      const convertedDate = (this.notificationService as any).getDateInTimezone(testDate, 'America/New_York');
      
      this.addResult({
        test: 'Notification Service Date Conversion',
        passed: convertedDate instanceof Date && convertedDate.getHours() === 0,
        message: 'Notification service date conversion works correctly',
        details: { convertedDate: convertedDate.toISOString() }
      });
    } catch (error) {
      this.addResult({
        test: 'Notification Service Date Conversion',
        passed: false,
        message: `Error in date conversion: ${error.message}`
      });
    }

    // Test invalid timezone handling
    try {
      const hours = (this.notificationService as any).calculateHoursUntilDeadline('Invalid/Timezone');
      
      this.addResult({
        test: 'Invalid Timezone Handling (Notifications)',
        passed: typeof hours === 'number' && hours >= 0,
        message: 'Invalid timezone handled gracefully with fallback',
        details: { hours }
      });
    } catch (error) {
      this.addResult({
        test: 'Invalid Timezone Handling (Notifications)',
        passed: false,
        message: `Should not throw error: ${error.message}`
      });
    }
  }

  private async validateProgramService() {
    console.log('\n📋 Validating Program Service...');

    // Test program day calculation
    try {
      const result = (this.programService as any).calculateParticipantProgramDay(
        '2024-01-01',
        'America/New_York',
        30
      );
      
      this.addResult({
        test: 'Program Day Calculation',
        passed: typeof result.currentDay === 'number' && 
                result.currentDay > 0 &&
                ['upcoming', 'ongoing', 'ended'].includes(result.status),
        message: 'Program day calculation returns valid result',
        details: result
      });
    } catch (error) {
      this.addResult({
        test: 'Program Day Calculation',
        passed: false,
        message: `Error in calculation: ${error.message}`
      });
    }

    // Test date conversion in program service
    try {
      const testDate = new Date('2024-01-15T12:00:00Z');
      const convertedDate = (this.programService as any).getDateInTimezone(testDate, 'Europe/London');
      
      this.addResult({
        test: 'Program Service Date Conversion',
        passed: convertedDate instanceof Date && convertedDate.getHours() === 0,
        message: 'Program service date conversion works correctly',
        details: { convertedDate: convertedDate.toISOString() }
      });
    } catch (error) {
      this.addResult({
        test: 'Program Service Date Conversion',
        passed: false,
        message: `Error in date conversion: ${error.message}`
      });
    }
  }

  private async validateCommitService() {
    console.log('\n✅ Validating Commit Service...');

    const mockCommit = {
      timezone: 'America/New_York',
      schedule: {
        frequency: 'daily',
        startDate: '2024-01-01',
        deadline: {
          type: 'before',
          time: '23:00'
        }
      }
    };

    // Test should perform check today
    try {
      const shouldCheck = await (this.commitService as any).shouldPerformCheckToday(mockCommit, {});
      
      this.addResult({
        test: 'Should Perform Check Today',
        passed: typeof shouldCheck === 'boolean',
        message: 'Should perform check today returns boolean',
        details: { shouldCheck }
      });
    } catch (error) {
      this.addResult({
        test: 'Should Perform Check Today',
        passed: false,
        message: `Error in check: ${error.message}`
      });
    }

    // Test deadline check
    try {
      const deadlinePassed = await (this.commitService as any).hasDeadlinePassed(mockCommit, {
        deadlineType: 'before',
        deadlineTime: '23:00'
      });
      
      this.addResult({
        test: 'Deadline Check',
        passed: typeof deadlinePassed === 'boolean',
        message: 'Deadline check returns boolean',
        details: { deadlinePassed }
      });
    } catch (error) {
      this.addResult({
        test: 'Deadline Check',
        passed: false,
        message: `Error in deadline check: ${error.message}`
      });
    }
  }

  private async validateCommitNotificationService() {
    console.log('\n📬 Validating Commit Notification Service...');

    const mockCommit = {
      schedule: {
        startDate: '2024-01-01',
        deadline: {
          type: 'midnight'
        }
      }
    };

    // Test time until deadline calculation
    try {
      const timeUntilDeadline = (this.commitNotificationService as any).calculateTimeUntilDeadline(
        mockCommit, 
        'America/New_York'
      );
      
      this.addResult({
        test: 'Commit Time Until Deadline',
        passed: typeof timeUntilDeadline === 'number' && timeUntilDeadline >= 0,
        message: 'Commit time until deadline calculation works',
        details: { timeUntilDeadline }
      });
    } catch (error) {
      this.addResult({
        test: 'Commit Time Until Deadline',
        passed: false,
        message: `Error in calculation: ${error.message}`
      });
    }

    // Test current day calculation
    try {
      const currentDay = (this.commitNotificationService as any).getCurrentDayForCommit(
        mockCommit,
        'Europe/London'
      );
      
      this.addResult({
        test: 'Commit Current Day',
        passed: typeof currentDay === 'number' && currentDay > 0,
        message: 'Commit current day calculation works',
        details: { currentDay }
      });
    } catch (error) {
      this.addResult({
        test: 'Commit Current Day',
        passed: false,
        message: `Error in calculation: ${error.message}`
      });
    }
  }

  private async validateCrossServiceConsistency() {
    console.log('\n🔄 Validating Cross-Service Consistency...');

    const testDate = new Date('2024-01-15T12:00:00Z');
    const timezone = 'America/New_York';

    try {
      // Test that both services return the same date for same input
      const notificationDate = (this.notificationService as any).getDateInTimezone(testDate, timezone);
      const programDate = (this.programService as any).getDateInTimezone(testDate, timezone);
      
      this.addResult({
        test: 'Cross-Service Date Consistency',
        passed: notificationDate.getTime() === programDate.getTime(),
        message: 'Both services return same date for same input',
        details: {
          notificationDate: notificationDate.toISOString(),
          programDate: programDate.toISOString(),
          timeDifference: Math.abs(notificationDate.getTime() - programDate.getTime())
        }
      });
    } catch (error) {
      this.addResult({
        test: 'Cross-Service Date Consistency',
        passed: false,
        message: `Error in consistency check: ${error.message}`
      });
    }
  }

  private async validateEdgeCases() {
    console.log('\n🌍 Validating Edge Cases...');

    // Test unusual timezone offsets
    const unusualTimezones = ['Pacific/Chatham', 'Asia/Kathmandu'];
    
    for (const timezone of unusualTimezones) {
      try {
        const isValid = isValidTimezone(timezone);
        
        if (isValid) {
          const currentDate = getCurrentDateInTimezone(timezone);
          const testDate = convertDateToTimezone(new Date(), timezone);
          
          this.addResult({
            test: `Unusual Timezone (${timezone})`,
            passed: currentDate instanceof Date && testDate instanceof Date,
            message: `Unusual timezone ${timezone} handled correctly`,
            details: {
              currentDate: currentDate.toISOString(),
              testDate: testDate.toISOString()
            }
          });
        } else {
          this.addResult({
            test: `Unusual Timezone (${timezone})`,
            passed: false,
            message: `Timezone ${timezone} not recognized as valid`
          });
        }
      } catch (error) {
        this.addResult({
          test: `Unusual Timezone (${timezone})`,
          passed: false,
          message: `Error with ${timezone}: ${error.message}`
        });
      }
    }

    // Test DST transition dates
    const dstDates = [
      '2024-03-10T12:00:00Z', // Spring forward
      '2024-11-03T12:00:00Z'  // Fall back
    ];

    for (const dateStr of dstDates) {
      try {
        const testDate = new Date(dateStr);
        const nyDate = convertDateToTimezone(testDate, 'America/New_York');
        
        this.addResult({
          test: `DST Transition (${dateStr})`,
          passed: nyDate instanceof Date && nyDate.getHours() === 0,
          message: `DST transition date handled correctly`,
          details: {
            originalDate: testDate.toISOString(),
            convertedDate: nyDate.toISOString()
          }
        });
      } catch (error) {
        this.addResult({
          test: `DST Transition (${dateStr})`,
          passed: false,
          message: `Error with DST date: ${error.message}`
        });
      }
    }
  }

  private async validatePerformance() {
    console.log('\n⚡ Validating Performance...');

    const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'UTC'];
    const testDate = new Date('2024-01-15T12:00:00Z');
    const iterations = 1000;

    const startTime = Date.now();

    try {
      // Perform many timezone operations
      for (let i = 0; i < iterations; i++) {
        for (const timezone of timezones) {
          isValidTimezone(timezone);
          getCurrentDateInTimezone(timezone);
          convertDateToTimezone(testDate, timezone);
        }
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      const operationsPerSecond = (iterations * timezones.length * 3) / (duration / 1000);

      this.addResult({
        test: 'Performance Test',
        passed: duration < 5000, // Should complete within 5 seconds
        message: `Performance test completed in ${duration}ms`,
        details: {
          duration,
          iterations,
          operationsPerSecond: Math.round(operationsPerSecond),
          averageTimePerOperation: duration / (iterations * timezones.length * 3)
        }
      });
    } catch (error) {
      this.addResult({
        test: 'Performance Test',
        passed: false,
        message: `Performance test failed: ${error.message}`
      });
    }
  }

  private addResult(result: ValidationResult) {
    this.results.push(result);
    const emoji = result.passed ? '✅' : '❌';
    console.log(`  ${emoji} ${result.test}: ${result.message}`);
    
    if (result.details && !result.passed) {
      console.log(`     Details:`, result.details);
    }
  }

  private generateValidationReport() {
    console.log('\n📊 VALIDATION REPORT');
    console.log('='.repeat(30));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = (passedTests / totalTests * 100).toFixed(1);

    console.log(`\n📈 Summary:`);
    console.log(`  Total Tests: ${totalTests}`);
    console.log(`  Passed: ${passedTests} ✅`);
    console.log(`  Failed: ${failedTests} ❌`);
    console.log(`  Success Rate: ${successRate}%`);

    if (failedTests > 0) {
      console.log(`\n❌ Failed Tests:`);
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.test}: ${result.message}`);
      });
    }

    console.log(`\n${failedTests === 0 ? '🎉 All validations passed!' : '⚠️  Some validations failed'}`);
    console.log(`\n${failedTests === 0 ? '✅ Timezone compliance is VERIFIED' : '❌ Timezone compliance needs attention'}`);
  }
}

// Run validation if this file is executed directly
if (require.main === module) {
  const validator = new TimezoneComplianceValidator();
  
  validator.runAllValidations().then(results => {
    const failedCount = results.filter(r => !r.passed).length;
    process.exit(failedCount > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export { TimezoneComplianceValidator };
