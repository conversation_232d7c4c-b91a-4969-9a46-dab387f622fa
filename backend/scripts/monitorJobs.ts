/**
 * Production monitoring script for scheduled jobs
 * Monitors job execution, success rates, and timezone compliance
 */

import * as admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firebase Admin (use your production credentials)
if (!admin.apps.length) {
  admin.initializeApp({
    // Add your production Firebase config here
    credential: admin.credential.applicationDefault(),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

const db = getFirestore();

interface JobMetrics {
  jobName: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  lastExecution: Date;
  timezoneBreakdown: { [timezone: string]: number };
}

interface TimezoneAnalysis {
  timezone: string;
  participantCount: number;
  jobExecutions: number;
  successRate: number;
  averageNotificationTime: string;
  issues: string[];
}

async function getJobExecutionLogs(jobName: string, hours: number = 24): Promise<any[]> {
  console.log(`📊 Fetching ${jobName} logs from last ${hours} hours...`);
  
  const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
  
  // Query job execution logs (adjust collection name based on your logging setup)
  const logsSnapshot = await db.collection('job_logs')
    .where('jobName', '==', jobName)
    .where('timestamp', '>=', cutoffTime)
    .orderBy('timestamp', 'desc')
    .get();
  
  return logsSnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    timestamp: doc.data().timestamp.toDate()
  }));
}

async function analyzeJobMetrics(jobName: string, hours: number = 24): Promise<JobMetrics> {
  const logs = await getJobExecutionLogs(jobName, hours);
  
  const successful = logs.filter(log => log.success === true);
  const failed = logs.filter(log => log.success === false);
  
  const executionTimes = logs
    .filter(log => log.executionTime)
    .map(log => log.executionTime);
  
  const timezoneBreakdown: { [timezone: string]: number } = {};
  logs.forEach(log => {
    if (log.timezone) {
      timezoneBreakdown[log.timezone] = (timezoneBreakdown[log.timezone] || 0) + 1;
    }
  });
  
  return {
    jobName,
    totalExecutions: logs.length,
    successfulExecutions: successful.length,
    failedExecutions: failed.length,
    successRate: logs.length > 0 ? (successful.length / logs.length) * 100 : 0,
    averageExecutionTime: executionTimes.length > 0 
      ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length 
      : 0,
    lastExecution: logs.length > 0 ? logs[0].timestamp : new Date(0),
    timezoneBreakdown
  };
}

async function analyzeTimezoneCompliance(): Promise<TimezoneAnalysis[]> {
  console.log('🌍 Analyzing timezone compliance...');
  
  // Get all active participants with their timezones
  const participantsSnapshot = await db.collectionGroup('participants')
    .where('personalProgramStatus', '==', 'ongoing')
    .get();
  
  const timezoneStats: { [timezone: string]: TimezoneAnalysis } = {};
  
  participantsSnapshot.docs.forEach(doc => {
    const participant = doc.data();
    const timezone = participant.timezone || 'Unknown';
    
    if (!timezoneStats[timezone]) {
      timezoneStats[timezone] = {
        timezone,
        participantCount: 0,
        jobExecutions: 0,
        successRate: 0,
        averageNotificationTime: 'Unknown',
        issues: []
      };
    }
    
    timezoneStats[timezone].participantCount++;
  });
  
  // Analyze job execution for each timezone
  for (const timezone of Object.keys(timezoneStats)) {
    const jobLogs = await db.collection('job_logs')
      .where('timezone', '==', timezone)
      .where('timestamp', '>=', new Date(Date.now() - 24 * 60 * 60 * 1000))
      .get();
    
    const logs = jobLogs.docs.map(doc => doc.data());
    const successful = logs.filter(log => log.success === true);
    
    timezoneStats[timezone].jobExecutions = logs.length;
    timezoneStats[timezone].successRate = logs.length > 0 
      ? (successful.length / logs.length) * 100 
      : 0;
    
    // Analyze notification timing
    const notificationTimes = logs
      .filter(log => log.notificationTime)
      .map(log => new Date(log.notificationTime).toLocaleTimeString('en-US', { 
        timeZone: timezone,
        hour12: false 
      }));
    
    if (notificationTimes.length > 0) {
      // Calculate most common notification time
      const timeFrequency: { [time: string]: number } = {};
      notificationTimes.forEach(time => {
        timeFrequency[time] = (timeFrequency[time] || 0) + 1;
      });
      
      const mostCommonTime = Object.keys(timeFrequency)
        .reduce((a, b) => timeFrequency[a] > timeFrequency[b] ? a : b);
      
      timezoneStats[timezone].averageNotificationTime = mostCommonTime;
    }
    
    // Identify issues
    const issues: string[] = [];
    
    if (timezoneStats[timezone].successRate < 95) {
      issues.push(`Low success rate: ${timezoneStats[timezone].successRate.toFixed(1)}%`);
    }
    
    if (timezoneStats[timezone].jobExecutions === 0) {
      issues.push('No job executions in last 24 hours');
    }
    
    const failedLogs = logs.filter(log => log.success === false);
    if (failedLogs.length > 0) {
      const commonErrors = failedLogs
        .map(log => log.error)
        .filter(error => error)
        .reduce((acc: { [error: string]: number }, error) => {
          acc[error] = (acc[error] || 0) + 1;
          return acc;
        }, {});
      
      Object.keys(commonErrors).forEach(error => {
        issues.push(`${commonErrors[error]} occurrences of: ${error}`);
      });
    }
    
    timezoneStats[timezone].issues = issues;
  }
  
  return Object.values(timezoneStats);
}

async function checkJobHealth() {
  console.log('🏥 Checking job health...');
  
  const jobNames = [
    'dailyNotificationScheduler',
    'individualDailyCheckup',
    'commitDailyChecker',
    'commitNotificationScheduler',
    'programEnder'
  ];
  
  const healthReport: { [jobName: string]: any } = {};
  
  for (const jobName of jobNames) {
    const metrics = await analyzeJobMetrics(jobName);
    
    const health = {
      status: 'healthy',
      issues: [] as string[],
      metrics
    };
    
    // Check for issues
    if (metrics.successRate < 95) {
      health.status = 'unhealthy';
      health.issues.push(`Low success rate: ${metrics.successRate.toFixed(1)}%`);
    }
    
    if (metrics.totalExecutions === 0) {
      health.status = 'inactive';
      health.issues.push('No executions in last 24 hours');
    }
    
    const timeSinceLastExecution = Date.now() - metrics.lastExecution.getTime();
    if (timeSinceLastExecution > 2 * 60 * 60 * 1000) { // 2 hours
      health.status = 'stale';
      health.issues.push(`Last execution: ${metrics.lastExecution.toISOString()}`);
    }
    
    if (metrics.averageExecutionTime > 30000) { // 30 seconds
      health.status = 'slow';
      health.issues.push(`Slow execution: ${metrics.averageExecutionTime}ms average`);
    }
    
    healthReport[jobName] = health;
  }
  
  return healthReport;
}

async function generateDailyReport() {
  console.log('📋 Generating daily monitoring report...');
  console.log('=====================================');
  
  try {
    // Job health check
    const healthReport = await checkJobHealth();
    
    console.log('\n🏥 Job Health Summary:');
    Object.keys(healthReport).forEach(jobName => {
      const health = healthReport[jobName];
      const statusEmoji = {
        healthy: '✅',
        unhealthy: '❌',
        inactive: '⚠️',
        stale: '🕐',
        slow: '🐌'
      }[health.status] || '❓';
      
      console.log(`  ${statusEmoji} ${jobName}: ${health.status.toUpperCase()}`);
      
      if (health.issues.length > 0) {
        health.issues.forEach((issue: string) => {
          console.log(`    - ${issue}`);
        });
      }
      
      console.log(`    Executions: ${health.metrics.totalExecutions}, Success Rate: ${health.metrics.successRate.toFixed(1)}%`);
    });
    
    // Timezone compliance analysis
    const timezoneAnalysis = await analyzeTimezoneCompliance();
    
    console.log('\n🌍 Timezone Compliance Summary:');
    timezoneAnalysis.forEach(analysis => {
      const statusEmoji = analysis.issues.length === 0 ? '✅' : '⚠️';
      
      console.log(`  ${statusEmoji} ${analysis.timezone}:`);
      console.log(`    Participants: ${analysis.participantCount}`);
      console.log(`    Job Executions: ${analysis.jobExecutions}`);
      console.log(`    Success Rate: ${analysis.successRate.toFixed(1)}%`);
      console.log(`    Avg Notification Time: ${analysis.averageNotificationTime}`);
      
      if (analysis.issues.length > 0) {
        console.log(`    Issues:`);
        analysis.issues.forEach(issue => {
          console.log(`      - ${issue}`);
        });
      }
    });
    
    // Overall system health
    const overallHealth = Object.values(healthReport).every(health => health.status === 'healthy');
    const timezoneHealth = timezoneAnalysis.every(analysis => analysis.issues.length === 0);
    
    console.log('\n🎯 Overall System Health:');
    console.log(`  Jobs: ${overallHealth ? '✅ Healthy' : '❌ Issues Detected'}`);
    console.log(`  Timezone Compliance: ${timezoneHealth ? '✅ Compliant' : '⚠️ Issues Detected'}`);
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    
    const unhealthyJobs = Object.keys(healthReport).filter(job => healthReport[job].status !== 'healthy');
    if (unhealthyJobs.length > 0) {
      console.log(`  - Investigate ${unhealthyJobs.join(', ')} jobs`);
    }
    
    const problematicTimezones = timezoneAnalysis.filter(analysis => analysis.issues.length > 0);
    if (problematicTimezones.length > 0) {
      console.log(`  - Review timezone handling for: ${problematicTimezones.map(t => t.timezone).join(', ')}`);
    }
    
    if (overallHealth && timezoneHealth) {
      console.log('  - System is operating normally ✅');
    }
    
    console.log('\n=====================================');
    console.log('📋 Daily report completed');
    
  } catch (error) {
    console.error('❌ Failed to generate daily report:', error);
    throw error;
  }
}

async function alertOnCriticalIssues() {
  console.log('🚨 Checking for critical issues...');
  
  const healthReport = await checkJobHealth();
  const criticalIssues: string[] = [];
  
  Object.keys(healthReport).forEach(jobName => {
    const health = healthReport[jobName];
    
    if (health.status === 'unhealthy' && health.metrics.successRate < 50) {
      criticalIssues.push(`${jobName}: Critical failure rate (${health.metrics.successRate.toFixed(1)}%)`);
    }
    
    if (health.status === 'inactive') {
      criticalIssues.push(`${jobName}: No executions in 24 hours`);
    }
  });
  
  if (criticalIssues.length > 0) {
    console.log('🚨 CRITICAL ISSUES DETECTED:');
    criticalIssues.forEach(issue => {
      console.log(`  ❌ ${issue}`);
    });
    
    // Here you would send alerts to your monitoring system
    // e.g., Slack, email, PagerDuty, etc.
    
  } else {
    console.log('✅ No critical issues detected');
  }
  
  return criticalIssues;
}

// Main monitoring function
async function runMonitoring() {
  try {
    await generateDailyReport();
    const criticalIssues = await alertOnCriticalIssues();
    
    if (criticalIssues.length > 0) {
      process.exit(1); // Exit with error code for CI/CD
    }
    
  } catch (error) {
    console.error('Monitoring failed:', error);
    process.exit(1);
  }
}

// Instructions
console.log(`
🔍 Production Monitoring Instructions:

1. Set up environment variables:
   export FIREBASE_PROJECT_ID=your-production-project

2. Run daily monitoring:
   npx tsx backend/scripts/monitorJobs.ts

3. Set up cron job for automated monitoring:
   0 9 * * * cd /path/to/project && npx tsx backend/scripts/monitorJobs.ts

4. Integrate with your alerting system:
   - Modify alertOnCriticalIssues() to send alerts
   - Add webhook notifications
   - Connect to Slack/Discord/email

5. View detailed logs:
   firebase functions:log --project production
`);

// Run monitoring if this file is executed directly
if (require.main === module) {
  runMonitoring();
}

export {
  analyzeJobMetrics,
  analyzeTimezoneCompliance,
  checkJobHealth,
  generateDailyReport,
  alertOnCriticalIssues,
  runMonitoring
};
