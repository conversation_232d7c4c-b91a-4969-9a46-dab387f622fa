/**
 * Complete End-to-End Simulation
 * Simulates real-world program lifecycle with multiple participants across timezones
 */

import { NotificationService } from '../src/services/notificationService';
import { ProgramService } from '../src/services/programService';
import { CommitService } from '../src/services/commitService';
import { CommitNotificationService } from '../src/services/commitNotificationService';

interface SimulatedParticipant {
  id: string;
  fname: string;
  timezone: string;
  personalStartDate: string;
  personalEndDate: string;
  programPreferences: any;
  livesLeft: number;
  personalProgramStatus: string;
  personalCurrentDay: number;
  submissions: { [day: number]: 'submitted' | 'upcoming' | 'bailed' | 'not_submitted' };
}

interface SimulatedProgram {
  id: string;
  name: string;
  startDate: string;
  duration: number;
  participants: SimulatedParticipant[];
  status: 'upcoming' | 'ongoing' | 'ended';
}

interface SimulatedCommit {
  id: string;
  userId: string;
  timezone: string;
  schedule: {
    frequency: string;
    startDate: string;
    endDate: string;
    deadline: {
      type: string;
      time?: string;
    };
  };
  submissions: { [day: number]: 'submitted' | 'upcoming' | 'bailed' | 'not_submitted' };
}

class FullSimulation {
  private programs: SimulatedProgram[] = [];
  private commits: SimulatedCommit[] = [];
  private simulationDay: number = 1;
  private simulationStartDate: Date;
  
  private notificationService: NotificationService;
  private programService: ProgramService;
  private commitService: CommitService;
  private commitNotificationService: CommitNotificationService;

  constructor() {
    this.simulationStartDate = new Date();
    this.notificationService = new NotificationService();
    this.programService = new ProgramService();
    this.commitService = new CommitService();
    this.commitNotificationService = new CommitNotificationService();
  }

  /**
   * Create realistic test data
   */
  setupSimulationData() {
    console.log('🎬 Setting up simulation data...');

    // Create a diverse program with participants across multiple timezones
    const program: SimulatedProgram = {
      id: 'sim-program-global',
      name: 'Global Fitness Challenge',
      startDate: this.simulationStartDate.toISOString().split('T')[0],
      duration: 30,
      status: 'ongoing',
      participants: [
        {
          id: 'user-ny-john',
          fname: 'John',
          timezone: 'America/New_York',
          personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
          personalEndDate: new Date(this.simulationStartDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          programPreferences: {
            notificationTiming: {
              type: 'hours-before',
              hoursBeforeDeadline: 3
            }
          },
          livesLeft: 3,
          personalProgramStatus: 'ongoing',
          personalCurrentDay: 1,
          submissions: {}
        },
        {
          id: 'user-london-jane',
          fname: 'Jane',
          timezone: 'Europe/London',
          personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
          personalEndDate: new Date(this.simulationStartDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          programPreferences: {
            notificationTiming: {
              type: 'multiple-times',
              multipleTimes: ['09:00', '18:00']
            }
          },
          livesLeft: 3,
          personalProgramStatus: 'ongoing',
          personalCurrentDay: 1,
          submissions: {}
        },
        {
          id: 'user-tokyo-yuki',
          fname: 'Yuki',
          timezone: 'Asia/Tokyo',
          personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
          personalEndDate: new Date(this.simulationStartDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          programPreferences: {
            notificationTiming: {
              type: 'hours-before',
              hoursBeforeDeadline: 2
            }
          },
          livesLeft: 3,
          personalProgramStatus: 'ongoing',
          personalCurrentDay: 1,
          submissions: {}
        },
        {
          id: 'user-sydney-alex',
          fname: 'Alex',
          timezone: 'Australia/Sydney',
          personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
          personalEndDate: new Date(this.simulationStartDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          programPreferences: {
            notificationTiming: {
              type: 'multiple-times',
              multipleTimes: ['07:00', '19:00']
            }
          },
          livesLeft: 3,
          personalProgramStatus: 'ongoing',
          personalCurrentDay: 1,
          submissions: {}
        },
        {
          id: 'user-la-maria',
          fname: 'Maria',
          timezone: 'America/Los_Angeles',
          personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
          personalEndDate: new Date(this.simulationStartDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          programPreferences: {
            notificationTiming: {
              type: 'hours-before',
              hoursBeforeDeadline: 4
            }
          },
          livesLeft: 3,
          personalProgramStatus: 'ongoing',
          personalCurrentDay: 1,
          submissions: {}
        }
      ]
    };

    this.programs.push(program);

    // Create individual commits for some participants
    const commits: SimulatedCommit[] = [
      {
        id: 'commit-john-reading',
        userId: 'user-ny-john',
        timezone: 'America/New_York',
        schedule: {
          frequency: 'daily',
          startDate: this.simulationStartDate.toISOString().split('T')[0],
          endDate: new Date(this.simulationStartDate.getTime() + 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          deadline: {
            type: 'before',
            time: '22:00'
          }
        },
        submissions: {}
      },
      {
        id: 'commit-jane-meditation',
        userId: 'user-london-jane',
        timezone: 'Europe/London',
        schedule: {
          frequency: 'daily',
          startDate: this.simulationStartDate.toISOString().split('T')[0],
          endDate: new Date(this.simulationStartDate.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          deadline: {
            type: 'midnight'
          }
        },
        submissions: {}
      }
    ];

    this.commits.push(...commits);

    console.log(`✅ Created simulation with:`);
    console.log(`   📋 ${this.programs.length} program(s)`);
    console.log(`   👥 ${program.participants.length} participants`);
    console.log(`   ✅ ${this.commits.length} individual commit(s)`);
    console.log(`   🌍 Timezones: ${[...new Set(program.participants.map(p => p.timezone))].join(', ')}`);
  }

  /**
   * Simulate a single day of scheduled job execution
   */
  async simulateDay(day: number) {
    console.log(`\n📅 === SIMULATION DAY ${day} ===`);
    
    const currentDate = new Date(this.simulationStartDate.getTime() + (day - 1) * 24 * 60 * 60 * 1000);
    console.log(`🕐 Simulated date: ${currentDate.toDateString()}`);

    // Mock the current time for consistent testing
    const originalDate = Date;
    global.Date = class extends Date {
      constructor(...args: any[]) {
        if (args.length === 0) {
          super(currentDate);
        } else {
          super(...args);
        }
      }
      
      static now() {
        return currentDate.getTime();
      }
    } as any;

    try {
      // Simulate morning jobs (notifications, checkups)
      await this.simulateMorningJobs(day);
      
      // Simulate user activity during the day
      await this.simulateUserActivity(day);
      
      // Simulate evening jobs (deadline checks, commit verification)
      await this.simulateEveningJobs(day);
      
      // Update simulation state
      this.updateSimulationState(day);
      
    } finally {
      // Restore original Date
      global.Date = originalDate;
    }
  }

  private async simulateMorningJobs(day: number) {
    console.log(`\n🌅 Morning Jobs (Day ${day})`);
    
    // Simulate notification scheduling for each participant
    for (const program of this.programs) {
      for (const participant of program.participants) {
        if (participant.personalProgramStatus === 'ongoing') {
          try {
            console.log(`  📬 Processing notifications for ${participant.fname} (${participant.timezone})`);
            
            const result = await this.notificationService.handleRecurringNotification({
              programId: program.id,
              participantId: participant.id,
              personalStartDate: participant.personalStartDate,
              personalEndDate: participant.personalEndDate,
              timezone: participant.timezone
            });
            
            console.log(`    ${result.success ? '✅' : '❌'} ${result.message}`);
            
            // Calculate notification times
            const notificationTimes = (this.programService as any).calculateNotificationTimes(participant);
            console.log(`    🕐 Notification times: ${notificationTimes.join(', ')}`);
            
          } catch (error) {
            console.error(`    ❌ Error for ${participant.fname}:`, error.message);
          }
        }
      }
    }
  }

  private async simulateUserActivity(day: number) {
    console.log(`\n☀️ User Activity Simulation (Day ${day})`);
    
    // Simulate random user submissions (80% success rate)
    for (const program of this.programs) {
      for (const participant of program.participants) {
        if (participant.personalProgramStatus === 'ongoing') {
          const willSubmit = Math.random() > 0.2; // 80% chance
          const submissionStatus = willSubmit ? 'submitted' : 'upcoming';
          
          participant.submissions[day] = submissionStatus;
          
          console.log(`  👤 ${participant.fname}: ${willSubmit ? '✅ Submitted' : '⏳ No submission yet'}`);
        }
      }
    }
    
    // Simulate commit submissions
    for (const commit of this.commits) {
      const willSubmit = Math.random() > 0.15; // 85% chance
      const submissionStatus = willSubmit ? 'submitted' : 'upcoming';
      
      commit.submissions[day] = submissionStatus;
      
      const participant = this.programs[0].participants.find(p => p.id === commit.userId);
      console.log(`  ✅ ${participant?.fname} commit: ${willSubmit ? '✅ Submitted' : '⏳ No submission yet'}`);
    }
  }

  private async simulateEveningJobs(day: number) {
    console.log(`\n🌆 Evening Jobs (Day ${day})`);
    
    // Simulate individual daily checkups
    for (const program of this.programs) {
      for (const participant of program.participants) {
        if (participant.personalProgramStatus === 'ongoing') {
          try {
            console.log(`  🔍 Daily checkup for ${participant.fname} (${participant.timezone})`);
            
            const result = await this.programService.performIndividualDailyCheckup({
              programId: program.id,
              participantId: participant.id,
              personalStartDate: participant.personalStartDate,
              personalEndDate: participant.personalEndDate,
              timezone: participant.timezone
            });
            
            console.log(`    ${result.success ? '✅' : '❌'} ${result.message}`);
            
            // Simulate missed submission handling
            if (participant.submissions[day] === 'upcoming') {
              console.log(`    ⚠️ Processing missed submission for day ${day}`);
              
              if (participant.livesLeft > 0) {
                participant.livesLeft--;
                participant.submissions[day] = 'bailed';
                console.log(`    💔 Life deducted. Lives left: ${participant.livesLeft}`);
                
                if (participant.livesLeft === 0) {
                  participant.personalProgramStatus = 'disqualified';
                  console.log(`    ❌ ${participant.fname} disqualified`);
                }
              }
            }
            
          } catch (error) {
            console.error(`    ❌ Error for ${participant.fname}:`, error.message);
          }
        }
      }
    }
    
    // Simulate commit deadline checks
    for (const commit of this.commits) {
      try {
        const participant = this.programs[0].participants.find(p => p.id === commit.userId);
        console.log(`  ✅ Commit check for ${participant?.fname} (${commit.timezone})`);
        
        const result = await this.commitService.performDailyCommitCheck({
          commitId: commit.id,
          userId: commit.userId,
          timezone: commit.timezone,
          deadlineType: commit.schedule.deadline.type,
          deadlineTime: commit.schedule.deadline.time
        });
        
        console.log(`    ${result.success ? '✅' : '❌'} ${result.message}`);
        
        // Process missed commit submissions
        if (commit.submissions[day] === 'upcoming') {
          commit.submissions[day] = 'not_submitted';
          console.log(`    ❌ Commit deadline missed`);
        }
        
      } catch (error) {
        console.error(`    ❌ Commit check error:`, error.message);
      }
    }
  }

  private updateSimulationState(day: number) {
    // Update participant current days
    for (const program of this.programs) {
      for (const participant of program.participants) {
        if (participant.personalProgramStatus === 'ongoing') {
          participant.personalCurrentDay = day;
          
          // Check if program should end for this participant
          const programEndDate = new Date(participant.personalEndDate);
          const currentDate = new Date(this.simulationStartDate.getTime() + (day - 1) * 24 * 60 * 60 * 1000);
          
          if (currentDate >= programEndDate) {
            participant.personalProgramStatus = 'ended';
            console.log(`    🏁 ${participant.fname} completed the program`);
          }
        }
      }
    }
  }

  /**
   * Generate comprehensive simulation report
   */
  generateReport() {
    console.log(`\n📊 === SIMULATION REPORT ===`);
    console.log(`🕐 Total simulation days: ${this.simulationDay - 1}`);
    
    for (const program of this.programs) {
      console.log(`\n📋 Program: ${program.name}`);
      
      for (const participant of program.participants) {
        const totalDays = Object.keys(participant.submissions).length;
        const submitted = Object.values(participant.submissions).filter(s => s === 'submitted').length;
        const bailed = Object.values(participant.submissions).filter(s => s === 'bailed').length;
        const missed = Object.values(participant.submissions).filter(s => s === 'not_submitted').length;
        
        const successRate = totalDays > 0 ? (submitted / totalDays * 100).toFixed(1) : '0';
        
        console.log(`  👤 ${participant.fname} (${participant.timezone}):`);
        console.log(`    Status: ${participant.personalProgramStatus}`);
        console.log(`    Success Rate: ${successRate}%`);
        console.log(`    Lives Left: ${participant.livesLeft}`);
        console.log(`    Submissions: ${submitted} ✅, ${bailed} 💔, ${missed} ❌`);
      }
    }
    
    // Commit report
    if (this.commits.length > 0) {
      console.log(`\n✅ Individual Commits:`);
      
      for (const commit of this.commits) {
        const participant = this.programs[0].participants.find(p => p.id === commit.userId);
        const totalDays = Object.keys(commit.submissions).length;
        const submitted = Object.values(commit.submissions).filter(s => s === 'submitted').length;
        const missed = Object.values(commit.submissions).filter(s => s === 'not_submitted').length;
        
        const successRate = totalDays > 0 ? (submitted / totalDays * 100).toFixed(1) : '0';
        
        console.log(`  ✅ ${participant?.fname} commit (${commit.timezone}):`);
        console.log(`    Success Rate: ${successRate}%`);
        console.log(`    Submissions: ${submitted} ✅, ${missed} ❌`);
      }
    }
    
    // Timezone analysis
    console.log(`\n🌍 Timezone Analysis:`);
    const timezones = [...new Set(this.programs[0].participants.map(p => p.timezone))];
    
    for (const timezone of timezones) {
      const participants = this.programs[0].participants.filter(p => p.timezone === timezone);
      const activeParticipants = participants.filter(p => p.personalProgramStatus === 'ongoing').length;
      const completedParticipants = participants.filter(p => p.personalProgramStatus === 'ended').length;
      const disqualifiedParticipants = participants.filter(p => p.personalProgramStatus === 'disqualified').length;
      
      console.log(`  🌍 ${timezone}:`);
      console.log(`    Active: ${activeParticipants}, Completed: ${completedParticipants}, Disqualified: ${disqualifiedParticipants}`);
    }
  }

  /**
   * Run complete simulation
   */
  async runFullSimulation(days: number = 7) {
    console.log(`🎬 Starting Full Simulation (${days} days)`);
    console.log('='.repeat(50));
    
    this.setupSimulationData();
    
    for (let day = 1; day <= days; day++) {
      await this.simulateDay(day);
      this.simulationDay = day + 1;
      
      // Add small delay to make it more realistic
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.generateReport();
    
    console.log('\n🎉 Simulation completed successfully!');
    console.log('='.repeat(50));
  }
}

// Export for use in other scripts
export { FullSimulation };

// Run simulation if this file is executed directly
if (require.main === module) {
  const simulation = new FullSimulation();
  
  // Run 7-day simulation by default
  const days = parseInt(process.argv[2]) || 7;
  simulation.runFullSimulation(days);
}
