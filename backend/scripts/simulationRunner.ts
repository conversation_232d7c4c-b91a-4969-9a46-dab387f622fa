#!/usr/bin/env node

/**
 * Comprehensive Simulation Runner
 * Master script to run all types of simulations and tests
 */

import { FullSimulation } from './fullSimulation';
import { InteractiveSimulation } from './interactiveSimulation';
import * as readline from 'readline';

interface SimulationOptions {
  mode: 'basic' | 'full' | 'interactive' | 'stress' | 'timezone' | 'dst';
  days?: number;
  participants?: number;
  speed?: number;
  verbose?: boolean;
}

class SimulationRunner {
  private rl: readline.Interface;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async showMainMenu(): Promise<SimulationOptions> {
    console.clear();
    console.log('🎬 ACCUSTOM SCHEDULED JOBS SIMULATION SUITE');
    console.log('==========================================');
    console.log('');
    console.log('Choose a simulation mode:');
    console.log('');
    console.log('1. 🚀 Basic Simulation (Quick test - 3 days)');
    console.log('2. 📋 Full Simulation (Complete test - 7 days)');
    console.log('3. 🎮 Interactive Simulation (Real-time with controls)');
    console.log('4. 💪 Stress Test (High load simulation)');
    console.log('5. 🌍 Timezone Focus (Multi-timezone edge cases)');
    console.log('6. 🕐 DST Transition Test (Daylight saving time)');
    console.log('7. 🧪 All Tests (Run everything)');
    console.log('8. ❌ Exit');
    console.log('');

    const choice = await this.askQuestion('Enter your choice (1-8): ');
    
    switch (choice.trim()) {
      case '1':
        return { mode: 'basic', days: 3, verbose: false };
      case '2':
        return { mode: 'full', days: 7, verbose: true };
      case '3':
        return { mode: 'interactive' };
      case '4':
        return await this.configureStressTest();
      case '5':
        return { mode: 'timezone', days: 5, verbose: true };
      case '6':
        return { mode: 'dst', days: 10, verbose: true };
      case '7':
        return await this.runAllTests();
      case '8':
        process.exit(0);
      default:
        console.log('Invalid choice. Please try again.');
        return this.showMainMenu();
    }
  }

  private async configureStressTest(): Promise<SimulationOptions> {
    console.log('\n💪 Stress Test Configuration:');
    
    const participants = await this.askQuestion('Number of participants (default: 100): ');
    const days = await this.askQuestion('Number of days (default: 7): ');
    const speed = await this.askQuestion('Speed multiplier (default: 10x): ');
    
    return {
      mode: 'stress',
      participants: parseInt(participants) || 100,
      days: parseInt(days) || 7,
      speed: parseInt(speed) || 10,
      verbose: false
    };
  }

  private async runAllTests(): Promise<SimulationOptions> {
    console.log('\n🧪 Running All Tests...');
    console.log('This will run all simulation modes sequentially.');
    console.log('Estimated time: 10-15 minutes');
    
    const confirm = await this.askQuestion('Continue? (y/N): ');
    if (confirm.toLowerCase() !== 'y') {
      return this.showMainMenu();
    }
    
    // Run all tests sequentially
    await this.runBasicSimulation();
    await this.runTimezoneTest();
    await this.runDSTTest();
    await this.runStressTest({ participants: 50, days: 3, speed: 5 });
    
    console.log('\n✅ All tests completed successfully!');
    process.exit(0);
  }

  async runSimulation(options: SimulationOptions) {
    console.log(`\n🎬 Starting ${options.mode} simulation...`);
    
    switch (options.mode) {
      case 'basic':
        await this.runBasicSimulation(options.days);
        break;
      case 'full':
        await this.runFullSimulation(options.days);
        break;
      case 'interactive':
        await this.runInteractiveSimulation();
        break;
      case 'stress':
        await this.runStressTest(options);
        break;
      case 'timezone':
        await this.runTimezoneTest();
        break;
      case 'dst':
        await this.runDSTTest();
        break;
    }
  }

  private async runBasicSimulation(days: number = 3) {
    console.log('🚀 Basic Simulation - Testing core functionality');
    console.log('='.repeat(50));
    
    const simulation = new FullSimulation();
    await simulation.runFullSimulation(days);
    
    console.log('\n✅ Basic simulation completed');
    await this.waitForContinue();
  }

  private async runFullSimulation(days: number = 7) {
    console.log('📋 Full Simulation - Comprehensive testing');
    console.log('='.repeat(50));
    
    const simulation = new FullSimulation();
    await simulation.runFullSimulation(days);
    
    console.log('\n✅ Full simulation completed');
    await this.waitForContinue();
  }

  private async runInteractiveSimulation() {
    console.log('🎮 Interactive Simulation - Real-time controls');
    console.log('='.repeat(50));
    
    const simulation = new InteractiveSimulation();
    await simulation.startInteractiveSimulation();
  }

  private async runStressTest(options: { participants?: number; days?: number; speed?: number }) {
    console.log('💪 Stress Test - High load simulation');
    console.log('='.repeat(50));
    
    const participants = options.participants || 100;
    const days = options.days || 7;
    const speed = options.speed || 10;
    
    console.log(`📊 Configuration:`);
    console.log(`   👥 Participants: ${participants}`);
    console.log(`   📅 Days: ${days}`);
    console.log(`   ⚡ Speed: ${speed}x`);
    
    // Create stress test simulation
    const simulation = new FullSimulation();
    
    // Override setup to create more participants
    (simulation as any).setupStressTestData = function() {
      console.log(`🔧 Setting up stress test with ${participants} participants...`);
      
      const timezones = [
        'America/New_York', 'Europe/London', 'Asia/Tokyo', 'Australia/Sydney',
        'America/Los_Angeles', 'Europe/Paris', 'Asia/Shanghai', 'America/Chicago',
        'Europe/Berlin', 'Asia/Mumbai', 'America/Toronto', 'Pacific/Auckland'
      ];
      
      const testParticipants = [];
      
      for (let i = 0; i < participants; i++) {
        const timezone = timezones[i % timezones.length];
        testParticipants.push({
          id: `stress-user-${i}`,
          fname: `User${i}`,
          timezone: timezone,
          personalStartDate: new Date().toISOString().split('T')[0],
          personalEndDate: new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          programPreferences: {
            notificationTiming: {
              type: Math.random() > 0.5 ? 'hours-before' : 'multiple-times',
              hoursBeforeDeadline: Math.floor(Math.random() * 5) + 1,
              multipleTimes: ['09:00', '18:00']
            }
          },
          livesLeft: 3,
          personalProgramStatus: 'ongoing',
          personalCurrentDay: 1,
          submissions: {}
        });
      }
      
      this.programs = [{
        id: 'stress-test-program',
        name: 'Stress Test Program',
        startDate: new Date().toISOString().split('T')[0],
        duration: days,
        status: 'ongoing',
        participants: testParticipants
      }];
      
      console.log(`✅ Created ${participants} participants across ${timezones.length} timezones`);
    };
    
    // Run stress test
    (simulation as any).setupStressTestData();
    
    const startTime = Date.now();
    await simulation.runFullSimulation(days);
    const endTime = Date.now();
    
    const duration = (endTime - startTime) / 1000;
    const operationsPerSecond = (participants * days * 24) / duration; // Approximate operations
    
    console.log('\n📊 Stress Test Results:');
    console.log(`   ⏱️  Duration: ${duration.toFixed(2)} seconds`);
    console.log(`   🚀 Operations/sec: ${operationsPerSecond.toFixed(0)}`);
    console.log(`   💾 Memory usage: ${process.memoryUsage().heapUsed / 1024 / 1024:.1f} MB`);
    
    console.log('\n✅ Stress test completed');
    await this.waitForContinue();
  }

  private async runTimezoneTest() {
    console.log('🌍 Timezone Focus Test - Multi-timezone edge cases');
    console.log('='.repeat(50));
    
    // Test with unusual timezones and edge cases
    const simulation = new FullSimulation();
    
    // Override setup for timezone-specific testing
    (simulation as any).setupTimezoneTestData = function() {
      const edgeTimezones = [
        'Pacific/Chatham',     // UTC+12:45
        'Asia/Kathmandu',      // UTC+5:45
        'Australia/Adelaide',  // UTC+9:30
        'America/St_Johns',    // UTC-3:30
        'Pacific/Marquesas',   // UTC-9:30
        'UTC',                 // UTC+0
        'Pacific/Kiritimati'   // UTC+14
      ];
      
      const testParticipants = edgeTimezones.map((timezone, i) => ({
        id: `timezone-user-${i}`,
        fname: `User${i}`,
        timezone: timezone,
        personalStartDate: new Date().toISOString().split('T')[0],
        personalEndDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'hours-before',
            hoursBeforeDeadline: 2
          }
        },
        livesLeft: 3,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        submissions: {}
      }));
      
      this.programs = [{
        id: 'timezone-test-program',
        name: 'Timezone Edge Case Test',
        startDate: new Date().toISOString().split('T')[0],
        duration: 5,
        status: 'ongoing',
        participants: testParticipants
      }];
      
      console.log(`✅ Created timezone test with unusual offsets`);
    };
    
    (simulation as any).setupTimezoneTestData();
    await simulation.runFullSimulation(5);
    
    console.log('\n✅ Timezone test completed');
    await this.waitForContinue();
  }

  private async runDSTTest() {
    console.log('🕐 DST Transition Test - Daylight saving time handling');
    console.log('='.repeat(50));
    
    // Test around DST transition dates
    const simulation = new FullSimulation();
    
    console.log('📅 Testing around DST transition dates:');
    console.log('   🌸 Spring Forward: March 10, 2024');
    console.log('   🍂 Fall Back: November 3, 2024');
    
    // Mock dates around DST transitions
    const dstDates = [
      { date: '2024-03-09', name: 'Before Spring DST' },
      { date: '2024-03-10', name: 'Spring DST Transition' },
      { date: '2024-03-11', name: 'After Spring DST' },
      { date: '2024-11-02', name: 'Before Fall DST' },
      { date: '2024-11-03', name: 'Fall DST Transition' },
      { date: '2024-11-04', name: 'After Fall DST' }
    ];
    
    for (const dstDate of dstDates) {
      console.log(`\n🕐 Testing ${dstDate.name} (${dstDate.date})`);
      
      // Override simulation start date
      (simulation as any).simulationStartDate = new Date(dstDate.date + 'T12:00:00Z');
      
      simulation.setupSimulationData();
      await simulation.runFullSimulation(1); // Just 1 day for each DST test
    }
    
    console.log('\n✅ DST transition test completed');
    await this.waitForContinue();
  }

  private async askQuestion(question: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer);
      });
    });
  }

  private async waitForContinue() {
    await this.askQuestion('\nPress Enter to continue...');
  }

  async cleanup() {
    this.rl.close();
  }
}

// Main execution
async function main() {
  const runner = new SimulationRunner();
  
  try {
    // Check for command line arguments
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
      // Direct mode from command line
      const mode = args[0] as SimulationOptions['mode'];
      const days = parseInt(args[1]) || 7;
      
      await runner.runSimulation({ mode, days });
    } else {
      // Interactive menu mode
      while (true) {
        const options = await runner.showMainMenu();
        await runner.runSimulation(options);
        
        const again = await runner.askQuestion('\nRun another simulation? (y/N): ');
        if (again.toLowerCase() !== 'y') {
          break;
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Simulation error:', error);
    process.exit(1);
  } finally {
    await runner.cleanup();
  }
}

// Usage instructions
if (require.main === module) {
  console.log(`
🎬 ACCUSTOM SIMULATION SUITE
===========================

Usage:
  npx tsx backend/scripts/simulationRunner.ts                    # Interactive menu
  npx tsx backend/scripts/simulationRunner.ts basic             # Quick 3-day test
  npx tsx backend/scripts/simulationRunner.ts full 7            # 7-day full test
  npx tsx backend/scripts/simulationRunner.ts interactive       # Real-time simulation
  npx tsx backend/scripts/simulationRunner.ts stress            # Stress test
  npx tsx backend/scripts/simulationRunner.ts timezone          # Timezone edge cases
  npx tsx backend/scripts/simulationRunner.ts dst               # DST transitions

Features:
  ✅ Multi-timezone participant simulation
  ✅ Real-time job execution testing
  ✅ Interactive controls and monitoring
  ✅ Stress testing with high participant counts
  ✅ Edge case testing (DST, unusual timezones)
  ✅ Comprehensive reporting and metrics

`);
  
  main();
}

export { SimulationRunner };
