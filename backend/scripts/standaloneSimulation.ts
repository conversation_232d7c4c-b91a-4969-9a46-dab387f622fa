/**
 * Standalone Timezone Simulation
 * Simulates scheduled job timezone logic without Firebase dependencies
 */

import {
  isValidTimezone,
  getSafeTimezone,
  getCurrentDateInTimezone,
  convertDateToTimezone,
  extractParticipantTimezones
} from '../src/utils/timezoneUtils';

interface SimulatedParticipant {
  id: string;
  fname: string;
  timezone: string;
  personalStartDate: string;
  personalEndDate: string;
  programPreferences: any;
  livesLeft: number;
  personalProgramStatus: string;
  personalCurrentDay: number;
  submissions: { [day: number]: 'submitted' | 'upcoming' | 'bailed' | 'not_submitted' };
}

class StandaloneSimulation {
  private participants: SimulatedParticipant[] = [];
  private simulationStartDate: Date;
  private currentDay: number = 1;

  constructor() {
    this.simulationStartDate = new Date();
    this.setupParticipants();
  }

  private setupParticipants() {
    console.log('🎬 Setting up global participants...');

    this.participants = [
      {
        id: 'user-ny-john',
        fname: '<PERSON> (New York)',
        timezone: 'America/New_York',
        personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
        personalEndDate: new Date(this.simulationStartDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'hours-before',
            hoursBeforeDeadline: 3
          }
        },
        livesLeft: 3,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        submissions: {}
      },
      {
        id: 'user-london-jane',
        fname: 'Jane (London)',
        timezone: 'Europe/London',
        personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
        personalEndDate: new Date(this.simulationStartDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'multiple-times',
            multipleTimes: ['09:00', '18:00']
          }
        },
        livesLeft: 3,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        submissions: {}
      },
      {
        id: 'user-tokyo-yuki',
        fname: 'Yuki (Tokyo)',
        timezone: 'Asia/Tokyo',
        personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
        personalEndDate: new Date(this.simulationStartDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'hours-before',
            hoursBeforeDeadline: 2
          }
        },
        livesLeft: 3,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        submissions: {}
      },
      {
        id: 'user-sydney-alex',
        fname: 'Alex (Sydney)',
        timezone: 'Australia/Sydney',
        personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
        personalEndDate: new Date(this.simulationStartDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'multiple-times',
            multipleTimes: ['07:00', '19:00']
          }
        },
        livesLeft: 3,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        submissions: {}
      },
      {
        id: 'user-la-maria',
        fname: 'Maria (Los Angeles)',
        timezone: 'America/Los_Angeles',
        personalStartDate: this.simulationStartDate.toISOString().split('T')[0],
        personalEndDate: new Date(this.simulationStartDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'hours-before',
            hoursBeforeDeadline: 4
          }
        },
        livesLeft: 3,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        submissions: {}
      }
    ];

    console.log(`✅ Created ${this.participants.length} participants across ${[...new Set(this.participants.map(p => p.timezone))].length} timezones`);
  }

  private simulateTimezoneCalculations(day: number) {
    console.log(`\n🌍 === DAY ${day} TIMEZONE CALCULATIONS ===`);
    
    const currentDate = new Date(this.simulationStartDate.getTime() + (day - 1) * 24 * 60 * 60 * 1000);
    console.log(`📅 Simulation Date: ${currentDate.toDateString()}`);

    for (const participant of this.participants) {
      console.log(`\n👤 ${participant.fname} (${participant.timezone}):`);

      try {
        // Test timezone validation
        const isValid = isValidTimezone(participant.timezone);
        console.log(`   🔍 Timezone Valid: ${isValid ? '✅' : '❌'}`);

        // Get current date in user's timezone
        const userDate = getCurrentDateInTimezone(participant.timezone);
        console.log(`   📅 Current Date (User TZ): ${userDate.toDateString()}`);

        // Convert simulation date to user timezone
        const convertedDate = convertDateToTimezone(currentDate, participant.timezone);
        console.log(`   🔄 Converted Date: ${convertedDate.toDateString()}`);

        // Calculate hours until midnight (deadline)
        const hoursUntilMidnight = this.calculateHoursUntilDeadline(participant.timezone);
        console.log(`   ⏰ Hours Until Deadline: ${hoursUntilMidnight.toFixed(2)}`);

        // Calculate notification times
        const notificationTimes = this.calculateNotificationTimes(participant);
        console.log(`   🔔 Notification Times: ${notificationTimes.join(', ')}`);

        // Show current local time
        const localTime = currentDate.toLocaleString('en-US', {
          timeZone: participant.timezone,
          weekday: 'short',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        });
        console.log(`   🕐 Local Time: ${localTime}`);

        // Simulate user activity (80% success rate)
        const willSubmit = Math.random() > 0.2;
        participant.submissions[day] = willSubmit ? 'submitted' : 'upcoming';
        console.log(`   📊 Submission: ${willSubmit ? '✅ Submitted' : '⏳ Pending'}`);

        // Update participant status
        participant.personalCurrentDay = day;

      } catch (error) {
        console.error(`   ❌ Error processing ${participant.fname}: ${error.message}`);
      }
    }
  }

  private calculateHoursUntilDeadline(timezone: string): number {
    try {
      const now = new Date();
      
      // Get current time in user's timezone
      const userNowString = now.toLocaleString("en-US", { 
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
      
      // Parse the timezone-adjusted time
      const [datePart, timePart] = userNowString.split(', ');
      const [month, day, year] = datePart.split('/');
      const [hour, minute, second] = timePart.split(':');
      
      const userNow = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );

      // Calculate next midnight in user's timezone
      const midnight = new Date(userNow);
      midnight.setHours(24, 0, 0, 0);

      const hoursUntilDeadline = (midnight.getTime() - userNow.getTime()) / (1000 * 60 * 60);
      return Math.max(0, hoursUntilDeadline);
    } catch (error) {
      console.error(`Error calculating hours until deadline for timezone ${timezone}:`, error);
      return 12; // Fallback
    }
  }

  private calculateNotificationTimes(participant: SimulatedParticipant): string[] {
    try {
      const notificationTiming = participant.programPreferences?.notificationTiming;
      
      if (!notificationTiming) {
        return ['20:00']; // Default to 8 PM
      }

      switch (notificationTiming.type) {
        case 'multiple-times':
          if (notificationTiming.multipleTimes && notificationTiming.multipleTimes.length > 0) {
            return notificationTiming.multipleTimes;
          } else {
            return ['20:00'];
          }

        case 'hours-before':
          const hoursBeforeDeadline = notificationTiming.hoursBeforeDeadline || 4;
          const notificationHour = 24 - hoursBeforeDeadline;
          const validHour = Math.max(0, Math.min(23, notificationHour));
          const notificationTime = `${validHour.toString().padStart(2, '0')}:00`;
          return [notificationTime];

        default:
          return ['20:00'];
      }
    } catch (error) {
      console.error(`Error calculating notification times for ${participant.id}:`, error);
      return ['20:00'];
    }
  }

  private simulateEndOfDayProcessing(day: number) {
    console.log(`\n🌙 === END OF DAY ${day} PROCESSING ===`);

    for (const participant of this.participants) {
      if (participant.personalProgramStatus === 'ongoing') {
        console.log(`\n🔍 Processing ${participant.fname}:`);

        // Check if submission was made
        if (participant.submissions[day] === 'upcoming') {
          console.log(`   ⚠️ No submission detected`);
          
          if (participant.livesLeft > 0) {
            participant.livesLeft--;
            participant.submissions[day] = 'bailed';
            console.log(`   💔 Life deducted. Lives remaining: ${participant.livesLeft}`);
            
            if (participant.livesLeft === 0) {
              participant.personalProgramStatus = 'disqualified';
              console.log(`   ❌ ${participant.fname} disqualified from program`);
            }
          }
        } else {
          console.log(`   ✅ Submission recorded successfully`);
        }

        // Check if program should end
        const programEndDate = new Date(participant.personalEndDate);
        const currentDate = new Date(this.simulationStartDate.getTime() + (day - 1) * 24 * 60 * 60 * 1000);
        
        if (currentDate >= programEndDate) {
          participant.personalProgramStatus = 'ended';
          console.log(`   🏁 ${participant.fname} completed the program`);
        }
      }
    }
  }

  private generateDayReport(day: number) {
    console.log(`\n📊 === DAY ${day} SUMMARY ===`);

    const activeParticipants = this.participants.filter(p => p.personalProgramStatus === 'ongoing').length;
    const completedParticipants = this.participants.filter(p => p.personalProgramStatus === 'ended').length;
    const disqualifiedParticipants = this.participants.filter(p => p.personalProgramStatus === 'disqualified').length;

    console.log(`👥 Participant Status:`);
    console.log(`   🟢 Active: ${activeParticipants}`);
    console.log(`   🏁 Completed: ${completedParticipants}`);
    console.log(`   ❌ Disqualified: ${disqualifiedParticipants}`);

    // Show submissions for the day
    const submitted = this.participants.filter(p => p.submissions[day] === 'submitted').length;
    const bailed = this.participants.filter(p => p.submissions[day] === 'bailed').length;
    const pending = this.participants.filter(p => p.submissions[day] === 'upcoming').length;

    console.log(`📊 Day ${day} Submissions:`);
    console.log(`   ✅ Submitted: ${submitted}`);
    console.log(`   💔 Bailed: ${bailed}`);
    console.log(`   ⏳ Pending: ${pending}`);

    // Show timezone breakdown
    console.log(`🌍 Timezone Activity:`);
    const timezones = [...new Set(this.participants.map(p => p.timezone))];
    timezones.forEach(timezone => {
      const tzParticipants = this.participants.filter(p => p.timezone === timezone);
      const tzActive = tzParticipants.filter(p => p.personalProgramStatus === 'ongoing').length;
      const tzSubmitted = tzParticipants.filter(p => p.submissions[day] === 'submitted').length;
      
      console.log(`   ${timezone.split('/')[1]}: ${tzActive} active, ${tzSubmitted} submitted`);
    });
  }

  private generateFinalReport() {
    console.log(`\n🎯 === FINAL SIMULATION REPORT ===`);
    console.log(`📅 Total Days Simulated: ${this.currentDay - 1}`);

    for (const participant of this.participants) {
      const totalDays = Object.keys(participant.submissions).length;
      const submitted = Object.values(participant.submissions).filter(s => s === 'submitted').length;
      const bailed = Object.values(participant.submissions).filter(s => s === 'bailed').length;
      const successRate = totalDays > 0 ? (submitted / totalDays * 100).toFixed(1) : '0';

      console.log(`\n👤 ${participant.fname}:`);
      console.log(`   Status: ${participant.personalProgramStatus}`);
      console.log(`   Success Rate: ${successRate}%`);
      console.log(`   Lives Left: ${participant.livesLeft}`);
      console.log(`   Submissions: ${submitted} ✅, ${bailed} 💔`);
      console.log(`   Timezone: ${participant.timezone}`);
    }

    // Overall statistics
    const overallSubmissions = this.participants.reduce((total, p) => {
      return total + Object.values(p.submissions).filter(s => s === 'submitted').length;
    }, 0);

    const overallBails = this.participants.reduce((total, p) => {
      return total + Object.values(p.submissions).filter(s => s === 'bailed').length;
    }, 0);

    const totalPossibleSubmissions = this.participants.length * (this.currentDay - 1);
    const overallSuccessRate = totalPossibleSubmissions > 0 ? 
      (overallSubmissions / totalPossibleSubmissions * 100).toFixed(1) : '0';

    console.log(`\n📈 Overall Statistics:`);
    console.log(`   Total Submissions: ${overallSubmissions}`);
    console.log(`   Total Bails: ${overallBails}`);
    console.log(`   Success Rate: ${overallSuccessRate}%`);
    console.log(`   Timezone Compliance: ✅ All calculations successful`);
  }

  async runSimulation(days: number = 7) {
    console.log('🎬 STARTING STANDALONE TIMEZONE SIMULATION');
    console.log('='.repeat(50));
    console.log(`🌍 Multi-timezone program with ${this.participants.length} participants`);
    console.log(`📅 Simulating ${days} days of scheduled job execution`);
    console.log(`⏰ Focus: Timezone compliance and calculation accuracy`);

    for (let day = 1; day <= days; day++) {
      this.currentDay = day;
      
      // Simulate timezone calculations for the day
      this.simulateTimezoneCalculations(day);
      
      // Add a small delay to make it more realistic
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Simulate end-of-day processing
      this.simulateEndOfDayProcessing(day);
      
      // Generate day report
      this.generateDayReport(day);
      
      // Add delay between days
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.generateFinalReport();
    
    console.log('\n🎉 SIMULATION COMPLETED SUCCESSFULLY!');
    console.log('✅ All timezone calculations executed without errors');
    console.log('🌍 Multi-timezone coordination working correctly');
    console.log('='.repeat(50));
  }
}

// Run simulation if this file is executed directly
if (require.main === module) {
  const simulation = new StandaloneSimulation();
  
  // Get number of days from command line argument, default to 7
  const days = parseInt(process.argv[2]) || 7;
  
  console.log(`🚀 Starting ${days}-day simulation...`);
  simulation.runSimulation(days);
}

export { StandaloneSimulation };
