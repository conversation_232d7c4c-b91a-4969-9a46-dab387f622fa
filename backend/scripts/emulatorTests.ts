/**
 * Firebase Emulator Tests for Scheduled Jobs
 * Tests cloud functions with Firebase emulator suite
 */

import * as admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firebase Admin for emulator
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'demo-project',
    credential: admin.credential.applicationDefault()
  });
}

// Connect to emulator
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

const db = getFirestore();

interface TestProgram {
  id: string;
  name: string;
  startDate: string;
  duration: number;
  participants: TestParticipant[];
}

interface TestParticipant {
  id: string;
  fname: string;
  timezone: string;
  personalStartDate: string;
  personalEndDate: string;
  programPreferences?: any;
}

async function setupTestData(): Promise<TestProgram> {
  console.log('🔧 Setting up test data in emulator...');
  
  const testProgram: TestProgram = {
    id: 'emulator-test-program',
    name: 'Emulator Test Program',
    startDate: new Date().toISOString().split('T')[0], // Today
    duration: 7,
    participants: [
      {
        id: 'emulator-user-ny',
        fname: 'John',
        timezone: 'America/New_York',
        personalStartDate: new Date().toISOString().split('T')[0],
        personalEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'hours-before',
            hoursBeforeDeadline: 3
          }
        }
      },
      {
        id: 'emulator-user-london',
        fname: 'Jane',
        timezone: 'Europe/London',
        personalStartDate: new Date().toISOString().split('T')[0],
        personalEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        programPreferences: {
          notificationTiming: {
            type: 'multiple-times',
            multipleTimes: ['09:00', '18:00']
          }
        }
      }
    ]
  };
  
  // Create program document
  await db.collection('programs').doc(testProgram.id).set({
    name: testProgram.name,
    startDate: testProgram.startDate,
    duration: testProgram.duration,
    status: 'ongoing',
    createdAt: admin.firestore.FieldValue.serverTimestamp()
  });
  
  // Create participant documents
  for (const participant of testProgram.participants) {
    await db.collection('programs').doc(testProgram.id)
      .collection('participants').doc(participant.id).set({
        fname: participant.fname,
        timezone: participant.timezone,
        personalStartDate: participant.personalStartDate,
        personalEndDate: participant.personalEndDate,
        programPreferences: participant.programPreferences,
        personalProgramStatus: 'ongoing',
        personalCurrentDay: 1,
        livesLeft: 3,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
  }
  
  console.log('✅ Test data created successfully');
  return testProgram;
}

async function testCloudFunctionTrigger(functionName: string, data: any) {
  console.log(`🔥 Testing cloud function: ${functionName}`);
  
  try {
    // Simulate cloud function trigger
    // In real testing, you'd use firebase-functions-test
    const result = await simulateCloudFunction(functionName, data);
    
    console.log(`✅ Function ${functionName} executed successfully:`, result);
    return result;
    
  } catch (error) {
    console.error(`❌ Function ${functionName} failed:`, error);
    throw error;
  }
}

async function simulateCloudFunction(functionName: string, data: any) {
  // This simulates what your cloud function would do
  // Replace with actual cloud function imports when available
  
  switch (functionName) {
    case 'dailyNotificationScheduler':
      return await simulateDailyNotificationScheduler(data);
    
    case 'individualDailyCheckup':
      return await simulateIndividualDailyCheckup(data);
    
    case 'commitDailyChecker':
      return await simulateCommitDailyChecker(data);
    
    default:
      throw new Error(`Unknown function: ${functionName}`);
  }
}

async function simulateDailyNotificationScheduler(data: any) {
  const { programId, participantId } = data;
  
  // Get participant data from emulator
  const participantDoc = await db.collection('programs').doc(programId)
    .collection('participants').doc(participantId).get();
  
  if (!participantDoc.exists) {
    throw new Error('Participant not found');
  }
  
  const participant = participantDoc.data();
  
  // Simulate notification logic
  const notificationResult = {
    success: true,
    participantId,
    timezone: participant?.timezone,
    notificationTime: new Date().toISOString(),
    message: 'Notification scheduled successfully'
  };
  
  // Log to emulator (simulates cloud function logging)
  console.log('📬 Notification scheduled:', notificationResult);
  
  return notificationResult;
}

async function simulateIndividualDailyCheckup(data: any) {
  const { programId, participantId } = data;
  
  // Get participant data
  const participantDoc = await db.collection('programs').doc(programId)
    .collection('participants').doc(participantId).get();
  
  if (!participantDoc.exists) {
    throw new Error('Participant not found');
  }
  
  const participant = participantDoc.data();
  
  // Simulate checkup logic
  const checkupResult = {
    success: true,
    participantId,
    currentDay: participant?.personalCurrentDay || 1,
    status: participant?.personalProgramStatus || 'ongoing',
    timezone: participant?.timezone,
    checkupTime: new Date().toISOString(),
    message: 'Daily checkup completed successfully'
  };
  
  console.log('🔍 Daily checkup completed:', checkupResult);
  
  return checkupResult;
}

async function simulateCommitDailyChecker(data: any) {
  const { commitId, userId } = data;
  
  // Simulate commit check logic
  const commitResult = {
    success: true,
    commitId,
    userId,
    checkTime: new Date().toISOString(),
    deadlineStatus: 'within_deadline',
    message: 'Commit check completed successfully'
  };
  
  console.log('✅ Commit check completed:', commitResult);
  
  return commitResult;
}

async function testScheduledJobsWithEmulator() {
  console.log('🧪 Testing Scheduled Jobs with Firebase Emulator');
  console.log('================================================');
  
  try {
    // Setup test data
    const testProgram = await setupTestData();
    
    // Test notification scheduler for each participant
    for (const participant of testProgram.participants) {
      console.log(`\n📬 Testing notifications for ${participant.fname} (${participant.timezone})`);
      
      await testCloudFunctionTrigger('dailyNotificationScheduler', {
        programId: testProgram.id,
        participantId: participant.id,
        timezone: participant.timezone
      });
    }
    
    // Test individual checkups
    for (const participant of testProgram.participants) {
      console.log(`\n🔍 Testing checkup for ${participant.fname} (${participant.timezone})`);
      
      await testCloudFunctionTrigger('individualDailyCheckup', {
        programId: testProgram.id,
        participantId: participant.id,
        personalStartDate: participant.personalStartDate,
        personalEndDate: participant.personalEndDate,
        timezone: participant.timezone
      });
    }
    
    // Test commit checker
    console.log(`\n✅ Testing commit checker`);
    await testCloudFunctionTrigger('commitDailyChecker', {
      commitId: 'test-commit-123',
      userId: 'emulator-user-ny',
      timezone: 'America/New_York'
    });
    
    console.log('\n✅ All emulator tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Emulator tests failed:', error);
    throw error;
  }
}

async function testTimezoneConsistency() {
  console.log('\n🌍 Testing timezone consistency across functions...');
  
  const testProgram = await setupTestData();
  const participant = testProgram.participants[0]; // NY participant
  
  // Test same participant across different functions
  const notificationResult = await testCloudFunctionTrigger('dailyNotificationScheduler', {
    programId: testProgram.id,
    participantId: participant.id,
    timezone: participant.timezone
  });
  
  const checkupResult = await testCloudFunctionTrigger('individualDailyCheckup', {
    programId: testProgram.id,
    participantId: participant.id,
    personalStartDate: participant.personalStartDate,
    personalEndDate: participant.personalEndDate,
    timezone: participant.timezone
  });
  
  // Verify both functions handled timezone consistently
  console.log('🔍 Consistency check:', {
    notificationTimezone: notificationResult.timezone,
    checkupTimezone: checkupResult.timezone,
    consistent: notificationResult.timezone === checkupResult.timezone
  });
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Delete test program and all subcollections
    const batch = db.batch();
    
    // Delete participants
    const participantsSnapshot = await db.collection('programs')
      .doc('emulator-test-program').collection('participants').get();
    
    participantsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    // Delete program
    batch.delete(db.collection('programs').doc('emulator-test-program'));
    
    await batch.commit();
    console.log('✅ Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

// Main test runner
async function runEmulatorTests() {
  try {
    await testScheduledJobsWithEmulator();
    await testTimezoneConsistency();
    
  } catch (error) {
    console.error('Emulator tests failed:', error);
    process.exit(1);
    
  } finally {
    await cleanupTestData();
  }
}

// Instructions for running
console.log(`
🚀 Firebase Emulator Testing Instructions:

1. Start Firebase Emulator Suite:
   firebase emulators:start

2. Run this test script:
   npx tsx backend/scripts/emulatorTests.ts

3. View emulator UI:
   http://localhost:4000

4. Check Firestore data:
   http://localhost:4000/firestore

5. View function logs:
   http://localhost:4000/logs
`);

// Run tests if this file is executed directly
if (require.main === module) {
  runEmulatorTests();
}

export {
  setupTestData,
  testCloudFunctionTrigger,
  testScheduledJobsWithEmulator,
  testTimezoneConsistency,
  cleanupTestData,
  runEmulatorTests
};
