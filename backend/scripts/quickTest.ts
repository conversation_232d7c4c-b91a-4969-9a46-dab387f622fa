/**
 * Quick Test - Verify timezone fixes work without Firebase dependencies
 */

import {
  isValidTimezone,
  getSafeTimezone,
  getCurrentDateInTimezone,
  convertDateToTimezone,
  extractParticipantTimezones
} from '../src/utils/timezoneUtils';

async function runQuickTest() {
  console.log('🧪 Quick Timezone Compliance Test');
  console.log('='.repeat(40));

  let passed = 0;
  let total = 0;

  function test(name: string, condition: boolean, details?: any) {
    total++;
    const emoji = condition ? '✅' : '❌';
    console.log(`${emoji} ${name}`);
    
    if (condition) {
      passed++;
    } else if (details) {
      console.log(`   Details:`, details);
    }
  }

  // Test 1: Basic timezone validation
  test(
    'Timezone validation works',
    isValidTimezone('America/New_York') && 
    isValidTimezone('Europe/London') && 
    !isValidTimezone('Invalid/Timezone')
  );

  // Test 2: Safe timezone fallback
  test(
    'Safe timezone fallback works',
    getSafeTimezone('Invalid/Timezone') === 'UTC' &&
    getSafeTimezone('America/New_York') === 'America/New_York'
  );

  // Test 3: Current date in timezone
  const nyDate = getCurrentDateInTimezone('America/New_York');
  const londonDate = getCurrentDateInTimezone('Europe/London');
  
  test(
    'Current date in timezone returns valid dates',
    nyDate instanceof Date && 
    londonDate instanceof Date &&
    nyDate.getHours() === 0 && 
    londonDate.getHours() === 0,
    {
      nyDate: nyDate.toISOString(),
      londonDate: londonDate.toISOString()
    }
  );

  // Test 4: Date conversion
  const testDate = new Date('2024-01-15T12:00:00Z');
  const convertedNY = convertDateToTimezone(testDate, 'America/New_York');
  const convertedLondon = convertDateToTimezone(testDate, 'Europe/London');
  
  test(
    'Date conversion works correctly',
    convertedNY instanceof Date && 
    convertedLondon instanceof Date &&
    convertedNY.getHours() === 0 && 
    convertedLondon.getHours() === 0,
    {
      original: testDate.toISOString(),
      convertedNY: convertedNY.toISOString(),
      convertedLondon: convertedLondon.toISOString()
    }
  );

  // Test 5: Participant timezone extraction
  const testParticipants = [
    { timezone: 'America/New_York' },
    { timezone: 'Europe/London' },
    { timezone: 'Asia/Tokyo' },
    { timezone: 'America/New_York' }, // Duplicate
    { timezone: 'Invalid/Timezone' }, // Invalid
    { timezone: '' }, // Empty
  ];

  const extractedTimezones = extractParticipantTimezones(testParticipants);
  
  test(
    'Participant timezone extraction works',
    extractedTimezones.length === 3 &&
    extractedTimezones.includes('America/New_York') &&
    extractedTimezones.includes('Europe/London') &&
    extractedTimezones.includes('Asia/Tokyo') &&
    !extractedTimezones.includes('Invalid/Timezone'),
    { extractedTimezones }
  );

  // Test 6: Error handling
  let errorHandlingWorks = true;
  
  try {
    isValidTimezone(null as any);
    getSafeTimezone(undefined as any);
    getCurrentDateInTimezone('Invalid/Timezone');
    convertDateToTimezone(new Date(), 'Invalid/Timezone');
    extractParticipantTimezones(null as any);
  } catch (error) {
    errorHandlingWorks = false;
  }
  
  test(
    'Error handling works (no crashes)',
    errorHandlingWorks
  );

  // Test 7: Performance test
  const startTime = Date.now();
  const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'UTC'];
  
  for (let i = 0; i < 100; i++) {
    timezones.forEach(timezone => {
      isValidTimezone(timezone);
      getCurrentDateInTimezone(timezone);
      convertDateToTimezone(testDate, timezone);
    });
  }
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  test(
    'Performance is acceptable (< 1 second for 1200 operations)',
    duration < 1000,
    { duration, operations: 1200 }
  );

  // Test 8: Unusual timezone offsets
  const unusualTimezones = ['Pacific/Chatham', 'Asia/Kathmandu'];
  let unusualTimezonesWork = true;
  
  unusualTimezones.forEach(timezone => {
    if (isValidTimezone(timezone)) {
      try {
        const date = getCurrentDateInTimezone(timezone);
        if (!(date instanceof Date)) {
          unusualTimezonesWork = false;
        }
      } catch (error) {
        unusualTimezonesWork = false;
      }
    }
  });
  
  test(
    'Unusual timezone offsets work',
    unusualTimezonesWork
  );

  // Test 9: Date boundaries
  const utcMidnight = new Date('2024-01-15T00:00:00Z');
  const nyMidnight = convertDateToTimezone(utcMidnight, 'America/New_York');
  const londonMidnight = convertDateToTimezone(utcMidnight, 'Europe/London');
  const tokyoMidnight = convertDateToTimezone(utcMidnight, 'Asia/Tokyo');
  
  // All should be valid dates within 1 day of each other
  const dayDiff1 = Math.abs(nyMidnight.getTime() - londonMidnight.getTime()) / (1000 * 60 * 60 * 24);
  const dayDiff2 = Math.abs(londonMidnight.getTime() - tokyoMidnight.getTime()) / (1000 * 60 * 60 * 24);
  
  test(
    'Date boundaries handled correctly',
    dayDiff1 <= 1 && dayDiff2 <= 1,
    {
      nyMidnight: nyMidnight.toISOString(),
      londonMidnight: londonMidnight.toISOString(),
      tokyoMidnight: tokyoMidnight.toISOString(),
      dayDiff1,
      dayDiff2
    }
  );

  // Test 10: Consistency across multiple calls
  const date1 = getCurrentDateInTimezone('America/New_York');
  const date2 = getCurrentDateInTimezone('America/New_York');
  const timeDiff = Math.abs(date1.getTime() - date2.getTime());
  
  test(
    'Consistency across multiple calls',
    timeDiff < 1000, // Should be within 1 second
    { timeDiff }
  );

  // Summary
  console.log('\n📊 Test Results:');
  console.log(`   Total Tests: ${total}`);
  console.log(`   Passed: ${passed} ✅`);
  console.log(`   Failed: ${total - passed} ❌`);
  console.log(`   Success Rate: ${(passed / total * 100).toFixed(1)}%`);

  if (passed === total) {
    console.log('\n🎉 All tests passed! Timezone compliance is working correctly.');
    console.log('✅ Your scheduled jobs are ready for production.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the issues above.');
    console.log('❌ Timezone compliance needs attention before production.');
  }

  console.log('\n🚀 Next Steps:');
  console.log('   1. Run full simulation: npx tsx backend/scripts/simulationRunner.ts');
  console.log('   2. Test with Firebase emulator: firebase emulators:start');
  console.log('   3. Deploy to staging for integration testing');
  console.log('   4. Monitor in production with monitoring scripts');

  return passed === total;
}

// Run the test
if (require.main === module) {
  runQuickTest().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

export { runQuickTest };
