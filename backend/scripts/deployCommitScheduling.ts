/**
 * Deployment Script for Commit Scheduling System
 * Handles gradual rollout with feature flags and monitoring
 */

import * as admin from 'firebase-admin';
import { commitSchedulingMigration, DEFAULT_MIGRATION_OPTIONS } from '../src/migrations/commitSchedulingMigration';
import { commitMonitoringService } from '../src/utils/commitMonitoring';

export interface DeploymentConfig {
  environment: 'development' | 'staging' | 'production';
  rolloutPercentage: number;
  enableFeatureFlag: boolean;
  runMigration: boolean;
  migrationOptions: {
    dryRun: boolean;
    batchSize: number;
    maxConcurrency: number;
    skipInactiveCommits: boolean;
    enableScheduling: boolean;
  };
  monitoring: {
    enableAlerts: boolean;
    healthCheckInterval: number;
    performanceThreshold: number;
  };
}

export interface DeploymentResult {
  success: boolean;
  phase: string;
  message: string;
  migrationResult?: any;
  rolloutUsers?: number;
  errors: string[];
  duration: number;
}

export class CommitSchedulingDeployment {
  private db: admin.firestore.Firestore;

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Main deployment function with phased rollout
   */
  async deployCommitScheduling(config: DeploymentConfig): Promise<DeploymentResult> {
    const startTime = Date.now();

    const result: DeploymentResult = {
      success: false,
      phase: 'initialization',
      message: '',
      errors: [],
      duration: 0
    };

    try {
      // Phase 1: Pre-deployment validation
      result.phase = 'validation';
      await this.validatePreDeployment(config);

      // Phase 2: Feature flag setup
      result.phase = 'feature_flags';
      if (config.enableFeatureFlag) {
        await this.setupFeatureFlags(config);
      }

      // Phase 3: Database migration
      result.phase = 'migration';
      if (config.runMigration) {
        const migrationResult = await commitSchedulingMigration.migrateCommitsToScheduling(
          config.migrationOptions
        );
        result.migrationResult = migrationResult;
        
        if (!migrationResult.success) {
          throw new Error(`Migration failed: ${migrationResult.errors.join(', ')}`);
        }
      }

      // Phase 4: Gradual rollout
      result.phase = 'rollout';
      const rolloutResult = await this.performGradualRollout(config);
      result.rolloutUsers = rolloutResult.affectedUsers;

      // Phase 5: Monitoring setup
      result.phase = 'monitoring';
      if (config.monitoring.enableAlerts) {
        await this.setupMonitoring(config);
      }

      // Phase 6: Health check
      result.phase = 'health_check';
      const healthCheck = await commitMonitoringService.performHealthCheck();
      if (!healthCheck.healthy) {
        console.warn('⚠️ Health check issues detected:', healthCheck.issues);
        result.errors.push(...healthCheck.issues);
      } else {
      }

      result.success = true;
      result.phase = 'completed';
      result.message = 'Deployment completed successfully';
      result.duration = Date.now() - startTime;


      // Log successful deployment
      await commitMonitoringService.createAlert({
        type: 'info',
        severity: 'low',
        title: 'Commit Scheduling Deployment Successful',
        message: `Deployment completed in ${config.environment} environment`,
        metadata: {
          config,
          result,
          rolloutUsers: result.rolloutUsers
        }
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ Deployment failed in phase ${result.phase}:`, errorMessage);

      result.success = false;
      result.message = `Deployment failed in ${result.phase}: ${errorMessage}`;
      result.errors.push(errorMessage);
      result.duration = Date.now() - startTime;

      // Log deployment failure
      await commitMonitoringService.createAlert({
        type: 'error',
        severity: 'critical',
        title: 'Commit Scheduling Deployment Failed',
        message: `Deployment failed in ${result.phase}: ${errorMessage}`,
        metadata: {
          config,
          result,
          error: errorMessage
        }
      });

      // Attempt rollback if we're past migration phase
      if (['rollout', 'monitoring', 'health_check'].includes(result.phase)) {
        await this.performRollback(config);
      }

      return result;
    }
  }

  /**
   * Validate pre-deployment requirements
   */
  private async validatePreDeployment(config: DeploymentConfig): Promise<void> {

    // Check Firebase Admin SDK initialization
    try {
      await this.db.collection('commits').limit(1).get();
    } catch (error) {
      throw new Error('Firebase Admin SDK not properly initialized');
    }

    // Check Cloud Scheduler permissions (would need actual API call)
    // For now, assume permissions are correct

    // Validate configuration
    if (config.rolloutPercentage < 0 || config.rolloutPercentage > 100) {
      throw new Error('Rollout percentage must be between 0 and 100');
    }

    if (config.environment === 'production' && config.rolloutPercentage > 50) {
      console.warn('⚠️ Production rollout > 50% detected, proceeding with caution');
    }

    // Check for existing active commits
    const activeCommitsSnapshot = await this.db
      .collection('commits')
      .where('status', '==', 'active')
      .limit(1)
      .get();

    if (activeCommitsSnapshot.empty && config.runMigration) {
      console.warn('⚠️ No active commits found for migration');
    }

  }

  /**
   * Setup feature flags for gradual rollout
   */
  private async setupFeatureFlags(config: DeploymentConfig): Promise<void> {
    const featureFlags = {
      commitSchedulingEnabled: {
        enabled: true,
        rolloutPercentage: config.rolloutPercentage,
        environment: config.environment,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        config: {
          autoVerificationEnabled: true,
          notificationsEnabled: true,
          deadlineEnforcementEnabled: true
        }
      }
    };

    await this.db.collection('feature_flags').doc('commit_scheduling').set(featureFlags);
  }

  /**
   * Perform gradual rollout to selected users
   */
  private async performGradualRollout(config: DeploymentConfig): Promise<{
    affectedUsers: number;
    selectedUsers: string[];
  }> {

    // Get all users with active commits
    const activeCommitsSnapshot = await this.db
      .collection('commits')
      .where('status', '==', 'active')
      .get();

    const userIds = [...new Set(activeCommitsSnapshot.docs.map(doc => doc.data().userId))];

    // Select users for rollout based on percentage
    const rolloutCount = Math.ceil(userIds.length * (config.rolloutPercentage / 100));
    const selectedUsers = this.selectUsersForRollout(userIds, rolloutCount, config.environment);


    // Enable scheduling for selected users
    const batch = this.db.batch();
    
    for (const userId of selectedUsers) {
      const userRef = this.db.collection('users').doc(userId);
      batch.update(userRef, {
        'features.commitSchedulingEnabled': true,
        'features.commitSchedulingRolloutDate': admin.firestore.FieldValue.serverTimestamp()
      });
    }

    await batch.commit();

    return {
      affectedUsers: selectedUsers.length,
      selectedUsers
    };
  }

  /**
   * Select users for rollout using deterministic algorithm
   */
  private selectUsersForRollout(
    userIds: string[], 
    count: number, 
    environment: string
  ): string[] {
    // Use deterministic selection based on user ID hash
    // This ensures consistent rollout across deployments
    const selectedUsers: string[] = [];
    
    for (const userId of userIds) {
      const hash = this.hashString(userId + environment);
      const rolloutScore = hash % 100;
      
      if (rolloutScore < count / userIds.length * 100) {
        selectedUsers.push(userId);
      }
      
      if (selectedUsers.length >= count) {
        break;
      }
    }

    return selectedUsers.slice(0, count);
  }

  /**
   * Setup monitoring and alerting
   */
  private async setupMonitoring(config: DeploymentConfig): Promise<void> {
    const monitoringConfig = {
      commitSchedulingMonitoring: {
        enabled: true,
        environment: config.environment,
        healthCheckInterval: config.monitoring.healthCheckInterval,
        performanceThreshold: config.monitoring.performanceThreshold,
        alertChannels: ['console', 'firestore'],
        metrics: {
          trackVerificationSuccess: true,
          trackNotificationDelivery: true,
          trackSchedulerJobHealth: true,
          trackErrorRates: true
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      }
    };

    await this.db.collection('monitoring_config').doc('commit_scheduling').set(monitoringConfig);

    // Schedule initial health check
    setTimeout(async () => {
      const healthCheck = await commitMonitoringService.performHealthCheck();
    }, 5000);
  }

  /**
   * Perform rollback in case of deployment failure
   */
  private async performRollback(config: DeploymentConfig): Promise<void> {

    try {
      // Disable feature flag
      await this.db.collection('feature_flags').doc('commit_scheduling').update({
        'commitSchedulingEnabled.enabled': false,
        'commitSchedulingEnabled.rolledBackAt': admin.firestore.FieldValue.serverTimestamp()
      });

      // Disable scheduling for all users
      const usersSnapshot = await this.db
        .collection('users')
        .where('features.commitSchedulingEnabled', '==', true)
        .get();

      const batch = this.db.batch();
      usersSnapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          'features.commitSchedulingEnabled': false,
          'features.commitSchedulingRolledBackAt': admin.firestore.FieldValue.serverTimestamp()
        });
      });

      await batch.commit();

      await commitMonitoringService.createAlert({
        type: 'warning',
        severity: 'high',
        title: 'Commit Scheduling Rollback Completed',
        message: `Automatic rollback completed for ${usersSnapshot.size} users`,
        metadata: { rollbackReason: 'deployment_failure' }
      });

    } catch (error) {
      console.error('❌ Rollback failed:', error);
      
      await commitMonitoringService.createAlert({
        type: 'error',
        severity: 'critical',
        title: 'Commit Scheduling Rollback Failed',
        message: `Automatic rollback failed: ${error}`,
        metadata: { rollbackError: error }
      });
    }
  }

  /**
   * Simple hash function for deterministic user selection
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Check deployment status
   */
  async checkDeploymentStatus(): Promise<{
    isDeployed: boolean;
    rolloutPercentage: number;
    affectedUsers: number;
    healthStatus: any;
  }> {
    try {
      // Check feature flag
      const featureFlagDoc = await this.db.collection('feature_flags').doc('commit_scheduling').get();
      const featureFlag = featureFlagDoc.data();

      if (!featureFlag?.commitSchedulingEnabled?.enabled) {
        return {
          isDeployed: false,
          rolloutPercentage: 0,
          affectedUsers: 0,
          healthStatus: null
        };
      }

      // Count affected users
      const affectedUsersSnapshot = await this.db
        .collection('users')
        .where('features.commitSchedulingEnabled', '==', true)
        .get();

      // Get health status
      const healthStatus = await commitMonitoringService.performHealthCheck();

      return {
        isDeployed: true,
        rolloutPercentage: featureFlag.commitSchedulingEnabled.rolloutPercentage || 0,
        affectedUsers: affectedUsersSnapshot.size,
        healthStatus
      };

    } catch (error) {
      console.error('Failed to check deployment status:', error);
      return {
        isDeployed: false,
        rolloutPercentage: 0,
        affectedUsers: 0,
        healthStatus: { healthy: false, issues: [String(error)] }
      };
    }
  }
}

// Export deployment instance
export const commitSchedulingDeployment = new CommitSchedulingDeployment();

// Default deployment configurations
export const DEPLOYMENT_CONFIGS = {
  DEVELOPMENT: {
    environment: 'development' as const,
    rolloutPercentage: 100,
    enableFeatureFlag: true,
    runMigration: true,
    migrationOptions: {
      ...DEFAULT_MIGRATION_OPTIONS,
      dryRun: false,
      batchSize: 10,
      maxConcurrency: 2
    },
    monitoring: {
      enableAlerts: true,
      healthCheckInterval: 300000, // 5 minutes
      performanceThreshold: 10000 // 10 seconds
    }
  },

  STAGING: {
    environment: 'staging' as const,
    rolloutPercentage: 50,
    enableFeatureFlag: true,
    runMigration: true,
    migrationOptions: {
      ...DEFAULT_MIGRATION_OPTIONS,
      dryRun: false,
      batchSize: 25,
      maxConcurrency: 3
    },
    monitoring: {
      enableAlerts: true,
      healthCheckInterval: 180000, // 3 minutes
      performanceThreshold: 15000 // 15 seconds
    }
  },

  PRODUCTION: {
    environment: 'production' as const,
    rolloutPercentage: 10, // Start with 10% rollout
    enableFeatureFlag: true,
    runMigration: true,
    migrationOptions: {
      ...DEFAULT_MIGRATION_OPTIONS,
      dryRun: false,
      batchSize: 50,
      maxConcurrency: 5
    },
    monitoring: {
      enableAlerts: true,
      healthCheckInterval: 60000, // 1 minute
      performanceThreshold: 30000 // 30 seconds
    }
  }
};
