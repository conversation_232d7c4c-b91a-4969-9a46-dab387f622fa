/**
 * Manual testing script for scheduled jobs
 * Run this to test your timezone fixes manually
 */

import { NotificationService } from '../src/services/notificationService';
import { ProgramService } from '../src/services/programService';
import { CommitService } from '../src/services/commitService';
import { CommitNotificationService } from '../src/services/commitNotificationService';

// Test data
const testProgram = {
  id: 'test-program-123',
  name: 'Test Program',
  startDate: '2024-01-01',
  duration: 30
};

const testParticipants = [
  {
    id: 'user-ny',
    fname: '<PERSON>',
    timezone: 'America/New_York',
    personalStartDate: '2024-01-01',
    personalEndDate: '2024-01-31',
    programPreferences: {
      notificationTiming: {
        type: 'hours-before',
        hoursBeforeDeadline: 3
      }
    }
  },
  {
    id: 'user-london',
    fname: 'Jane',
    timezone: 'Europe/London',
    personalStartDate: '2024-01-01',
    personalEndDate: '2024-01-31',
    programPreferences: {
      notificationTiming: {
        type: 'multiple-times',
        multipleTimes: ['09:00', '18:00']
      }
    }
  },
  {
    id: 'user-tokyo',
    fname: 'Yuki',
    timezone: 'Asia/Tokyo',
    personalStartDate: '2024-01-01',
    personalEndDate: '2024-01-31',
    programPreferences: {
      notificationTiming: {
        type: 'hours-before',
        hoursBeforeDeadline: 2
      }
    }
  }
];

const testCommit = {
  id: 'test-commit-123',
  timezone: 'America/New_York',
  schedule: {
    frequency: 'daily',
    startDate: '2024-01-01',
    endDate: '2024-01-31',
    deadline: {
      type: 'before',
      time: '23:00'
    }
  }
};

async function testNotificationService() {
  console.log('\n🔔 Testing Notification Service...');
  
  const service = new NotificationService();
  
  for (const participant of testParticipants) {
    try {
      console.log(`\n  Testing participant: ${participant.fname} (${participant.timezone})`);
      
      // Test recurring notification logic
      const result = await service.handleRecurringNotification({
        programId: testProgram.id,
        participantId: participant.id,
        personalStartDate: participant.personalStartDate,
        personalEndDate: participant.personalEndDate,
        timezone: participant.timezone
      });
      
      console.log(`    ✅ Result:`, {
        success: result.success,
        action: result.action,
        message: result.message
      });
      
      // Test hours until deadline calculation
      const hoursUntilDeadline = (service as any).calculateHoursUntilDeadline(participant.timezone);
      console.log(`    ⏰ Hours until deadline: ${hoursUntilDeadline.toFixed(2)}`);
      
      // Test date conversion
      const userDate = (service as any).getDateInTimezone(new Date(), participant.timezone);
      console.log(`    📅 Current date in timezone: ${userDate.toDateString()}`);
      
    } catch (error) {
      console.error(`    ❌ Error for ${participant.fname}:`, error.message);
    }
  }
}

async function testProgramService() {
  console.log('\n📋 Testing Program Service...');
  
  const service = new ProgramService();
  
  for (const participant of testParticipants) {
    try {
      console.log(`\n  Testing participant: ${participant.fname} (${participant.timezone})`);
      
      // Test individual checkup
      const checkupResult = await service.performIndividualDailyCheckup({
        programId: testProgram.id,
        participantId: participant.id,
        personalStartDate: participant.personalStartDate,
        personalEndDate: participant.personalEndDate,
        timezone: participant.timezone
      });
      
      console.log(`    ✅ Checkup result:`, {
        success: checkupResult.success,
        action: checkupResult.action,
        message: checkupResult.message
      });
      
      // Test program day calculation
      const programDay = (service as any).calculateParticipantProgramDay(
        testProgram.startDate,
        participant.timezone,
        testProgram.duration
      );
      
      console.log(`    📊 Program day: ${programDay.currentDay}, Status: ${programDay.status}`);
      
      // Test notification time calculation
      const notificationTimes = (service as any).calculateNotificationTimes(participant);
      console.log(`    🕐 Notification times: ${notificationTimes.join(', ')}`);
      
    } catch (error) {
      console.error(`    ❌ Error for ${participant.fname}:`, error.message);
    }
  }
}

async function testCommitService() {
  console.log('\n✅ Testing Commit Service...');
  
  const service = new CommitService();
  
  try {
    console.log(`\n  Testing commit in ${testCommit.timezone}`);
    
    // Test daily commit check
    const checkResult = await service.performDailyCommitCheck({
      commitId: testCommit.id,
      userId: 'test-user',
      timezone: testCommit.timezone,
      deadlineType: testCommit.schedule.deadline.type,
      deadlineTime: testCommit.schedule.deadline.time
    });
    
    console.log(`    ✅ Check result:`, {
      success: checkResult.success,
      action: checkResult.action,
      message: checkResult.message
    });
    
    // Test if should perform check today
    const shouldCheck = await (service as any).shouldPerformCheckToday(testCommit, {});
    console.log(`    🔍 Should perform check today: ${shouldCheck}`);
    
    // Test deadline check
    const deadlinePassed = await (service as any).hasDeadlinePassed(testCommit, {
      deadlineType: testCommit.schedule.deadline.type,
      deadlineTime: testCommit.schedule.deadline.time
    });
    console.log(`    ⏰ Deadline passed: ${deadlinePassed}`);
    
  } catch (error) {
    console.error(`    ❌ Error:`, error.message);
  }
}

async function testCommitNotificationService() {
  console.log('\n📬 Testing Commit Notification Service...');
  
  const service = new CommitNotificationService();
  
  try {
    console.log(`\n  Testing commit notifications in ${testCommit.timezone}`);
    
    // Test time until deadline calculation
    const timeUntilDeadline = (service as any).calculateTimeUntilDeadline(testCommit, testCommit.timezone);
    const hoursUntilDeadline = timeUntilDeadline / (1000 * 60 * 60);
    console.log(`    ⏰ Time until deadline: ${hoursUntilDeadline.toFixed(2)} hours`);
    
    // Test current day calculation
    const currentDay = (service as any).getCurrentDayForCommit(testCommit, testCommit.timezone);
    console.log(`    📅 Current day for commit: ${currentDay}`);
    
    // Test notification scheduling
    const scheduleResult = await service.scheduleCommitNotifications({
      commitId: testCommit.id,
      userId: 'test-user',
      timezone: testCommit.timezone,
      schedule: testCommit.schedule
    });
    
    console.log(`    ✅ Schedule result:`, {
      success: scheduleResult.success,
      message: scheduleResult.message
    });
    
  } catch (error) {
    console.error(`    ❌ Error:`, error.message);
  }
}

async function testTimezoneEdgeCases() {
  console.log('\n🌍 Testing Timezone Edge Cases...');
  
  const edgeCaseTimezones = [
    'Invalid/Timezone',
    '',
    'UTC',
    'Pacific/Chatham', // UTC+12:45
    'Asia/Kathmandu'   // UTC+5:45
  ];
  
  const service = new NotificationService();
  
  for (const timezone of edgeCaseTimezones) {
    try {
      console.log(`\n  Testing timezone: "${timezone}"`);
      
      const hoursUntilDeadline = (service as any).calculateHoursUntilDeadline(timezone);
      console.log(`    ⏰ Hours until deadline: ${hoursUntilDeadline.toFixed(2)}`);
      
      const userDate = (service as any).getDateInTimezone(new Date(), timezone);
      console.log(`    📅 Current date: ${userDate.toDateString()}`);
      
      console.log(`    ✅ Handled successfully`);
      
    } catch (error) {
      console.error(`    ❌ Error with "${timezone}":`, error.message);
    }
  }
}

async function testDSTTransitions() {
  console.log('\n🕐 Testing DST Transitions...');
  
  // Mock dates around DST transitions
  const dstDates = [
    { date: '2024-03-10', name: 'Spring Forward (US)' },
    { date: '2024-11-03', name: 'Fall Back (US)' },
    { date: '2024-03-31', name: 'Spring Forward (EU)' },
    { date: '2024-10-27', name: 'Fall Back (EU)' }
  ];
  
  const service = new NotificationService();
  
  for (const dstDate of dstDates) {
    try {
      console.log(`\n  Testing ${dstDate.name} (${dstDate.date})`);
      
      // Test with different timezones
      const timezones = ['America/New_York', 'Europe/London'];
      
      for (const timezone of timezones) {
        const testDate = new Date(dstDate.date + 'T12:00:00Z');
        const userDate = (service as any).getDateInTimezone(testDate, timezone);
        
        console.log(`    ${timezone}: ${userDate.toDateString()}`);
      }
      
    } catch (error) {
      console.error(`    ❌ Error with ${dstDate.name}:`, error.message);
    }
  }
}

// Main test runner
async function runAllTests() {
  console.log('🧪 Starting Scheduled Jobs Testing...');
  console.log('=====================================');
  
  try {
    await testNotificationService();
    await testProgramService();
    await testCommitService();
    await testCommitNotificationService();
    await testTimezoneEdgeCases();
    await testDSTTransitions();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('=====================================');
    
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

export {
  testNotificationService,
  testProgramService,
  testCommitService,
  testCommitNotificationService,
  testTimezoneEdgeCases,
  testDSTTransitions,
  runAllTests
};
