/**
 * Interactive Real-Time Simulation
 * Advanced simulation with real-time monitoring, timezone visualization, and interactive controls
 */

import { FullSimulation } from './fullSimulation';
import * as readline from 'readline';

interface SimulationMetrics {
  totalNotifications: number;
  successfulNotifications: number;
  failedNotifications: number;
  timezoneBreakdown: { [timezone: string]: { sent: number; failed: number } };
  averageResponseTime: number;
  dstTransitions: number;
}

class InteractiveSimulation extends FullSimulation {
  private metrics: SimulationMetrics;
  private rl: readline.Interface;
  private isRunning: boolean = false;
  private currentSpeed: number = 1000; // milliseconds per simulation hour
  private realTimeMode: boolean = false;

  constructor() {
    super();
    this.metrics = {
      totalNotifications: 0,
      successfulNotifications: 0,
      failedNotifications: 0,
      timezoneBreakdown: {},
      averageResponseTime: 0,
      dstTransitions: 0
    };
    
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  /**
   * Start interactive simulation with real-time controls
   */
  async startInteractiveSimulation() {
    console.clear();
    this.displayWelcome();
    
    this.setupSimulationData();
    this.setupInteractiveControls();
    
    console.log('\n🎮 Interactive controls:');
    console.log('  [SPACE] - Pause/Resume');
    console.log('  [+/-]   - Speed up/slow down');
    console.log('  [r]     - Toggle real-time mode');
    console.log('  [s]     - Show statistics');
    console.log('  [t]     - Show timezone status');
    console.log('  [q]     - Quit simulation');
    
    this.isRunning = true;
    await this.runRealTimeSimulation();
  }

  private displayWelcome() {
    console.log('🎬 ACCUSTOM SCHEDULED JOBS SIMULATION');
    console.log('=====================================');
    console.log('🌍 Multi-timezone program simulation');
    console.log('⏰ Real-time job execution testing');
    console.log('📊 Live metrics and monitoring');
    console.log('🔧 Interactive debugging tools');
  }

  private setupInteractiveControls() {
    // Enable raw mode for immediate key detection
    if (process.stdin.setRawMode) {
      process.stdin.setRawMode(true);
    }
    
    process.stdin.on('keypress', (str, key) => {
      if (key.ctrl && key.name === 'c') {
        this.cleanup();
        process.exit();
      }
      
      switch (key.name) {
        case 'space':
          this.togglePause();
          break;
        case 'plus':
        case 'equal':
          this.adjustSpeed(0.5);
          break;
        case 'minus':
          this.adjustSpeed(2);
          break;
        case 'r':
          this.toggleRealTimeMode();
          break;
        case 's':
          this.showStatistics();
          break;
        case 't':
          this.showTimezoneStatus();
          break;
        case 'q':
          this.cleanup();
          process.exit();
          break;
      }
    });
  }

  private async runRealTimeSimulation() {
    let simulationHour = 0;
    let currentDay = 1;
    
    while (this.isRunning && currentDay <= 30) {
      if (!this.realTimeMode) {
        await new Promise(resolve => setTimeout(resolve, this.currentSpeed));
      } else {
        await new Promise(resolve => setTimeout(resolve, 60000)); // 1 minute = 1 hour
      }
      
      simulationHour++;
      
      // Update display every hour
      this.updateDisplay(currentDay, simulationHour);
      
      // Run jobs at specific hours
      await this.runHourlyJobs(currentDay, simulationHour);
      
      // New day every 24 hours
      if (simulationHour >= 24) {
        await this.simulateDay(currentDay);
        currentDay++;
        simulationHour = 0;
        this.displayDayTransition(currentDay);
      }
    }
    
    this.generateReport();
    this.cleanup();
  }

  private updateDisplay(day: number, hour: number) {
    // Clear screen and show current status
    console.clear();
    
    console.log('🎬 ACCUSTOM SIMULATION - LIVE');
    console.log('='.repeat(40));
    console.log(`📅 Day ${day}, Hour ${hour}:00`);
    console.log(`⚡ Speed: ${this.realTimeMode ? 'Real-time' : `${this.currentSpeed}ms/hour`}`);
    console.log(`▶️  Status: ${this.isRunning ? 'Running' : 'Paused'}`);
    
    // Show current time in each timezone
    this.displayWorldClock(day, hour);
    
    // Show live metrics
    this.displayLiveMetrics();
    
    // Show participant status
    this.displayParticipantStatus();
    
    console.log('\n🎮 Controls: [SPACE]Pause [+/-]Speed [r]RealTime [s]Stats [t]Timezones [q]Quit');
  }

  private displayWorldClock(day: number, hour: number) {
    console.log('\n🌍 World Clock:');
    
    const baseTime = new Date();
    baseTime.setHours(hour, 0, 0, 0);
    
    const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'Australia/Sydney', 'America/Los_Angeles'];
    
    timezones.forEach(timezone => {
      try {
        const localTime = baseTime.toLocaleTimeString('en-US', {
          timeZone: timezone,
          hour12: false,
          hour: '2-digit',
          minute: '2-digit'
        });
        
        const participants = this.programs[0]?.participants.filter(p => p.timezone === timezone) || [];
        const activeCount = participants.filter(p => p.personalProgramStatus === 'ongoing').length;
        
        console.log(`  ${timezone.padEnd(20)} ${localTime} (${activeCount} active)`);
      } catch (error) {
        console.log(`  ${timezone.padEnd(20)} Invalid timezone`);
      }
    });
  }

  private displayLiveMetrics() {
    console.log('\n📊 Live Metrics:');
    console.log(`  📬 Notifications: ${this.metrics.totalNotifications} total, ${this.metrics.successfulNotifications} successful`);
    console.log(`  ✅ Success Rate: ${this.metrics.totalNotifications > 0 ? ((this.metrics.successfulNotifications / this.metrics.totalNotifications) * 100).toFixed(1) : 0}%`);
    console.log(`  ⚡ Avg Response: ${this.metrics.averageResponseTime.toFixed(0)}ms`);
    console.log(`  🕐 DST Transitions: ${this.metrics.dstTransitions}`);
  }

  private displayParticipantStatus() {
    if (this.programs.length === 0) return;
    
    console.log('\n👥 Participant Status:');
    
    this.programs[0].participants.forEach(participant => {
      const statusEmoji = {
        'ongoing': '🟢',
        'ended': '🏁',
        'disqualified': '❌'
      }[participant.personalProgramStatus] || '❓';
      
      const submissionCount = Object.values(participant.submissions).filter(s => s === 'submitted').length;
      const totalDays = Object.keys(participant.submissions).length;
      const successRate = totalDays > 0 ? ((submissionCount / totalDays) * 100).toFixed(0) : '0';
      
      console.log(`  ${statusEmoji} ${participant.fname.padEnd(8)} (${participant.timezone.split('/')[1]}) - Day ${participant.personalCurrentDay}, ${successRate}% success, ${participant.livesLeft} lives`);
    });
  }

  private async runHourlyJobs(day: number, hour: number) {
    // Simulate different jobs running at different hours
    
    if (hour === 8) {
      // Morning notifications
      await this.simulateMorningNotifications(day);
    }
    
    if (hour === 12) {
      // Midday reminders
      await this.simulateMiddayReminders(day);
    }
    
    if (hour === 20) {
      // Evening notifications
      await this.simulateEveningNotifications(day);
    }
    
    if (hour === 23) {
      // End of day processing
      await this.simulateEndOfDayProcessing(day);
    }
  }

  private async simulateMorningNotifications(day: number) {
    this.metrics.totalNotifications++;
    
    // Simulate notification processing with random success/failure
    const success = Math.random() > 0.05; // 95% success rate
    const responseTime = Math.random() * 1000 + 200; // 200-1200ms
    
    if (success) {
      this.metrics.successfulNotifications++;
    }
    
    this.metrics.averageResponseTime = (this.metrics.averageResponseTime + responseTime) / 2;
    
    // Update timezone breakdown
    const timezone = 'America/New_York'; // Example
    if (!this.metrics.timezoneBreakdown[timezone]) {
      this.metrics.timezoneBreakdown[timezone] = { sent: 0, failed: 0 };
    }
    
    if (success) {
      this.metrics.timezoneBreakdown[timezone].sent++;
    } else {
      this.metrics.timezoneBreakdown[timezone].failed++;
    }
  }

  private async simulateMiddayReminders(day: number) {
    // Simulate midday reminder logic
    this.metrics.totalNotifications++;
    this.metrics.successfulNotifications++; // Assume success for simplicity
  }

  private async simulateEveningNotifications(day: number) {
    // Simulate evening notification logic
    this.metrics.totalNotifications++;
    this.metrics.successfulNotifications++; // Assume success for simplicity
  }

  private async simulateEndOfDayProcessing(day: number) {
    // Simulate end-of-day job processing
    // This would include deadline checks, submission verification, etc.
  }

  private displayDayTransition(newDay: number) {
    console.log(`\n🌅 === DAY ${newDay} BEGINS ===`);
    
    // Show day transition summary
    if (this.programs.length > 0) {
      const activeParticipants = this.programs[0].participants.filter(p => p.personalProgramStatus === 'ongoing').length;
      const completedParticipants = this.programs[0].participants.filter(p => p.personalProgramStatus === 'ended').length;
      const disqualifiedParticipants = this.programs[0].participants.filter(p => p.personalProgramStatus === 'disqualified').length;
      
      console.log(`👥 Participants: ${activeParticipants} active, ${completedParticipants} completed, ${disqualifiedParticipants} disqualified`);
    }
  }

  private togglePause() {
    this.isRunning = !this.isRunning;
    console.log(`\n${this.isRunning ? '▶️  Resumed' : '⏸️  Paused'} simulation`);
  }

  private adjustSpeed(factor: number) {
    this.currentSpeed = Math.max(100, Math.min(5000, this.currentSpeed * factor));
    console.log(`\n⚡ Speed adjusted to ${this.currentSpeed}ms per hour`);
  }

  private toggleRealTimeMode() {
    this.realTimeMode = !this.realTimeMode;
    console.log(`\n🕐 ${this.realTimeMode ? 'Enabled' : 'Disabled'} real-time mode`);
  }

  private showStatistics() {
    console.clear();
    console.log('📊 DETAILED STATISTICS');
    console.log('='.repeat(30));
    
    console.log('\n📬 Notification Metrics:');
    console.log(`  Total Sent: ${this.metrics.totalNotifications}`);
    console.log(`  Successful: ${this.metrics.successfulNotifications}`);
    console.log(`  Failed: ${this.metrics.failedNotifications}`);
    console.log(`  Success Rate: ${this.metrics.totalNotifications > 0 ? ((this.metrics.successfulNotifications / this.metrics.totalNotifications) * 100).toFixed(2) : 0}%`);
    console.log(`  Avg Response Time: ${this.metrics.averageResponseTime.toFixed(0)}ms`);
    
    console.log('\n🌍 Timezone Breakdown:');
    Object.keys(this.metrics.timezoneBreakdown).forEach(timezone => {
      const stats = this.metrics.timezoneBreakdown[timezone];
      const total = stats.sent + stats.failed;
      const successRate = total > 0 ? ((stats.sent / total) * 100).toFixed(1) : '0';
      console.log(`  ${timezone}: ${stats.sent} sent, ${stats.failed} failed (${successRate}% success)`);
    });
    
    console.log('\nPress any key to return to simulation...');
    
    this.rl.question('', () => {
      // Return to main simulation display
    });
  }

  private showTimezoneStatus() {
    console.clear();
    console.log('🌍 TIMEZONE STATUS');
    console.log('='.repeat(25));
    
    if (this.programs.length > 0) {
      this.programs[0].participants.forEach(participant => {
        console.log(`\n👤 ${participant.fname} (${participant.timezone}):`);
        
        // Show current local time
        const now = new Date();
        const localTime = now.toLocaleString('en-US', {
          timeZone: participant.timezone,
          weekday: 'short',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        });
        
        console.log(`  🕐 Local Time: ${localTime}`);
        console.log(`  📊 Status: ${participant.personalProgramStatus}`);
        console.log(`  📅 Day: ${participant.personalCurrentDay}`);
        console.log(`  💔 Lives: ${participant.livesLeft}`);
        
        // Show notification preferences
        const prefs = participant.programPreferences?.notificationTiming;
        if (prefs) {
          console.log(`  🔔 Notifications: ${prefs.type}`);
          if (prefs.multipleTimes) {
            console.log(`    Times: ${prefs.multipleTimes.join(', ')}`);
          }
          if (prefs.hoursBeforeDeadline) {
            console.log(`    Hours before deadline: ${prefs.hoursBeforeDeadline}`);
          }
        }
      });
    }
    
    console.log('\nPress any key to return to simulation...');
    
    this.rl.question('', () => {
      // Return to main simulation display
    });
  }

  private cleanup() {
    if (process.stdin.setRawMode) {
      process.stdin.setRawMode(false);
    }
    this.rl.close();
  }
}

// Run interactive simulation if this file is executed directly
if (require.main === module) {
  const simulation = new InteractiveSimulation();
  
  console.log('🎮 Starting Interactive Simulation...');
  console.log('Press Ctrl+C to exit at any time');
  
  simulation.startInteractiveSimulation().catch(error => {
    console.error('Simulation error:', error);
    process.exit(1);
  });
}

export { InteractiveSimulation };
