#!/usr/bin/env node

/**
 * CLI Script for Commit Scheduling Deployment
 * Usage: node deploy-commit-scheduling.js [environment] [options]
 */

const admin = require('firebase-admin');
const { program } = require('commander');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    projectId: process.env.FIREBASE_PROJECT_ID || 'habit-royale-dev'
  });
}

// Import deployment modules (would need to be compiled from TypeScript)
const { commitSchedulingDeployment, DEPLOYMENT_CONFIGS } = require('../dist/scripts/deployCommitScheduling');
const { commitSchedulingMigration } = require('../dist/src/migrations/commitSchedulingMigration');

program
  .name('deploy-commit-scheduling')
  .description('Deploy the Commit Scheduling System')
  .version('1.0.0');

program
  .command('deploy')
  .description('Deploy commit scheduling system')
  .argument('<environment>', 'deployment environment (development|staging|production)')
  .option('-r, --rollout <percentage>', 'rollout percentage (0-100)', '10')
  .option('--dry-run', 'perform dry run without making changes')
  .option('--skip-migration', 'skip database migration')
  .option('--skip-monitoring', 'skip monitoring setup')
  .option('-b, --batch-size <size>', 'migration batch size', '50')
  .option('-c, --concurrency <count>', 'max concurrent operations', '5')
  .action(async (environment, options) => {
    try {
      
      if (options.dryRun) {
      }

      // Get base config for environment
      const baseConfig = DEPLOYMENT_CONFIGS[environment.toUpperCase()];
      if (!baseConfig) {
        throw new Error(`Invalid environment: ${environment}. Use development, staging, or production.`);
      }

      // Override with CLI options
      const deploymentConfig = {
        ...baseConfig,
        rolloutPercentage: parseInt(options.rollout),
        runMigration: !options.skipMigration,
        migrationOptions: {
          ...baseConfig.migrationOptions,
          dryRun: options.dryRun,
          batchSize: parseInt(options.batchSize),
          maxConcurrency: parseInt(options.concurrency)
        },
        monitoring: {
          ...baseConfig.monitoring,
          enableAlerts: !options.skipMonitoring
        }
      };

      // Confirm production deployment
      if (environment === 'production' && !options.dryRun) {
        const readline = require('readline').createInterface({
          input: process.stdin,
          output: process.stdout
        });

        const answer = await new Promise(resolve => {
          readline.question(
            `⚠️  You are about to deploy to PRODUCTION with ${options.rollout}% rollout. Continue? (yes/no): `,
            resolve
          );
        });

        readline.close();

        if (answer.toLowerCase() !== 'yes') {
          process.exit(0);
        }
      }

      // Run deployment
      const result = await commitSchedulingDeployment.deployCommitScheduling(deploymentConfig);

      if (result.success) {
        process.exit(0);
      } else {
        console.error('❌ Deployment failed:', result.message);
        if (result.errors.length > 0) {
          console.error('Errors:', result.errors);
        }
        process.exit(1);
      }

    } catch (error) {
      console.error('❌ Deployment error:', error.message);
      process.exit(1);
    }
  });

program
  .command('status')
  .description('Check deployment status')
  .action(async () => {
    try {
      
      const status = await commitSchedulingDeployment.checkDeploymentStatus();
      
      
      if (status.healthStatus?.issues?.length > 0) {
      }

    } catch (error) {
      console.error('❌ Status check error:', error.message);
      process.exit(1);
    }
  });

program
  .command('rollback')
  .description('Rollback deployment')
  .option('--confirm', 'confirm rollback without prompt')
  .action(async (options) => {
    try {
      if (!options.confirm) {
        const readline = require('readline').createInterface({
          input: process.stdin,
          output: process.stdout
        });

        const answer = await new Promise(resolve => {
          readline.question('⚠️  Are you sure you want to rollback the deployment? (yes/no): ', resolve);
        });

        readline.close();

        if (answer.toLowerCase() !== 'yes') {
          process.exit(0);
        }
      }

      
      // Disable feature flag
      await admin.firestore().collection('feature_flags').doc('commit_scheduling').update({
        'commitSchedulingEnabled.enabled': false,
        'commitSchedulingEnabled.rolledBackAt': admin.firestore.FieldValue.serverTimestamp()
      });


    } catch (error) {
      console.error('❌ Rollback error:', error.message);
      process.exit(1);
    }
  });

program
  .command('migrate')
  .description('Run database migration only')
  .option('--dry-run', 'perform dry run without making changes')
  .option('-b, --batch-size <size>', 'migration batch size', '50')
  .option('-c, --concurrency <count>', 'max concurrent operations', '5')
  .option('--skip-inactive', 'skip inactive commits', true)
  .action(async (options) => {
    try {
      
      const migrationOptions = {
        dryRun: options.dryRun,
        batchSize: parseInt(options.batchSize),
        maxConcurrency: parseInt(options.concurrency),
        skipInactiveCommits: options.skipInactive,
        enableScheduling: !options.dryRun
      };

      const result = await commitSchedulingMigration.migrateCommitsToScheduling(migrationOptions);

      if (result.success) {
      } else {
        console.error('❌ Migration failed');
        if (result.errors.length > 0) {
          console.error('Errors:', result.errors);
        }
        process.exit(1);
      }

    } catch (error) {
      console.error('❌ Migration error:', error.message);
      process.exit(1);
    }
  });

program
  .command('validate')
  .description('Validate migration results')
  .argument('[commit-ids...]', 'specific commit IDs to validate (optional)')
  .action(async (commitIds) => {
    try {
      
      let idsToValidate = commitIds;
      
      if (!idsToValidate || idsToValidate.length === 0) {
        // Get all migrated commits
        const snapshot = await admin.firestore()
          .collection('commits')
          .where('scheduling.enabled', '==', true)
          .get();
        
        idsToValidate = snapshot.docs.map(doc => doc.id);
      }

      const validation = await commitSchedulingMigration.validateMigration(idsToValidate);
      
      
      if (validation.issues.length > 0) {
      }

    } catch (error) {
      console.error('❌ Validation error:', error.message);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();

// Show help if no command provided
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
