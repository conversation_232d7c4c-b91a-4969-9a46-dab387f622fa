#!/usr/bin/env node

/**
 * Test script to send a branded email and verify the new styling
 * Run this script to test the updated email templates with ACCUSTOM branding
 */

const { https } = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

async function testEmailBranding() {
  console.log('🧪 Testing ACCUSTOM Email Branding...');
  
  try {
    // You can replace this with your email address
    const testEmail = '<EMAIL>'; // Change this to your email
    
    console.log(`📧 Sending test email to: ${testEmail}`);
    
    // Call the sendTestEmailToAdmin function
    const testEmailFunction = require('../lib/testEmail').sendTestEmailToAdmin;
    
    const result = await testEmailFunction({
      data: { adminEmail: testEmail }
    });
    
    if (result.success) {
      console.log('✅ Test email sent successfully!');
      console.log(`📬 Message ID: ${result.messageId}`);
      console.log(`⏰ Timestamp: ${result.timestamp}`);
      console.log('\n🎨 The email should now feature:');
      console.log('   • Gold color scheme (#D4AF37, #FFD700)');
      console.log('   • Montserrat typography');
      console.log('   • ACCUSTOM branding (uppercase)');
      console.log('   • Logo placeholder in header');
      console.log('   • Luxury styling matching your app');
      console.log('\n📱 Check your email to verify the new branding!');
    } else {
      console.error('❌ Failed to send test email:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Error testing email branding:', error.message);
    console.log('\n🔧 Make sure you have:');
    console.log('   • Deployed the updated email templates');
    console.log('   • Valid SendGrid API key configured');
    console.log('   • Correct email address specified');
  }
}

// Run the test
testEmailBranding();
