{"name": "accustom-functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "deploy:commit-scheduling": "npm run build && node scripts/deploy-commit-scheduling.js", "deploy:commit-scheduling:dev": "npm run build && node scripts/deploy-commit-scheduling.js deploy development", "deploy:commit-scheduling:staging": "npm run build && node scripts/deploy-commit-scheduling.js deploy staging", "deploy:commit-scheduling:prod": "npm run build && node scripts/deploy-commit-scheduling.js deploy production", "migrate:commits": "npm run build && node scripts/deploy-commit-scheduling.js migrate", "migrate:commits:dry-run": "npm run build && node scripts/deploy-commit-scheduling.js migrate --dry-run", "status:commit-scheduling": "npm run build && node scripts/deploy-commit-scheduling.js status", "rollback:commit-scheduling": "npm run build && node scripts/deploy-commit-scheduling.js rollback", "validate:commit-migration": "npm run build && node scripts/deploy-commit-scheduling.js validate", "test:commit-scheduling": "jest --testPathPattern=commitScheduler", "test:commit-scheduling:watch": "jest --testPathPattern=commitScheduler --watch", "test:timezone": "jest --testPathPattern=timezone", "test:timezone:watch": "jest --testPathPattern=timezone --watch", "test:all": "jest", "test:watch": "jest --watch"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@google-cloud/scheduler": "^5.3.0", "@sendgrid/mail": "^8.1.5", "@types/node-fetch": "^2.6.12", "dotenv": "^17.2.1", "firebase-admin": "^12.0.0", "firebase-functions": "^6.4.0", "firebase-tools": "^14.11.1", "node-fetch": "^3.3.2", "twilio": "^5.8.0"}, "devDependencies": {"@types/node": "^18.19.117", "typescript": "^4.9.5"}, "private": true}