#!/bin/bash

# Setup script for Accustom Email System
# This script configures the SendGrid API key and email settings

echo "🔧 Setting up Accustom Email System..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created"
else
    echo "✅ .env file already exists"
fi

# Set Firebase Functions configuration (fallback)
echo "🔧 Setting Firebase Functions configuration..."

# Try to set the configuration (may fail if Runtime Config has issues)
npx firebase functions:config:set \
  sendgrid.api_key="*********************************************************************" \
  email.from_email="<EMAIL>" \
  email.from_name="Accustom" \
  email.reply_to_email="<EMAIL>" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Firebase Functions configuration set successfully"
else
    echo "⚠️  Firebase Functions config failed (using .env file instead)"
fi

echo ""
echo "📧 Email System Configuration:"
echo "   SendGrid API Key: SG.aasuB2yiQV-1hnvGfsxP9w..."
echo "   From Email: <EMAIL>"
echo "   From Name: Accustom"
echo "   Reply To: <EMAIL>"
echo ""
echo "🚀 Email system is configured and ready!"
echo ""
echo "Next steps:"
echo "1. Build functions: npm run build"
echo "2. Deploy functions: npm run deploy"
echo "3. Test email system from your app"
echo ""
