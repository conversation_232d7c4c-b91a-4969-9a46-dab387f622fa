# Commit Scheduling System Deployment Guide

This guide covers the deployment of the comprehensive commit scheduling system, including migration, rollout strategies, and monitoring.

## 🏗️ System Overview

The Commit Scheduling System provides:
- **Automated deadline enforcement** with timezone awareness
- **Smart notifications** (reminders, urgent alerts, verification results)
- **Auto-verification** for GitHub and Strava integrations
- **Flexible scheduling** (daily, weekly, monthly, one-time)
- **Comprehensive monitoring** and error handling

## 📋 Pre-Deployment Checklist

### Prerequisites
- [ ] Firebase Admin SDK configured
- [ ] Google Cloud Scheduler API enabled
- [ ] Firebase Cloud Functions deployed
- [ ] Database permissions verified
- [ ] Monitoring systems ready

### Environment Setup
```bash
# Set environment variables
export FIREBASE_PROJECT_ID="your-project-id"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account.json"

# Install dependencies
npm install

# Build TypeScript
npm run build
```

## 🚀 Deployment Process

### Phase 1: Development Deployment

```bash
# Full deployment with 100% rollout
node scripts/deploy-commit-scheduling.js deploy development

# Dry run first (recommended)
node scripts/deploy-commit-scheduling.js deploy development --dry-run

# Custom rollout percentage
node scripts/deploy-commit-scheduling.js deploy development -r 50
```

### Phase 2: Staging Deployment

```bash
# Staging deployment with 50% rollout
node scripts/deploy-commit-scheduling.js deploy staging

# Skip migration if already done
node scripts/deploy-commit-scheduling.js deploy staging --skip-migration

# Custom batch size for migration
node scripts/deploy-commit-scheduling.js deploy staging -b 25 -c 3
```

### Phase 3: Production Deployment

```bash
# Conservative production rollout (10%)
node scripts/deploy-commit-scheduling.js deploy production

# Gradual increase (requires multiple deployments)
node scripts/deploy-commit-scheduling.js deploy production -r 25
node scripts/deploy-commit-scheduling.js deploy production -r 50
node scripts/deploy-commit-scheduling.js deploy production -r 100
```

## 📊 Migration Process

### Database Migration

The migration process handles:
- Converting existing commits to new scheduling format
- Setting up scheduler jobs for active commits
- Preserving existing user data and preferences
- Validating migration results

```bash
# Run migration only
node scripts/deploy-commit-scheduling.js migrate

# Dry run to see what would be migrated
node scripts/deploy-commit-scheduling.js migrate --dry-run

# Custom batch processing
node scripts/deploy-commit-scheduling.js migrate -b 100 -c 10
```

### Migration Validation

```bash
# Validate all migrated commits
node scripts/deploy-commit-scheduling.js validate

# Validate specific commits
node scripts/deploy-commit-scheduling.js validate commit-123 commit-456
```

## 🔍 Monitoring and Health Checks

### Deployment Status

```bash
# Check current deployment status
node scripts/deploy-commit-scheduling.js status
```

Expected output:
```
📊 Checking deployment status...
🚀 Deployed: Yes
📈 Rollout: 25%
👥 Affected Users: 150
🏥 Health: Healthy
```

### Health Monitoring

The system includes comprehensive monitoring:

- **Performance Metrics**: Function execution times, success rates
- **Error Tracking**: Automatic error categorization and alerting
- **Circuit Breakers**: Prevent cascade failures
- **Retry Logic**: Exponential backoff for transient failures

### Key Metrics to Monitor

1. **Scheduler Job Success Rate** (target: >99%)
2. **Verification Success Rate** (target: >95%)
3. **Notification Delivery Rate** (target: >98%)
4. **Average Response Time** (target: <5s)
5. **Error Rate** (target: <1%)

## 🔄 Rollback Procedures

### Automatic Rollback

The system automatically rolls back if:
- Health check fails during deployment
- Error rate exceeds 10%
- Critical alerts are triggered

### Manual Rollback

```bash
# Immediate rollback
node scripts/deploy-commit-scheduling.js rollback --confirm

# Interactive rollback (with confirmation)
node scripts/deploy-commit-scheduling.js rollback
```

### Rollback Process

1. Disable feature flags
2. Stop scheduler job creation
3. Preserve existing data
4. Send rollback notifications
5. Update monitoring dashboards

## 🏁 Feature Flags

The system uses feature flags for gradual rollout:

```javascript
// Check if commit scheduling is enabled for user
const isEnabled = await checkFeatureFlag('commitSchedulingEnabled', userId);

if (isEnabled) {
  // Use new scheduling system
  await setupCommitScheduling(config);
} else {
  // Use legacy system
  await setupLegacyCommit(config);
}
```

### Feature Flag Configuration

```json
{
  "commitSchedulingEnabled": {
    "enabled": true,
    "rolloutPercentage": 25,
    "environment": "production",
    "config": {
      "autoVerificationEnabled": true,
      "notificationsEnabled": true,
      "deadlineEnforcementEnabled": true
    }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Scheduler Job Creation Fails
```bash
# Check Cloud Scheduler permissions
gcloud scheduler jobs list --location=us-central1

# Verify service account permissions
gcloud projects get-iam-policy PROJECT_ID
```

#### 2. Migration Stalls
```bash
# Check migration progress
node scripts/deploy-commit-scheduling.js status

# Resume with smaller batch size
node scripts/deploy-commit-scheduling.js migrate -b 10 -c 2
```

#### 3. High Error Rate
```bash
# Check recent alerts
# (View in Firebase Console or monitoring dashboard)

# Reduce rollout percentage
node scripts/deploy-commit-scheduling.js deploy production -r 5
```

### Error Recovery

1. **Transient Errors**: Automatic retry with exponential backoff
2. **Rate Limiting**: Circuit breaker activation and delayed retry
3. **Permission Errors**: Alert administrators for manual intervention
4. **Data Corruption**: Automatic rollback and data validation

## 📈 Performance Optimization

### Batch Processing
- Default batch size: 50 commits
- Max concurrency: 5 operations
- Adjustable based on system load

### Caching Strategy
- Cache user timezone data
- Cache feature flag status
- Cache verification results (5 minutes)

### Resource Management
- Connection pooling for database operations
- Rate limiting for external API calls
- Memory optimization for large migrations

## 🔐 Security Considerations

### Access Control
- Service account with minimal required permissions
- Encrypted communication for all external APIs
- Audit logging for all administrative operations

### Data Protection
- No sensitive data in logs
- Encrypted storage for verification tokens
- Secure handling of user notification preferences

## 📚 Additional Resources

### Documentation
- [Commit Scheduling Architecture](./COMMIT_SCHEDULING_ARCHITECTURE.md)
- [API Reference](./COMMIT_SCHEDULING_API.md)
- [Monitoring Guide](./COMMIT_SCHEDULING_MONITORING.md)

### Support
- Check system health: `node scripts/deploy-commit-scheduling.js status`
- View recent alerts in Firebase Console
- Contact development team for critical issues

### Testing
- Unit tests: `npm test -- --testPathPattern=commitScheduler`
- Integration tests: `npm run test:integration`
- Load testing: `npm run test:load`

## 🎯 Success Criteria

Deployment is considered successful when:
- [ ] Migration completes with <1% failure rate
- [ ] Health checks pass consistently
- [ ] Error rate remains <1%
- [ ] User satisfaction metrics maintained
- [ ] No critical alerts for 24 hours post-deployment

## 📞 Emergency Contacts

- **Development Team**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **On-Call Engineer**: +1-XXX-XXX-XXXX

---

**Last Updated**: 2024-01-26
**Version**: 1.0.0
**Environment**: All
