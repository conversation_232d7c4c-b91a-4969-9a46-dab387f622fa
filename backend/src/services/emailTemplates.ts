/**
 * Email Templates for Accustom Notifications
 * Provides HTML and text templates for different types of email notifications
 */

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface TemplateData {
  userName: string;
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  programName?: string;
  dayNumber?: number;
  streakCount?: number;
  pointsEarned?: number;
  [key: string]: any;
}

export class EmailTemplates {
  
  /**
   * Base HTML template with Accustom branding - matches app's luxury gold theme
   */
  private static getBaseTemplate(): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{{SUBJECT}}</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap');

          body {
            font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #1A1A1A;
            margin: 0;
            padding: 0;
            background-color: #F0F0F2;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #FFFFFF;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(212, 175, 55, 0.15);
          }
          .header {
            background: linear-gradient(135deg, #1A1A1A 0%, #2D2D2D 100%);
            color: #FFFFFF;
            padding: 40px 30px;
            text-align: center;
            position: relative;
          }
          .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: 3px;
            font-family: 'Montserrat', sans-serif;
            text-transform: uppercase;
            color: #D4AF37;
          }
          .header .tagline {
            margin: 8px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 400;
            letter-spacing: 1px;
            color: #FFFFFF;
            text-transform: uppercase;
          }
          .content {
            padding: 40px 30px;
          }
          .content h2 {
            color: #1A1A1A;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
            line-height: 1.3;
            font-family: 'Montserrat', sans-serif;
          }
          .content p {
            margin: 0 0 16px 0;
            font-size: 16px;
            line-height: 1.6;
            color: #2D2D2D;
          }
          .button {
            display: inline-block;
            background: #D4AF37;
            color: #FFFFFF;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            letter-spacing: 0.5px;
            font-family: 'Montserrat', sans-serif;
            margin: 20px 0;
            transition: all 0.2s ease;
            border: 1px solid #D4AF37;
          }
          .button:hover {
            background: #B8941F;
            border-color: #B8941F;
          }
          .stats {
            background: #FAFAFA;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
            padding: 20px;
            margin: 24px 0;
            text-align: center;
          }
          .stats .stat {
            display: inline-block;
            margin: 0 20px;
          }
          .stats .stat-number {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #D4AF37;
            font-family: 'Montserrat', sans-serif;
          }
          .stats .stat-label {
            font-size: 14px;
            color: #6B7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
          }
          .footer {
            background: #FAFAFA;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #E5E7EB;
          }
          .footer p {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #6B7280;
          }
          .footer a {
            color: #D4AF37;
            text-decoration: none;
            font-weight: 600;
          }
          .footer a:hover {
            text-decoration: underline;
          }
          .emoji {
            font-size: 24px;
            margin-right: 8px;
          }
          .brand-highlight {
            color: #D4AF37;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ACCUSTOM</h1>
            <p class="tagline">Premium Habit Building</p>
          </div>
          <div class="content">
            {{CONTENT}}
          </div>
          <div class="footer">
            <p>You're receiving this email because you have notifications enabled in your <span class="brand-highlight">Accustom</span> account.</p>
            <p><a href="{{UNSUBSCRIBE_URL}}">Manage your notification preferences</a></p>
            <p style="margin-top: 16px; font-size: 12px;">© 2024 <span class="brand-highlight">Accustom</span>. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Welcome email template
   */
  static getWelcomeTemplate(data: TemplateData): EmailTemplate {
    const subject = `Welcome to Accustom, ${data.userName}`;

    const content = `
      <h2>Welcome to <span class="brand-highlight">Accustom</span>, ${data.userName}</h2>
      <p>You've joined a community dedicated to meaningful change through consistent action.</p>
      <p><span class="brand-highlight">Accustom</span> provides the structure and accountability to transform intentions into lasting habits.</p>
      <div style="margin: 24px 0; padding: 20px; background: #FAFAFA; border-left: 4px solid #D4AF37; border-radius: 4px;">
        <p style="margin: 0 0 12px 0; font-weight: 600; color: #1A1A1A;">Your journey includes:</p>
        <ul style="margin: 0; padding-left: 20px; color: #2D2D2D;">
          <li style="margin-bottom: 6px;">Daily progress tracking with streak building</li>
          <li style="margin-bottom: 6px;">Intelligent reminders that respect your schedule</li>
          <li style="margin-bottom: 6px;">Progress recognition and milestone celebrations</li>
        </ul>
      </div>
      <p style="text-align: center;">
        <a href="${data.actionUrl || '#'}" class="button">Begin Your First Program</a>
      </p>
      <p>Questions? Our support team is here to help.</p>
      <p>Best regards,<br><strong>The Accustom Team</strong></p>
    `;

    const html = this.getBaseTemplate()
      .replace('{{SUBJECT}}', subject)
      .replace('{{CONTENT}}', content)
      .replace('{{UNSUBSCRIBE_URL}}', data.actionUrl || '#');

    const text = `Welcome to ACCUSTOM, ${data.userName}!

We're thrilled to have you join our community of goal-achievers and habit-builders!

ACCUSTOM is designed to help you build lasting habits through accountability, progress tracking, and community support.

Get started: ${data.actionUrl || 'Open the ACCUSTOM app'}

Best regards,
The ACCUSTOM Team`;

    return { subject, html, text };
  }

  /**
   * Daily reminder email template
   */
  static getReminderTemplate(data: TemplateData): EmailTemplate {
    const subject = `${data.programName || 'Your Program'} - Day ${data.dayNumber || ''} Progress`;
    
    const content = `
      <h2>Daily Progress Check-in</h2>
      <p>Hello ${data.userName},</p>
      <p>Time to log your progress for <strong style="color: #D4AF37;">"${data.programName}"</strong>.</p>
      ${data.dayNumber ? `<p>Day <strong style="color: #D4AF37;">${data.dayNumber}</strong> awaits your attention.</p>` : ''}
      ${data.streakCount ? `
        <div class="stats">
          <div class="stat">
            <span class="stat-number">${data.streakCount}</span>
            <span class="stat-label">Day Streak</span>
          </div>
        </div>
      ` : ''}
      <p style="text-align: center;">
        <a href="${data.actionUrl || '#'}" class="button">Log Today's Progress</a>
      </p>
      <p>Consistency builds momentum. Your commitment today shapes tomorrow's success.</p>
      <p>Best regards,<br><strong>The <span class="brand-highlight">Accustom</span> Team</strong></p>
    `;

    const html = this.getBaseTemplate()
      .replace('{{SUBJECT}}', subject)
      .replace('{{CONTENT}}', content)
      .replace('{{UNSUBSCRIBE_URL}}', data.actionUrl || '#');

    const text = `Daily Reminder: ${data.programName || 'Your Program'}

Hi ${data.userName},

This is your friendly reminder to submit your progress for "${data.programName}" today!

${data.dayNumber ? `You're on Day ${data.dayNumber} of your program. Keep up the great work!` : ''}
${data.streakCount ? `Current streak: ${data.streakCount} days` : ''}

Submit your progress: ${data.actionUrl || 'Open the ACCUSTOM app'}

Remember: Consistency is key to building lasting habits. Even small progress counts!

Best regards,
The ACCUSTOM Team`;

    return { subject, html, text };
  }

  /**
   * Program alert email template
   */
  static getProgramAlertTemplate(data: TemplateData): EmailTemplate {
    const subject = `🏆 ${data.title}`;
    
    const content = `
      <h2><span class="emoji">🏆</span>${data.title}</h2>
      <p>Hi ${data.userName},</p>
      <p>${data.message}</p>
      ${data.pointsEarned ? `
        <div class="stats">
          <div class="stat">
            <span class="stat-number">+${data.pointsEarned}</span>
            <span class="stat-label">Points Earned</span>
          </div>
        </div>
      ` : ''}
      <p style="text-align: center;">
        <a href="${data.actionUrl || '#'}" class="button">${data.actionText || 'View Program'}</a>
      </p>
      <p>Keep up the excellent work!</p>
      <p>Best regards,<br><strong>The <span class="brand-highlight">ACCUSTOM</span> Team</strong></p>
    `;

    const html = this.getBaseTemplate()
      .replace('{{SUBJECT}}', subject)
      .replace('{{CONTENT}}', content)
      .replace('{{UNSUBSCRIBE_URL}}', data.actionUrl || '#');

    const text = `${data.title}

Hi ${data.userName},

${data.message}

${data.pointsEarned ? `Points earned: +${data.pointsEarned}` : ''}

${data.actionText || 'View Program'}: ${data.actionUrl || 'Open the ACCUSTOM app'}

Keep up the excellent work!

Best regards,
The ACCUSTOM Team`;

    return { subject, html, text };
  }



  /**
   * Generic notification template
   */
  static getGenericTemplate(data: TemplateData): EmailTemplate {
    const subject = data.title;
    
    const content = `
      <h2>${data.title}</h2>
      <p>Hi ${data.userName},</p>
      <p>${data.message}</p>
      ${data.actionUrl ? `
        <p style="text-align: center;">
          <a href="${data.actionUrl}" class="button">${data.actionText || 'Open ACCUSTOM'}</a>
        </p>
      ` : ''}
      <p>Best regards,<br><strong>The <span class="brand-highlight">ACCUSTOM</span> Team</strong></p>
    `;

    const html = this.getBaseTemplate()
      .replace('{{SUBJECT}}', subject)
      .replace('{{CONTENT}}', content)
      .replace('{{UNSUBSCRIBE_URL}}', data.actionUrl || '#');

    const text = `${data.title}

Hi ${data.userName},

${data.message}

${data.actionUrl ? `${data.actionText || 'Open ACCUSTOM'}: ${data.actionUrl}` : ''}

Best regards,
The ACCUSTOM Team`;

    return { subject, html, text };
  }

  /**
   * Test email template
   */
  static getTestTemplate(data: TemplateData): EmailTemplate {
    const subject = `Test Email from ACCUSTOM`;

    const content = `
      <h2>Email Branding Test</h2>
      <p>Hi ${data.userName},</p>
      <p>${data.message}</p>
      <div style="margin: 24px 0; padding: 20px; background: #FAFAFA; border-left: 4px solid #D4AF37; border-radius: 4px;">
        <p style="margin: 0 0 12px 0; font-weight: 600; color: #1A1A1A;">Updated email features:</p>
        <ul style="margin: 0; padding-left: 20px; color: #2D2D2D;">
          <li style="margin-bottom: 6px;">Clean, professional design</li>
          <li style="margin-bottom: 6px;">Subtle gold accents</li>
          <li style="margin-bottom: 6px;">Montserrat typography</li>
          <li style="margin-bottom: 6px;">Consistent with app branding</li>
        </ul>
      </div>
      <p style="text-align: center;">
        <a href="${data.actionUrl || '#'}" class="button">Open Accustom</a>
      </p>
      <p>The email system now features elegant, professional styling that aligns with your app.</p>
      <p>Best regards,<br><strong>The Accustom Team</strong></p>
    `;

    const html = this.getBaseTemplate()
      .replace('{{SUBJECT}}', subject)
      .replace('{{CONTENT}}', content)
      .replace('{{UNSUBSCRIBE_URL}}', data.actionUrl || '#');

    const text = `Test Email from ACCUSTOM

Hi ${data.userName},

${data.message}

This test email demonstrates the new ACCUSTOM branding with gold color scheme, Montserrat typography, and luxury styling that matches your app.

Open ACCUSTOM: ${data.actionUrl || 'Open the ACCUSTOM app'}

If you can see this email properly, the email system is now perfectly aligned with your app!

Best regards,
The ACCUSTOM Team`;

    return { subject, html, text };
  }
}
