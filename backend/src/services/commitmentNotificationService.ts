/**
 * Commitment Notification Service
 * Handles sophisticated contextual notifications for user commitments
 * Integrates with the contextual messaging system for intelligent content
 */

import { DatabaseService } from './databaseService';
import { NotificationDispatcher } from './notificationDispatcher';
import { ContextualMessagingService, MessageContext } from './contextualMessaging';

export interface CommitmentNotificationResult {
  success: boolean;
  message: string;
  commitmentId: string;
  userId: string;
  action: string;
  notificationsSent?: number;
  error?: string;
}

export class CommitmentNotificationService {
  private dbService: DatabaseService;
  private notificationDispatcher: NotificationDispatcher;

  constructor() {
    this.dbService = new DatabaseService();
    this.notificationDispatcher = new NotificationDispatcher();
  }

  /**
   * Send contextual reminder notification for a commitment
   */
  async sendCommitmentReminder(
    userId: string,
    commitmentId: string,
    hoursUntilDeadline?: number
  ): Promise<CommitmentNotificationResult> {
    try {
      // Get commitment and user data
      const [commitmentData, userData, userCommitData] = await Promise.all([
        this.dbService.getDocument('commits', commitmentId),
        this.dbService.getDocument('users', userId),
        this.dbService.getDocument(`users/${userId}/commits`, commitmentId)
      ]);

      if (!commitmentData || !userData) {
        return {
          success: false,
          message: 'Commitment or user data not found',
          commitmentId,
          userId,
          action: 'error',
          error: 'Missing data'
        };
      }

      // Build message context for commitment
      const context: MessageContext = {
        userName: userData.fname || 'there',
        activityName: commitmentData.title,
        activityType: 'commitment',
        commitmentTitle: commitmentData.title,
        reportingFrequency: commitmentData.schedule?.frequency || 'daily',
        amountAtRisk: commitmentData.financial?.amountPerReport || 0,
        amountLost: userCommitData?.financial?.totalLost || 0,
        verificationType: commitmentData.verification?.type,
        hoursUntilDeadline,
        streakCount: userCommitData?.progress?.currentStreak || 0,
        totalReports: userCommitData?.progress?.totalReports || 0,
        completedReports: userCommitData?.progress?.completedReports || 0,
        missedReports: userCommitData?.progress?.missedReports || 0,
        timeOfDay: ContextualMessagingService.getTimeOfDay(userData.timezone || 'UTC'),
        timezone: userData.timezone || 'UTC'
      };

      // Generate contextual message
      const contextualMessage = hoursUntilDeadline && hoursUntilDeadline <= 6
        ? ContextualMessagingService.generateUrgentMessage({ ...context, hoursLeft: hoursUntilDeadline })
        : ContextualMessagingService.generateReminderMessage(context);

      // Create notification
      const notification = {
        title: contextualMessage.title,
        message: contextualMessage.message,
        type: "reminder" as const,
        priority: (contextualMessage.tone === 'urgent' ? 'high' : 'medium') as const,
        data: {
          action: "submit_commitment",
          commitmentId: commitmentId,
          commitmentTitle: commitmentData.title,
          amountAtRisk: context.amountAtRisk?.toString(),
          verificationType: context.verificationType
        }
      };

      // Send notification through all enabled channels
      await this.notificationDispatcher.dispatchNotification(userId, notification);

      return {
        success: true,
        message: `Contextual commitment reminder sent for "${commitmentData.title}"`,
        commitmentId,
        userId,
        action: 'reminder_sent',
        notificationsSent: 1
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error sending commitment reminder for user ${userId}:`, errorMessage);

      return {
        success: false,
        message: `Failed to send commitment reminder: ${errorMessage}`,
        commitmentId,
        userId,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Send milestone celebration notification for commitment streak
   */
  async sendCommitmentMilestone(
    userId: string,
    commitmentId: string,
    streakCount: number
  ): Promise<CommitmentNotificationResult> {
    try {
      // Get commitment and user data
      const [commitmentData, userData, userCommitData] = await Promise.all([
        this.dbService.getDocument('commits', commitmentId),
        this.dbService.getDocument('users', userId),
        this.dbService.getDocument(`users/${userId}/commits`, commitmentId)
      ]);

      if (!commitmentData || !userData) {
        return {
          success: false,
          message: 'Commitment or user data not found',
          commitmentId,
          userId,
          action: 'error',
          error: 'Missing data'
        };
      }

      // Build message context for milestone
      const context: MessageContext = {
        userName: userData.fname || 'there',
        activityName: commitmentData.title,
        activityType: 'commitment',
        commitmentTitle: commitmentData.title,
        reportingFrequency: commitmentData.schedule?.frequency || 'daily',
        amountAtRisk: commitmentData.financial?.amountPerReport || 0,
        streakCount,
        timeOfDay: ContextualMessagingService.getTimeOfDay(userData.timezone || 'UTC'),
        timezone: userData.timezone || 'UTC'
      };

      // Generate milestone message
      const milestoneMessage = ContextualMessagingService.generateMilestoneMessage(context);

      // Create celebration notification
      const notification = {
        title: milestoneMessage.title,
        message: milestoneMessage.message,
        type: "program" as const, // Using program type for celebrations
        priority: "medium" as const,
        data: {
          action: "view_commitment",
          commitmentId: commitmentId,
          commitmentTitle: commitmentData.title,
          streakCount: streakCount.toString(),
          milestone: 'true'
        }
      };

      // Send celebration notification
      await this.notificationDispatcher.dispatchNotification(userId, notification);

      return {
        success: true,
        message: `Milestone celebration sent for ${streakCount}-report streak`,
        commitmentId,
        userId,
        action: 'milestone_sent',
        notificationsSent: 1
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error sending commitment milestone for user ${userId}:`, errorMessage);

      return {
        success: false,
        message: `Failed to send commitment milestone: ${errorMessage}`,
        commitmentId,
        userId,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Send encouraging message after missed commitment
   */
  async sendCommitmentEncouragement(
    userId: string,
    commitmentId: string,
    amountLost: number
  ): Promise<CommitmentNotificationResult> {
    try {
      // Get commitment and user data
      const [commitmentData, userData] = await Promise.all([
        this.dbService.getDocument('commits', commitmentId),
        this.dbService.getDocument('users', userId)
      ]);

      if (!commitmentData || !userData) {
        return {
          success: false,
          message: 'Commitment or user data not found',
          commitmentId,
          userId,
          action: 'error',
          error: 'Missing data'
        };
      }

      // Build encouraging context
      const context: MessageContext = {
        userName: userData.fname || 'there',
        activityName: commitmentData.title,
        activityType: 'commitment',
        commitmentTitle: commitmentData.title,
        reportingFrequency: commitmentData.schedule?.frequency || 'daily',
        amountAtRisk: commitmentData.financial?.amountPerReport || 0,
        amountLost,
        lastSubmissionDays: 1, // Indicates they missed yesterday
        timeOfDay: ContextualMessagingService.getTimeOfDay(userData.timezone || 'UTC'),
        timezone: userData.timezone || 'UTC'
      };

      // Generate encouraging message
      const encouragingMessage = ContextualMessagingService.generateReminderMessage(context);

      // Create encouragement notification
      const notification = {
        title: encouragingMessage.title,
        message: encouragingMessage.message,
        type: "reminder" as const,
        priority: "medium" as const,
        data: {
          action: "submit_commitment",
          commitmentId: commitmentId,
          commitmentTitle: commitmentData.title,
          encouragement: 'true',
          amountLost: amountLost.toString()
        }
      };

      // Send encouragement notification
      await this.notificationDispatcher.dispatchNotification(userId, notification);

      return {
        success: true,
        message: `Encouragement sent after $${amountLost} loss`,
        commitmentId,
        userId,
        action: 'encouragement_sent',
        notificationsSent: 1
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error sending commitment encouragement for user ${userId}:`, errorMessage);

      return {
        success: false,
        message: `Failed to send commitment encouragement: ${errorMessage}`,
        commitmentId,
        userId,
        action: 'error',
        error: errorMessage
      };
    }
  }
}

// Export singleton instance
let commitmentNotificationServiceInstance: CommitmentNotificationService | null = null;

export function getCommitmentNotificationService(): CommitmentNotificationService {
  if (!commitmentNotificationServiceInstance) {
    commitmentNotificationServiceInstance = new CommitmentNotificationService();
  }
  return commitmentNotificationServiceInstance;
}
