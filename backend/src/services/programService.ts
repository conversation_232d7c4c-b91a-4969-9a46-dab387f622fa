/**
 * Program service for program-specific business logic
 * Handles program lifecycle operations and participant management
 *
 * UPDATED: Now uses participant-level timezone system for simplified management
 */

import { DatabaseService, ProgramData, ParticipantData } from './databaseService';
import { SchedulerService } from './schedulerService';
import { ValidationService } from './validationService';
import { LockService } from './lockService';
import { TransactionManager, TransactionStep } from '../utils/transactionManager';
import { extractParticipantTimezones, calculateProgramStartTime, calculateProgramEndTime, findLatestTimezone, findEarliestTimezone } from '../utils/timezoneUtils';

export interface ProgramStartResult {
  success: true;
  message: string;
  programId: string;
  participantCount: number;
}

export interface ProgramEndResult {
  success: true;
  message: string;
  programId: string;
  endedAt?: string;
  rescheduledTo?: string;
  ongoingParticipants?: number;
  rescheduleAttempt?: number;
}

export interface IndividualCheckupResult {
  success: boolean;
  message: string;
  programId: string;
  userId: string;
  participantName?: string;
  action: string;
  dayNumber?: number;
  oldStatus?: string;
  newStatus?: string;
  oldLivesLeft?: number;
  livesLeft?: number;
  disqualified?: boolean;
  personalStatus?: string;
  currentDay?: number;
}

export class ProgramService {
  private dbService: DatabaseService;
  private schedulerService: SchedulerService;
  private lockService: LockService;
  private transactionManager: TransactionManager;

  constructor() {
    this.dbService = new DatabaseService();
    this.schedulerService = new SchedulerService();
    this.lockService = new LockService();
    this.transactionManager = new TransactionManager();
  }

  /**
   * Start a program - transition from upcoming to ongoing with transaction safety
   */
  async startProgram(programId: string): Promise<ProgramStartResult> {
    const steps: TransactionStep[] = [];
    let program: ProgramData;

    // Step 1: Get and validate program
    steps.push(TransactionManager.createSimpleStep(
      'validate_program',
      'Validate program can be started',
      async () => {
        program = await this.dbService.getProgram(programId);
        ValidationService.validateProgramCanStart(program);
        return program;
      }
    ));

    // Step 2: Update program status with rollback
    steps.push(TransactionManager.createCompensatingStep(
      'update_program_status',
      'Update program status to ongoing',
      async () => {
        await this.dbService.updateProgramStatus(programId, 'ongoing');
        return { programId, newStatus: 'ongoing' };
      },
      async () => {
        // Rollback: revert to upcoming status
        await this.dbService.updateProgramStatus(programId, 'upcoming');
      }
    ));

    // Step 3: Clean up starter job
    steps.push(TransactionManager.createSimpleStep(
      'cleanup_starter',
      'Clean up program starter job',
      async () => {
        await this.schedulerService.deleteJob(`program-starter-${programId}`);
        return { cleaned: true };
      }
    ));

    // Execute transaction
    const result = await this.transactionManager.executeTransaction(steps, `start_program_${programId}`);

    if (!result.success) {
      throw new Error(`Failed to start program: ${result.error}`);
    }

    return {
      success: true,
      message: `Program ${programId} started successfully`,
      programId,
      participantCount: 0 // Participant count no longer tracked during start
    };
  }

  /**
   * End a program with timezone check - two-stage ending system
   * First checks if any participants are still ongoing, if so reschedules for latest timezone
   */
  async endProgramWithTimezoneCheck(programId: string): Promise<ProgramEndResult> {
    // Get and validate program
    const program = await this.dbService.getProgram(programId);
    ValidationService.validateProgramCanEnd(program);

    // Check for infinite rescheduling protection
    const maxRescheduleAttempts = 5;
    const rescheduleCount = (program as any).rescheduleCount || 0;

    if (rescheduleCount >= maxRescheduleAttempts) {
      console.warn(`Program ${programId} has been rescheduled ${rescheduleCount} times, forcing end to prevent infinite loop`);
      return await this.endProgram(programId);
    }

    // Get all participants and check their current status
    const participants = await this.dbService.getProgramParticipants(programId);
    const ongoingParticipants = participants.filter(p => p.personalProgramStatus === 'ongoing');

    if (ongoingParticipants.length === 0) {
      // No ongoing participants, safe to end program
      return await this.endProgram(programId);
    }

    // Check if we're past the maximum reasonable end time (program duration + 2 days)
    const maxEndTime = new Date(program.startDate);
    maxEndTime.setDate(maxEndTime.getDate() + program.duration + 2);

    if (new Date() > maxEndTime) {
      console.warn(`Program ${programId} is past maximum reasonable end time, forcing end`);
      return await this.endProgram(programId);
    }

    // There are still ongoing participants, reschedule for latest timezone

    const ongoingTimezones = extractParticipantTimezones(ongoingParticipants);
    const latestEndTime = calculateProgramEndTime(program.startDate, program.duration, ongoingTimezones);


    // Update program with reschedule count
    await this.dbService.updateProgramStatus(programId, 'ongoing', {
      rescheduleCount: rescheduleCount + 1,
      lastRescheduleTime: new Date().toISOString()
    });

    // Delete current ender job and create new one for latest timezone
    await this.schedulerService.deleteJob(`program-ender-${programId}`);
    await this.schedulerService.createProgramEnderJob(programId, latestEndTime);

    return {
      success: true,
      message: `Program end rescheduled for ${ongoingParticipants.length} ongoing participants (attempt ${rescheduleCount + 1})`,
      programId,
      rescheduledTo: latestEndTime.toISOString(),
      ongoingParticipants: ongoingParticipants.length,
      rescheduleAttempt: rescheduleCount + 1
    };
  }

  /**
   * End a program - transition from ongoing to ended (original method)
   */
  async endProgram(programId: string): Promise<ProgramEndResult> {
    // Get and validate program
    const program = await this.dbService.getProgram(programId);
    ValidationService.validateProgramCanEnd(program);

    // Update program status
    await this.dbService.updateProgramStatus(programId, 'ended');

    // Clean up the ender scheduler job
    await this.schedulerService.deleteJob(`program-ender-${programId}`);

    // Clean up all recurring checkup jobs for this program
    await this.cleanupProgramRecurringJobs(programId);

    return {
      success: true,
      message: `Program ${programId} ended successfully`,
      programId,
      endedAt: new Date().toISOString()
    };
  }

  /**
   * Perform individual daily checkup for a participant
   * Enhanced to handle both legacy (specific day) and recurring approaches
   */
  async performIndividualCheckup(programId: string, userId: string, schedulerData?: any): Promise<IndividualCheckupResult> {

    // Get participant details
    const participant = await this.dbService.getParticipant(programId, userId);

    // Check if participant has individual program status fields
    if (!participant.personalProgramStatus) {
      return {
        success: false,
        message: 'Participant missing individual program status fields',
        programId,
        userId,
        action: 'missing_individual_status'
      };
    }

    // Skip already disqualified participants
    if (participant.disqualified === true) {
      return {
        success: true,
        message: 'Participant already disqualified',
        programId,
        userId,
        participantName: participant.fname,
        livesLeft: participant.livesLeft || 0,
        disqualified: true,
        action: 'skipped_already_disqualified'
      };
    }

    // Handle recurring checkup logic
    if (schedulerData?.isRecurring) {
      const recurringResult = await this.handleRecurringCheckup(participant, programId, schedulerData);
      if (recurringResult) {
        return recurringResult;
      }
    }

    // Update participant's current status
    const statusUpdate = await this.updateParticipantCurrentStatus(participant, programId);
    if (statusUpdate.personalStatus === 'ended') {
      // For recurring jobs, clean up the scheduler when program ends
      if (schedulerData?.isRecurring) {
        await this.schedulerService.deleteRecurringCheckupJob(programId, userId);
      }

      return {
        success: true,
        message: 'Program ended for user',
        programId,
        userId,
        participantName: participant.fname,
        personalStatus: statusUpdate.personalStatus,
        currentDay: statusUpdate.currentDay,
        action: 'program_ended'
      };
    }

    // Only process checkups for ongoing programs
    if (statusUpdate.personalStatus !== 'ongoing') {
      return {
        success: true,
        message: 'Program not ongoing for user',
        programId,
        userId,
        participantName: participant.fname,
        personalStatus: statusUpdate.personalStatus,
        currentDay: statusUpdate.currentDay,
        action: 'program_not_ongoing'
      };
    }

    // Process previous day's submission
    return await this.processPreviousDaySubmission(participant, statusUpdate.currentDay, programId);
  }

  /**
   * Set up program start and end schedulers based on participant timezones
   */
  async setupProgramStartScheduler(programId: string): Promise<any> {
    const lockId = `program-scheduler-setup-${programId}`;

    return await this.lockService.withLock(
      lockId,
      'setupProgramStartScheduler',
      async () => {
        try {

          // Get program and validate
          const program = await this.dbService.getProgram(programId);

          if (program.status !== 'upcoming') {
            return {
              success: false,
              message: `Program status is ${program.status}, scheduler only needed for upcoming programs`,
              programId
            };
          }

          // Get participant timezones
          const participants = await this.dbService.getProgramParticipants(programId);

          const participantTimezones = extractParticipantTimezones(participants);

          // Calculate program start time (earliest participant timezone)
          const programStartTime = calculateProgramStartTime(program.startDate, participantTimezones);
          const earliestTimezone = participantTimezones.length > 0 ? findEarliestTimezone(participantTimezones, new Date()) : 'UTC';

          // Calculate initial program end time (latest participant timezone)
          const programInitialEndTime = calculateProgramEndTime(program.startDate, program.duration, participantTimezones);
          const latestTimezone = participantTimezones.length > 0 ? findLatestTimezone(participantTimezones, new Date()) : 'UTC';

          // Delete existing jobs if they exist
          await this.schedulerService.deleteJob(`program-starter-${programId}`);
          await this.schedulerService.deleteJob(`program-ender-${programId}`);

          // Create new starter job
          await this.schedulerService.createProgramStarterJob(programId, programStartTime);

          // Create new ender job (will check for ongoing participants and reschedule if needed)
          await this.schedulerService.createProgramEnderJob(programId, programInitialEndTime);

          return {
            success: true,
            message: 'Program start and initial end schedulers set up successfully with timezone awareness',
            programId,
            startTime: programStartTime.toISOString(),
            initialEndTime: programInitialEndTime.toISOString(),
            participantCount: participantTimezones.length,
            earliestTimezone,
            latestTimezone
          };
        } catch (error) {
          console.error(`❌ Error in setupProgramStartScheduler for ${programId}:`, error);
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            programId
          };
        }
      }
    );
  }

  /**
   * Set up program end scheduler only - now uses timezone-aware scheduling from the beginning
   * This is called when setting up initial scheduling, not for participant changes
   */
  async setupProgramEndScheduler(programId: string): Promise<any> {
    // Get program and validate
    const program = await this.dbService.getProgram(programId);

    if (program.status === 'ended') {
      return {
        success: false,
        message: `Program status is ${program.status}, end scheduler not needed`,
        programId
      };
    }

    // Get participant timezones for timezone-aware scheduling
    const participants = await this.dbService.getProgramParticipants(programId);
    const participantTimezones = extractParticipantTimezones(participants);

    // Calculate initial program end time based on latest participant timezone
    const programInitialEndTime = calculateProgramEndTime(program.startDate, program.duration, participantTimezones);
    const latestTimezone = participantTimezones.length > 0 ? findLatestTimezone(participantTimezones, new Date()) : 'UTC';

    // Delete existing ender job if it exists
    await this.schedulerService.deleteJob(`program-ender-${programId}`);

    // Create new ender job (will check for ongoing participants and reschedule if needed)
    await this.schedulerService.createProgramEnderJob(programId, programInitialEndTime);

    return {
      success: true,
      message: 'Program initial end scheduler set up successfully with timezone awareness',
      programId,
      initialEndTime: programInitialEndTime.toISOString(),
      participantCount: participantTimezones.length,
      latestTimezone
    };
  }

  /**
   * Set up individual scheduling for a participant (NEW RECURRING APPROACH)
   */
  async setupIndividualScheduling(programId: string, userId: string): Promise<any> {
    const participant = await this.dbService.getParticipant(programId, userId);
    ValidationService.validateParticipantIndividualStatus(participant);

    // Set up recurring checkup job
    const checkupResult = await this.scheduleParticipantRecurringCheckup(programId, participant);

    // Set up notification scheduler job
    const notificationResult = await this.scheduleParticipantNotifications(programId, participant);

    const totalJobs = (checkupResult.success ? 1 : 0) + (notificationResult.success ? 1 : 0);

    return {
      success: true,
      message: 'Individual recurring scheduling and notifications set up for participant',
      programId,
      userId,
      schedulerJobs: totalJobs
    };
  }

  /**
   * Set up individual scheduling for a participant (LEGACY APPROACH)
   * Kept for backward compatibility
   */
  async setupIndividualSchedulingLegacy(programId: string, userId: string): Promise<any> {
    const participant = await this.dbService.getParticipant(programId, userId);
    ValidationService.validateParticipantIndividualStatus(participant);

    const program = await this.dbService.getProgram(programId);
    const result = await this.scheduleParticipantCheckups(programId, participant, program);

    return {
      success: true,
      message: 'Individual scheduling set up for participant',
      programId,
      userId,
      schedulerJobs: result.totalJobs
    };
  }

  /**
   * Calculate notification times based on user preferences
   */
  private calculateNotificationTimes(participant: ParticipantData): string[] {
    try {
      // Get user's notification preferences
      const notificationTiming = participant.programPreferences?.notificationTiming;

      if (!notificationTiming) {
        return ['20:00']; // Default to 8 PM
      }

      console.log(`📋 Calculating notification times for participant ${participant.id}:`, {
        type: notificationTiming.type,
        preferences: notificationTiming
      });

      switch (notificationTiming.type) {
        case 'multiple-times':
          if (notificationTiming.multipleTimes && notificationTiming.multipleTimes.length > 0) {
            return notificationTiming.multipleTimes;
          } else {
            return ['20:00'];
          }

        case 'hours-before':
          const hoursBeforeDeadline = notificationTiming.hoursBeforeDeadline || 4;
          // Deadline is at midnight (00:00), so calculate hours before
          // Example: 3 hours before midnight = 21:00 (9 PM)
          const notificationHour = 24 - hoursBeforeDeadline;
          // Ensure hour is within valid range (0-23)
          const validHour = Math.max(0, Math.min(23, notificationHour));
          const notificationTime = `${validHour.toString().padStart(2, '0')}:00`;

          console.log(`⏰ Hours-before calculation:`, {
            hoursBeforeDeadline,
            calculatedHour: validHour,
            notificationTime
          });

          return [notificationTime];

        case 'every-x-hours':
          // For every-x-hours, we could create multiple jobs throughout the day
          // For now, let's use a default time and enhance this later
          return ['20:00'];

        default:
          return ['20:00'];
      }

    } catch (error) {
      console.error(`Error calculating notification times for ${participant.id}:`, error);
      return ['20:00']; // Fallback to 8 PM
    }
  }

  /**
   * Private helper methods
   */

  /**
   * Handle recurring checkup logic - determines if checkup should run today
   */
  private async handleRecurringCheckup(
    participant: ParticipantData,
    programId: string,
    schedulerData: any
  ): Promise<IndividualCheckupResult | null> {
    const now = new Date();
    const personalStartDate = new Date(schedulerData.personalStartDate);
    const personalEndDate = new Date(schedulerData.personalEndDate);


    // Use user's timezone for date comparison with improved timezone handling
    const userTimezone = participant.timezone || 'UTC';

    // Get current date in user's timezone using proper timezone conversion
    const nowDateInUserTz = this.getDateInTimezone(now, userTimezone);

    // Get program start/end dates in user's timezone
    const startDateOnlyInUserTz = this.getDateInTimezone(personalStartDate, userTimezone);
    const endDateOnlyInUserTz = this.getDateInTimezone(personalEndDate, userTimezone);

    console.log(`🕐 Individual checkup timing check for user ${participant.id}:`, {
      nowDateInUserTz: nowDateInUserTz.toISOString(),
      startDateOnlyInUserTz: startDateOnlyInUserTz.toISOString(),
      endDateOnlyInUserTz: endDateOnlyInUserTz.toISOString(),
      isBeforeStart: nowDateInUserTz.getTime() < startDateOnlyInUserTz.getTime(),
      isAfterEnd: nowDateInUserTz.getTime() > endDateOnlyInUserTz.getTime(),
      userTimezone: userTimezone
    });

    // Check if we're within the program duration for this user using proper date comparison
    if (nowDateInUserTz.getTime() < startDateOnlyInUserTz.getTime()) {
      return {
        success: true,
        message: 'Program not started yet for user',
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'program_not_started'
      };
    }

    if (nowDateInUserTz.getTime() > endDateOnlyInUserTz.getTime()) {
      // Program ended, clean up the recurring job
      await this.schedulerService.deleteRecurringCheckupJob(programId, participant.id);
      return {
        success: true,
        message: 'Program ended for user, cleaned up recurring job',
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'program_ended_cleanup'
      };
    }

    // Continue with normal checkup logic
    return null;
  }

  /**
   * Get date-only representation in a specific timezone
   * Avoids timezone conversion issues by properly handling date boundaries
   */
  private getDateInTimezone(date: Date, timezone: string): Date {
    try {
      // Get the date string in the target timezone
      const dateString = date.toLocaleDateString("en-CA", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      // Create a new date object representing just the date (no time) in the target timezone
      const [year, month, day] = dateString.split('-').map(Number);
      return new Date(year, month - 1, day, 0, 0, 0, 0);
    } catch (error) {
      console.error(`Error converting date to timezone ${timezone}:`, error);
      // Fallback to UTC date
      return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0, 0);
    }
  }

  /**
   * Schedule recurring checkup for a participant (NEW APPROACH)
   */
  private async scheduleParticipantRecurringCheckup(
    programId: string,
    participant: ParticipantData
  ): Promise<{ success: boolean; error?: string }> {
    const timezone = participant.timezone || 'UTC';

    const personalStartDate = new Date(participant.personalStartDate!);
    const personalEndDate = new Date(participant.personalEndDate!);

    return await this.schedulerService.createParticipantRecurringCheckup(
      programId,
      participant.id,
      personalStartDate,
      personalEndDate,
      timezone
    );
  }

  /**
   * Schedule recurring notifications for a participant
   */
  private async scheduleParticipantNotifications(
    programId: string,
    participant: ParticipantData
  ): Promise<{ success: boolean; error?: string }> {
    const timezone = participant.timezone || 'UTC';

    const personalStartDate = new Date(participant.personalStartDate!);
    const personalEndDate = new Date(participant.personalEndDate!);

    // Calculate notification times based on user preferences
    const notificationTimes = this.calculateNotificationTimes(participant);

    // Create multiple notification scheduler jobs if user selected multiple times
    let successCount = 0;
    let errors: string[] = [];

    for (let i = 0; i < notificationTimes.length; i++) {
      const notificationTime = notificationTimes[i];
      try {
        const result = await this.schedulerService.createParticipantNotificationScheduler(
          programId,
          participant.id,
          personalStartDate,
          personalEndDate,
          timezone,
          notificationTime,
          i + 1 // Job index for unique naming
        );

        if (result.success) {
          successCount++;
        } else {
          errors.push(`Time ${notificationTime}: ${result.error}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Time ${notificationTime}: ${errorMessage}`);
      }
    }

    if (successCount === notificationTimes.length) {
      return { success: true };
    } else if (successCount > 0) {
      return {
        success: true,
        error: `Partial success: ${successCount}/${notificationTimes.length} jobs created. Errors: ${errors.join(', ')}`
      };
    } else {
      return {
        success: false,
        error: `Failed to create notification jobs. Errors: ${errors.join(', ')}`
      };
    }
  }

  /**
   * Schedule individual checkups for a participant (LEGACY APPROACH)
   */
  private async scheduleParticipantCheckups(
    programId: string,
    participant: ParticipantData,
    _program: ProgramData
  ): Promise<{ totalJobs: number; errors: string[] }> {
    const timezone = participant.timezone || 'UTC';

    const personalStartDate = new Date(participant.personalStartDate!);
    const personalEndDate = new Date(participant.personalEndDate!);

    return await this.schedulerService.createParticipantCheckupJobs(
      programId,
      participant.id,
      personalStartDate,
      personalEndDate,
      timezone
    );
  }

  /**
   * Clean up all recurring checkup jobs for a program when it ends
   */
  private async cleanupProgramRecurringJobs(programId: string): Promise<void> {
    try {
      // Get all participants for this program
      const participants = await this.dbService.getProgramParticipants(programId);


      // Delete recurring jobs for all participants
      const cleanupPromises = participants.map(participant =>
        this.schedulerService.deleteRecurringCheckupJob(programId, participant.id)
      );

      const results = await Promise.all(cleanupPromises);
      const deletedCount = results.filter(result => result.deleted).length;
      console.log(`🧹 Cleaned up ${deletedCount} recurring jobs for program ${programId}`);

    } catch (error) {
      console.error(`Error cleaning up recurring jobs for program ${programId}:`, error);
      // Don't throw - program ending should not fail due to cleanup issues
    }
  }

  private async updateParticipantCurrentStatus(participant: ParticipantData, programId: string): Promise<{
    personalStatus: 'upcoming' | 'ongoing' | 'ended';
    currentDay: number;
  }> {
    const program = await this.dbService.getProgram(programId);
    const userTimezone = participant.timezone || 'UTC';

    // Simple participant-level calculation - no edge cases
    const result = this.calculateParticipantProgramDay(program.startDate, userTimezone, program.duration);

    // Update participant if status changed
    const updateData: any = {};
    if (participant.personalProgramStatus !== result.status) {
      updateData.personalProgramStatus = result.status;
    }
    if (participant.personalCurrentDay !== result.currentDay) {
      updateData.personalCurrentDay = result.currentDay;
    }
    updateData.lastDayCheckup = new Date().toISOString();

    if (Object.keys(updateData).length > 0) {
      await this.dbService.updateParticipant(programId, participant.id, updateData);
    }

    return {
      personalStatus: result.status,
      currentDay: result.currentDay
    };
  }

  /**
   * Simple participant program day calculation - eliminates timezone coordination complexity
   * Fixed to properly handle timezone conversions for accurate date calculations
   */
  private calculateParticipantProgramDay(
    programStartDate: string,
    userTimezone: string,
    programDuration: number
  ): { currentDay: number; status: 'upcoming' | 'ongoing' | 'ended' } {
    try {
      // Get current date in user's timezone using proper timezone conversion
      const now = new Date();
      const userDateString = now.toLocaleDateString("en-CA", {
        timeZone: userTimezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      const userDate = new Date(userDateString + 'T00:00:00');

      // Parse program start date with validation
      const [year, month, day] = programStartDate.split('-').map(Number);
      if (!year || !month || !day || month < 1 || month > 12 || day < 1 || day > 31) {
        throw new Error(`Invalid program start date format: ${programStartDate}`);
      }
      const startDate = new Date(programStartDate + 'T00:00:00');

      // Calculate days since start using proper date comparison
      const daysDiff = Math.floor((userDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const currentDay = daysDiff + 1;

      // Determine status
      let status: 'upcoming' | 'ongoing' | 'ended';
      if (currentDay < 1) {
        status = 'upcoming';
      } else if (currentDay > programDuration) {
        status = 'ended';
      } else {
        status = 'ongoing';
      }

      // Ensure currentDay is within reasonable bounds for display
      const displayDay = Math.max(1, Math.min(currentDay, programDuration + 1));


      return {
        currentDay: displayDay,
        status
      };
    } catch (error) {
      console.error('Error calculating participant program day:', error);
      return { currentDay: 1, status: 'upcoming' };
    }
  }

  private async processPreviousDaySubmission(
    participant: ParticipantData,
    currentDay: number,
    programId: string
  ): Promise<IndividualCheckupResult> {
    const previousDay = currentDay - 1;
    if (previousDay < 1) {
      return {
        success: true,
        message: 'No previous day to check',
        programId,
        userId: participant.id,
        participantName: participant.fname,
        currentDay,
        action: 'no_previous_day'
      };
    }

    // Get previous day's submission
    const submission = await this.dbService.getSubmission(programId, participant.id, previousDay);
    const submissionStatus = submission?.status || 'upcoming';


    // Handle different submission statuses
    switch (submissionStatus) {
      case 'upcoming':
        // Not submitted yet - process as missed submission
        return await this.handleMissedSubmission(participant, previousDay, programId);

      case 'submitted':
        // Already submitted successfully - no action needed
        return {
          success: true,
          message: 'Previous day already submitted successfully',
          programId,
          userId: participant.id,
          dayNumber: previousDay,
          participantName: participant.fname,
          oldStatus: submissionStatus,
          livesLeft: participant.livesLeft || 0,
          action: 'already_submitted'
        };

      case 'bailed':
        // Already processed as bailed - no action needed
        return {
          success: true,
          message: 'Previous day already processed as bailed',
          programId,
          userId: participant.id,
          dayNumber: previousDay,
          participantName: participant.fname,
          oldStatus: submissionStatus,
          livesLeft: participant.livesLeft || 0,
          action: 'already_bailed'
        };

      case 'not_submitted':
        // Already processed as not submitted (disqualified) - no action needed
        return {
          success: true,
          message: 'Previous day already processed as not submitted',
          programId,
          userId: participant.id,
          dayNumber: previousDay,
          participantName: participant.fname,
          oldStatus: submissionStatus,
          livesLeft: participant.livesLeft || 0,
          action: 'already_not_submitted'
        };

      default:
        // Unknown status - log warning and treat as already processed
        console.warn(`Unknown submission status '${submissionStatus}' for participant ${participant.id}, day ${previousDay}`);
        return {
          success: true,
          message: `Previous day has unknown status: ${submissionStatus}`,
          programId,
          userId: participant.id,
          dayNumber: previousDay,
          participantName: participant.fname,
          oldStatus: submissionStatus,
          livesLeft: participant.livesLeft || 0,
          action: 'unknown_status'
        };
    }
  }

  private async handleMissedSubmission(
    participant: ParticipantData,
    dayNumber: number,
    programId: string
  ): Promise<IndividualCheckupResult> {
    const currentLives = participant.livesLeft || 0;
    const currentTime = new Date();

    if (currentLives > 0) {
      // Deduct a life and mark as bailed
      const newLives = currentLives - 1;
      const shouldDisqualify = newLives <= 0;

      const updateData: any = { livesLeft: newLives };
      if (shouldDisqualify) {
        updateData.disqualified = true;
        updateData.disqualifyReason = `Missed submission on day ${dayNumber} - No lives left`;
      }

      // Atomically update participant and submission
      await this.dbService.updateParticipantAndSubmission(
        programId,
        participant.id,
        dayNumber,
        updateData,
        {
          status: shouldDisqualify ? 'not_submitted' : 'bailed',
          timestamp: currentTime.toISOString(),
          attachment: shouldDisqualify ? 'Disqualified - No lives left' : 'Bailed - Life deducted'
        }
      );

      // Clean up recurring scheduler job if user is disqualified
      if (shouldDisqualify) {
        await this.schedulerService.deleteRecurringCheckupJob(programId, participant.id);
      }

      return {
        success: true,
        message: 'Participant status updated',
        programId,
        userId: participant.id,
        dayNumber,
        participantName: participant.fname,
        oldStatus: 'upcoming',
        newStatus: shouldDisqualify ? 'not_submitted' : 'bailed',
        oldLivesLeft: currentLives,
        livesLeft: newLives,
        disqualified: shouldDisqualify,
        action: shouldDisqualify ? 'disqualified' : 'bailed_life_deducted'
      };
    } else {
      // No lives left - disqualify atomically
      await this.dbService.updateParticipantAndSubmission(
        programId,
        participant.id,
        dayNumber,
        {
          disqualified: true,
          disqualifyReason: `Missed submission on day ${dayNumber} - No lives left`
        },
        {
          status: 'not_submitted',
          timestamp: currentTime.toISOString(),
          attachment: 'Disqualified - No lives left'
        }
      );

      return {
        success: true,
        message: 'Participant disqualified',
        programId,
        userId: participant.id,
        dayNumber,
        participantName: participant.fname,
        oldStatus: 'upcoming',
        newStatus: 'not_submitted',
        livesLeft: 0,
        disqualified: true,
        action: 'disqualified'
      };
    }
  }


}
