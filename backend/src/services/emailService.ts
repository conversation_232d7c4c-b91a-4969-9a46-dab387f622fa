/**
 * Email Service using SendGrid
 * Handles email sending functionality for notifications
 */

import sgMail from '@sendgrid/mail';
import { getEmailConfig } from '../utils/config';
import { DatabaseService } from './databaseService';
import { EmailTemplates, TemplateData } from './emailTemplates';

export interface EmailNotification {
  title: string;
  message: string;
  type: 'account' | 'program' | 'points' | 'reminder';
  priority: 'low' | 'medium' | 'high';
  data?: Record<string, string>;
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  timestamp?: string;
  error?: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class EmailService {
  private dbService: DatabaseService;
  private emailConfig: any;

  constructor() {
    this.dbService = new DatabaseService();
    this.initializeSendGrid();
  }

  /**
   * Initialize SendGrid with API key
   */
  private initializeSendGrid(): void {
    try {
      this.emailConfig = getEmailConfig();
      sgMail.setApiKey(this.emailConfig.sendgridApiKey);
    } catch (error) {
      console.error('Failed to initialize SendGrid:', error);
      throw error;
    }
  }

  /**
   * Send email notification to a user
   */
  async sendEmailNotification(userId: string, notification: EmailNotification): Promise<EmailResult> {
    try {
      // Get user email from database
      const userData = await this.dbService.getDocument('users', userId);
      if (!userData) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const userEmail = userData.email;
      if (!userEmail) {
        return {
          success: false,
          error: 'User email not found'
        };
      }

      // Check user email preferences
      const emailPrefs = await this.getUserEmailPreferences(userId);
      if (!this.shouldSendEmail(notification.type, emailPrefs)) {
        return {
          success: true,
          messageId: 'skipped',
          timestamp: new Date().toISOString()
        };
      }

      // Generate email template
      const template = this.generateEmailTemplate(notification, userData);

      // Send email
      const result = await this.sendEmail(userEmail, template);
      
      // Log email sent
      await this.logEmailSent(userId, notification, result);

      return result;

    } catch (error) {
      console.error('Error sending email notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }



  /**
   * Send welcome email
   */
  async sendWelcomeEmail(userId: string, userName: string): Promise<EmailResult> {
    const welcomeNotification: EmailNotification = {
      title: `Welcome to Accustom, ${userName}!`,
      message: `Hi ${userName}, welcome to Accustom! We're excited to help you build lasting habits and achieve your goals.`,
      type: 'account',
      priority: 'medium'
    };

    return this.sendEmailNotification(userId, welcomeNotification);
  }

  /**
   * Send email using SendGrid
   */
  private async sendEmail(to: string, template: EmailTemplate): Promise<EmailResult> {
    try {
      const msg = {
        to,
        from: {
          email: this.emailConfig.fromEmail,
          name: this.emailConfig.fromName
        },
        replyTo: this.emailConfig.replyToEmail,
        subject: template.subject,
        text: template.text,
        html: template.html,
        trackingSettings: {
          clickTracking: {
            enable: true
          },
          openTracking: {
            enable: true
          }
        }
      };

      const response = await sgMail.send(msg);
      
      return {
        success: true,
        messageId: response[0].headers['x-message-id'] || 'unknown',
        timestamp: new Date().toISOString()
      };

    } catch (error: any) {
      console.error('SendGrid error:', error);
      
      let errorMessage = 'Failed to send email';
      if (error.response?.body?.errors) {
        errorMessage = error.response.body.errors.map((e: any) => e.message).join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Generate email template based on notification type
   */
  private generateEmailTemplate(notification: EmailNotification, user: any): EmailTemplate {
    const userName = user.fname || 'there';

    const templateData: TemplateData = {
      userName,
      title: notification.title,
      message: notification.message,
      actionUrl: '#', // TODO: Add proper deep links
      programName: notification.data?.programName,
      dayNumber: notification.data?.dayNumber ? parseInt(notification.data.dayNumber) : undefined,
      streakCount: notification.data?.streakCount ? parseInt(notification.data.streakCount) : undefined,
      pointsEarned: notification.data?.pointsEarned ? parseInt(notification.data.pointsEarned) : undefined,
    };

    // Use appropriate template based on notification type
    switch (notification.type) {
      case 'reminder':
        return EmailTemplates.getReminderTemplate(templateData);
      case 'program':
        return EmailTemplates.getProgramAlertTemplate(templateData);
      case 'account':
        if (notification.title.toLowerCase().includes('welcome')) {
          return EmailTemplates.getWelcomeTemplate(templateData);
        }
        return EmailTemplates.getGenericTemplate(templateData);
      case 'points':
        return EmailTemplates.getProgramAlertTemplate(templateData);
      default:
        return EmailTemplates.getGenericTemplate(templateData);
    }
  }

  /**
   * Get user email preferences
   */
  private async getUserEmailPreferences(userId: string): Promise<any> {
    try {
      const userData = await this.dbService.getDocument('users', userId);
      return userData?.notificationPreferences?.emailNotifications || {
        reminders: true,
        programAlerts: true,
        marketingNotifications: false,
        chatRoomNotifications: false
      };
    } catch (error) {
      console.error('Error getting email preferences:', error);
      // Default to enabled for critical notifications
      return {
        reminders: true,
        programAlerts: true,
        marketingNotifications: false,
        chatRoomNotifications: false
      };
    }
  }

  /**
   * Check if email should be sent based on user preferences
   */
  private shouldSendEmail(type: string, preferences: any): boolean {
    switch (type) {
      case 'reminder':
        return preferences.reminders !== false;
      case 'program':
        return preferences.programAlerts !== false;
      case 'account':
        return true; // Always send account-related emails
      case 'points':
        return preferences.programAlerts !== false;
      default:
        return true;
    }
  }

  /**
   * Log email sent to database
   */
  private async logEmailSent(userId: string, notification: EmailNotification, result: EmailResult): Promise<void> {
    try {
      const db = this.dbService.getDatabase();
      await db.collection('users').doc(userId).collection('emailLogs').add({
        timestamp: new Date().toISOString(),
        type: notification.type,
        title: notification.title,
        success: result.success,
        messageId: result.messageId,
        error: result.error
      });
    } catch (error) {
      console.error('Error logging email:', error);
      // Don't throw - logging failure shouldn't break email sending
    }
  }
}
