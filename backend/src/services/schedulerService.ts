/**
 * Scheduler service for Google Cloud Scheduler operations
 * Centralizes scheduler client setup and job management
 */

import { CloudSchedulerClient } from '@google-cloud/scheduler';
import { getCloudConfig, getSchedulerConfig, getFunctionUrl } from '../utils/config';
import { withTimeout } from '../utils/errorHandler';

export interface SchedulerJobConfig {
  name: string;
  functionName: string;
  schedule: string;
  timeZone?: string;
  data?: any;
  description?: string;
}

export interface IndividualCheckupJobConfig {
  programId: string;
  userId: string;
  dayNumber: number;
  checkupDate: Date;
  timezone: string;
}

export interface RecurringCheckupJobConfig {
  programId: string;
  userId: string;
  timezone: string;
  personalStartDate: Date;
  personalEndDate: Date;
}

export interface NotificationSchedulerJobConfig {
  programId: string;
  userId: string;
  timezone: string;
  personalStartDate: Date;
  personalEndDate: Date;
  notificationTime?: string; // Time to send notifications (default: '20:00' - 8 PM)
}

export class SchedulerService {
  private client: CloudSchedulerClient;
  private cloudConfig = getCloudConfig();
  private schedulerConfig = getSchedulerConfig();
  private parent: string;

  constructor() {
    this.client = new CloudSchedulerClient();
    this.parent = this.client.locationPath(
      this.cloudConfig.projectId,
      this.cloudConfig.location
    );
  }

  /**
   * Create a generic scheduler job
   */
  async createJob(config: SchedulerJobConfig): Promise<void> {
    const jobPath = this.client.jobPath(
      this.cloudConfig.projectId,
      this.cloudConfig.location,
      config.name
    );

    const job = {
      name: jobPath,
      httpTarget: {
        uri: getFunctionUrl(config.functionName, this.cloudConfig),
        httpMethod: 'POST' as const,
        headers: {
          'Content-Type': 'application/json',
        },
        body: Buffer.from(JSON.stringify({
          data: config.data || {}
        })),
        oidcToken: {
          serviceAccountEmail: `<EMAIL>`,
          audience: getFunctionUrl(config.functionName, this.cloudConfig)
        }
      },
      schedule: config.schedule,
      timeZone: config.timeZone || this.schedulerConfig.defaultTimeZone,
      ...(config.description && { description: config.description })
    };

    await withTimeout(
      this.client.createJob({ parent: this.parent, job }),
      this.schedulerConfig.timeoutMs,
      `Scheduler job creation timed out for ${config.name}`
    );
  }

  /**
   * Delete a scheduler job
   */
  async deleteJob(jobName: string): Promise<{ deleted: boolean; reason?: string }> {
    const jobPath = this.client.jobPath(
      this.cloudConfig.projectId,
      this.cloudConfig.location,
      jobName
    );

    try {
      // Delete job directly - NOT_FOUND errors are expected and handled
      await withTimeout(
        this.client.deleteJob({ name: jobPath }),
        this.schedulerConfig.timeoutMs,
        `Scheduler job deletion timed out for ${jobName}`
      );
      return { deleted: true };
    } catch (error: any) {
      if (error.code === 5) { // NOT_FOUND - this is expected and not a failure
        return { deleted: false, reason: 'not_found' };
      }
      // Only log actual errors, not NOT_FOUND
      console.warn(`Failed to delete scheduler job ${jobName}:`, error.message);
      return { deleted: false, reason: error.message };
    }
  }

  /**
   * Check if a scheduler job exists
   * Note: NOT_FOUND errors are expected and handled gracefully
   */
  async jobExists(jobName: string): Promise<boolean> {
    const jobPath = this.client.jobPath(
      this.cloudConfig.projectId,
      this.cloudConfig.location,
      jobName
    );

    try {
      await withTimeout(
        this.client.getJob({ name: jobPath }),
        this.schedulerConfig.timeoutMs,
        `Scheduler job existence check timed out for ${jobName}`
      );
      return true;
    } catch (error: any) {
      if (error.code === 5) { // NOT_FOUND - this is expected
        return false;
      }
      throw error;
    }
  }

  /**
   * Create program starter job
   */
  async createProgramStarterJob(programId: string, startTime: Date): Promise<void> {
    const cronExpression = this.createCronForUTCDateTime(startTime);

    await this.createJob({
      name: `program-starter-${programId}`,
      functionName: 'programStarter',
      schedule: cronExpression,
      timeZone: 'UTC',
      data: { programId },
      description: `Start program ${programId} at ${startTime.toISOString()}`
    });

  }

  /**
   * Create program ender job
   */
  async createProgramEnderJob(programId: string, endTime: Date): Promise<void> {
    const cronExpression = this.createCronForUTCDateTime(endTime);

    await this.createJob({
      name: `program-ender-${programId}`,
      functionName: 'programEnder',
      schedule: cronExpression,
      timeZone: 'UTC',
      data: { programId },
      description: `End program ${programId} at ${endTime.toISOString()}`
    });

  }

  /**
   * Sanitize user ID for use in scheduler job names
   * Google Cloud Scheduler job names must match [a-zA-Z\d_-]{1,500}
   */
  public sanitizeUserIdForJobName(userId: string): string {
    // Replace @ with -at- and . with -dot- to make email addresses scheduler-safe
    return userId
      .replace(/@/g, '-at-')
      .replace(/\./g, '-dot-')
      .replace(/[^a-zA-Z0-9_-]/g, '-') // Replace any other invalid chars with hyphens
      .substring(0, 100); // Limit length to prevent job names from being too long
  }

  /**
   * Create recurring individual daily checkup job (NEW APPROACH)
   * Creates one job per user that runs daily throughout their program duration
   */
  async createRecurringCheckupJob(config: RecurringCheckupJobConfig): Promise<void> {
    const sanitizedUserId = this.sanitizeUserIdForJobName(config.userId);
    const jobName = `recurring-checkup-${config.programId}-${sanitizedUserId}`;

    // Check if job already exists
    if (await this.jobExists(jobName)) {
      return;
    }

    try {
      await this.createJob({
        name: jobName,
        functionName: 'individualDailyCheckup',
        schedule: '0 0 * * *', // Daily at midnight (12 AM) in user's timezone
        timeZone: config.timezone,
        data: {
          programId: config.programId,
          userId: config.userId,
          personalStartDate: config.personalStartDate.toISOString(),
          personalEndDate: config.personalEndDate.toISOString(),
          isRecurring: true
        },
        description: `Recurring daily checkup for user ${config.userId} in program ${config.programId}`
      });

    } catch (error: any) {
      if (error.code === 6) { // ALREADY_EXISTS
        return;
      }
      throw error;
    }
  }

  /**
   * Delete recurring individual checkup job
   */
  async deleteRecurringCheckupJob(programId: string, userId: string): Promise<{ deleted: boolean; reason?: string }> {
    const sanitizedUserId = this.sanitizeUserIdForJobName(userId);
    const jobName = `recurring-checkup-${programId}-${sanitizedUserId}`;
    return await this.deleteJob(jobName);
  }

  /**
   * Create individual daily checkup job (LEGACY APPROACH - kept for backward compatibility)
   */
  async createIndividualCheckupJob(config: IndividualCheckupJobConfig, skipExistenceCheck: boolean = false): Promise<void> {
    // Set to 1 AM in user's timezone to ensure it runs after midnight deadline
    const checkupDate = new Date(config.checkupDate);
    checkupDate.setHours(1, 0, 0, 0);

    const cronExpression = `0 1 ${checkupDate.getDate()} ${checkupDate.getMonth() + 1} *`;
    const sanitizedUserId = this.sanitizeUserIdForJobName(config.userId);
    const jobName = `individual-checkup-${config.programId}-${sanitizedUserId}-day${config.dayNumber}`;

    // Check if job already exists (skip for new enrollments to improve performance)
    if (!skipExistenceCheck && await this.jobExists(jobName)) {
      return;
    }

    try {
      await this.createJob({
        name: jobName,
        functionName: 'individualDailyCheckup',
        schedule: cronExpression,
        timeZone: config.timezone,
        data: {
          programId: config.programId,
          userId: config.userId
        },
        description: `Daily checkup for user ${config.userId} in program ${config.programId}, day ${config.dayNumber}`
      });
    } catch (error: any) {
      // If job already exists (ALREADY_EXISTS error), that's fine - just log and continue
      if (error.code === 6) { // ALREADY_EXISTS
        return;
      }
      throw error;
    }
  }

  /**
   * Create recurring checkup job for a participant (NEW APPROACH)
   * Creates one recurring job instead of multiple individual jobs
   */
  async createParticipantRecurringCheckup(
    programId: string,
    userId: string,
    personalStartDate: Date,
    personalEndDate: Date,
    timezone: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await this.createRecurringCheckupJob({
        programId,
        userId,
        timezone,
        personalStartDate,
        personalEndDate
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error creating recurring checkup job for user ${userId}:`, errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Create recurring notification scheduler job for a participant
   * Sends daily reminder notifications at specified time
   */
  async createRecurringNotificationJob(config: NotificationSchedulerJobConfig & { jobIndex?: number }): Promise<void> {
    const sanitizedUserId = this.sanitizeUserIdForJobName(config.userId);

    // Create unique job name for multiple notification times
    const jobSuffix = config.jobIndex ? `-${config.jobIndex}` : '';
    const jobName = `notification-scheduler-${config.programId}-${sanitizedUserId}${jobSuffix}`;

    // Check if job already exists
    if (await this.jobExists(jobName)) {
      return;
    }

    // Parse notification time (default to 8 PM)
    const notificationTime = config.notificationTime || '20:00';
    const [hour, minute] = notificationTime.split(':').map(Number);

    try {
      await this.createJob({
        name: jobName,
        functionName: 'dailyNotificationScheduler',
        schedule: `${minute} ${hour} * * *`, // Daily at specified time in user's timezone
        timeZone: config.timezone,
        data: {
          programId: config.programId,
          userId: config.userId,
          personalStartDate: config.personalStartDate.toISOString(),
          personalEndDate: config.personalEndDate.toISOString(),
          notificationTime: notificationTime,
          isRecurring: true
        },
        description: `Daily notification scheduler for user ${config.userId} in program ${config.programId} at ${notificationTime}`
      });

    } catch (error: any) {
      if (error.code === 6) { // ALREADY_EXISTS
        return;
      }
      throw error;
    }
  }

  /**
   * Create notification scheduler job for a participant
   * Creates recurring notification delivery job
   */
  async createParticipantNotificationScheduler(
    programId: string,
    userId: string,
    personalStartDate: Date,
    personalEndDate: Date,
    timezone: string,
    notificationTime?: string,
    jobIndex?: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await this.createRecurringNotificationJob({
        programId,
        userId,
        timezone,
        personalStartDate,
        personalEndDate,
        notificationTime,
        jobIndex
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error creating notification scheduler job for user ${userId}:`, errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Create multiple individual checkup jobs for a participant (LEGACY APPROACH)
   * Kept for backward compatibility
   */
  async createParticipantCheckupJobs(
    programId: string,
    userId: string,
    personalStartDate: Date,
    personalEndDate: Date,
    timezone: string
  ): Promise<{ totalJobs: number; errors: string[] }> {
    const totalDays = Math.ceil(
      (personalEndDate.getTime() - personalStartDate.getTime()) / (1000 * 60 * 60 * 24)
    );


    // Create all job configurations first
    const jobConfigs = [];
    for (let day = 1; day <= totalDays; day++) {
      const checkupDate = new Date(personalStartDate);
      checkupDate.setDate(checkupDate.getDate() + day); // Day after submission day

      jobConfigs.push({
        programId,
        userId,
        dayNumber: day,
        checkupDate,
        timezone
      });
    }

    // Process jobs in parallel with concurrency limit to avoid overwhelming the API
    const BATCH_SIZE = 5; // Process 5 jobs at a time
    const errors: string[] = [];
    let successCount = 0;

    for (let i = 0; i < jobConfigs.length; i += BATCH_SIZE) {
      const batch = jobConfigs.slice(i, i + BATCH_SIZE);

      const batchPromises = batch.map(async (config) => {
        try {
          // Skip existence check for new enrollments to improve performance
          await this.createIndividualCheckupJob(config, true);
          return { success: true, day: config.dayNumber };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return { success: false, day: config.dayNumber, error: errorMessage };
        }
      });

      const batchResults = await Promise.all(batchPromises);

      for (const result of batchResults) {
        if (result.success) {
          successCount++;
        } else {
          errors.push(`Day ${result.day}: ${result.error}`);
          console.error(`Error creating individual checkup job for day ${result.day}:`, result.error);
        }
      }

      // Small delay between batches to be respectful to the API
      if (i + BATCH_SIZE < jobConfigs.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }


    return {
      totalJobs: successCount,
      errors
    };
  }





  /**
   * Create cron expression for a specific UTC date/time
   */
  public createCronForUTCDateTime(dateTime: Date): string {
    return `${dateTime.getUTCMinutes()} ${dateTime.getUTCHours()} ${dateTime.getUTCDate()} ${dateTime.getUTCMonth() + 1} *`;
  }

  /**
   * Get scheduler client for advanced operations
   */
  getClient(): CloudSchedulerClient {
    return this.client;
  }

  /**
   * Get parent path for job operations
   */
  getParentPath(): string {
    return this.parent;
  }


}
