/**
 * Commit Notification Service
 * Handles notifications for commit reminders, deadlines, and verification results
 */

import { DatabaseService } from './databaseService';
import * as admin from 'firebase-admin';

export interface CommitNotificationResult {
  success: boolean;
  message: string;
  commitId: string;
  userId: string;
  notificationsSent: number;
  action: string;
  error?: string;
}

export interface NotificationData {
  title: string;
  message: string;
  type: 'reminder' | 'urgent' | 'verification' | 'deadline' | 'completion';
  priority: 'low' | 'medium' | 'high';
  data: {
    action: string;
    commitId: string;
    commitTitle: string;
    [key: string]: any;
  };
}

export class CommitNotificationService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = new DatabaseService();
  }

  /**
   * Send daily commit notifications
   */
  async sendDailyCommitNotifications(
    commitId: string,
    userId: string,
    schedulerData?: any
  ): Promise<CommitNotificationResult> {

    try {
      // Get commit and user data
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit) {
        return {
          success: false,
          message: 'Commit not found',
          commitId,
          userId,
          notificationsSent: 0,
          action: 'error',
          error: 'Commit not found'
        };
      }

      const user = await this.dbService.getDocument('users', userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found',
          commitId,
          userId,
          notificationsSent: 0,
          action: 'error',
          error: 'User not found'
        };
      }

      // Use commit timezone if available, fallback to user timezone, then UTC
      const userTimezone = commit.timezone || user.timezone || 'UTC';

      // Check if commit is still active
      if (commit.status !== 'active') {
        return {
          success: true,
          message: 'Commit not active, skipping notifications',
          commitId,
          userId,
          notificationsSent: 0,
          action: 'skipped_inactive'
        };
      }

      let notificationsSent = 0;

      // Handle recurring notification logic
      if (schedulerData?.isRecurring) {
        const result = await this.handleRecurringCommitNotification(commit, user, schedulerData, userTimezone);
        notificationsSent += result.notificationsSent;
      }

      // Send reminder notifications based on deadline proximity
      const reminderResult = await this.sendCommitReminders(commit, user, userTimezone);
      notificationsSent += reminderResult.notificationsSent;

      // Send urgent notifications if deadline is approaching
      if (schedulerData?.urgentRemindersEnabled) {
        const urgentResult = await this.sendUrgentCommitReminders(commit, user, userTimezone);
        notificationsSent += urgentResult.notificationsSent;
      }

      return {
        success: true,
        message: `Daily commit notifications processed`,
        commitId,
        userId,
        notificationsSent,
        action: 'notifications_sent'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error in daily commit notification delivery:`, errorMessage);

      return {
        success: false,
        message: `Daily commit notification delivery failed: ${errorMessage}`,
        commitId,
        userId,
        notificationsSent: 0,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Handle recurring commit notification logic
   */
  private async handleRecurringCommitNotification(
    commit: any,
    user: any,
    schedulerData: any,
    userTimezone: string
  ): Promise<{ notificationsSent: number }> {

    // Check if we should send notification today based on commit frequency
    const shouldSendToday = await this.shouldSendNotificationToday(commit, userTimezone);
    
    if (!shouldSendToday) {
      return { notificationsSent: 0 };
    }

    // Check if user has already submitted today
    const hasSubmittedToday = await this.hasUserSubmittedToday(commit.id, user.id, userTimezone);
    
    if (hasSubmittedToday) {
      return { notificationsSent: 0 };
    }

    // Send reminder notification
    await this.sendReminderNotification(
      user.id,
      commit.title,
      commit.id,
      this.getCurrentDayForCommit(commit, userTimezone)
    );

    return { notificationsSent: 1 };
  }

  /**
   * Send commit reminder notifications
   */
  private async sendCommitReminders(
    commit: any,
    user: any,
    userTimezone: string
  ): Promise<{ notificationsSent: number }> {
    let notificationsSent = 0;

    // Check if reminder should be sent based on deadline timing
    const timeUntilDeadline = this.calculateTimeUntilDeadline(commit, userTimezone);
    
    // Send reminder 2 hours before deadline
    if (timeUntilDeadline > 0 && timeUntilDeadline <= 2 * 60 * 60 * 1000) {
      const hasSubmittedToday = await this.hasUserSubmittedToday(commit.id, user.id, userTimezone);
      
      if (!hasSubmittedToday) {
        await this.sendReminderNotification(
          user.id,
          commit.title,
          commit.id,
          this.getCurrentDayForCommit(commit, userTimezone)
        );
        notificationsSent++;
      }
    }

    return { notificationsSent };
  }

  /**
   * Send urgent commit reminders
   */
  private async sendUrgentCommitReminders(
    commit: any,
    user: any,
    userTimezone: string
  ): Promise<{ notificationsSent: number }> {
    let notificationsSent = 0;

    const timeUntilDeadline = this.calculateTimeUntilDeadline(commit, userTimezone);
    const hoursUntilDeadline = timeUntilDeadline / (60 * 60 * 1000);

    // Send urgent reminder 30 minutes before deadline
    if (hoursUntilDeadline > 0 && hoursUntilDeadline <= 0.5) {
      const hasSubmittedToday = await this.hasUserSubmittedToday(commit.id, user.id, userTimezone);
      
      if (!hasSubmittedToday) {
        await this.sendUrgentReminderNotification(
          user.id,
          commit.title,
          commit.id,
          hoursUntilDeadline
        );
        notificationsSent++;
      }
    }

    return { notificationsSent };
  }

  /**
   * Send reminder notification
   */
  private async sendReminderNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    dayNumber: number
  ): Promise<void> {
    const notificationData: NotificationData = {
      title: "Commitment Reminder",
      message: `Don't forget about your "${commitTitle}" commitment today!`,
      type: "reminder",
      priority: "medium",
      data: {
        action: "submit_commitment",
        commitId: commitId,
        commitTitle: commitTitle,
        dayNumber: dayNumber.toString()
      }
    };

    await this.sendFCMNotification(userId, notificationData);
  }

  /**
   * Send urgent reminder notification
   */
  private async sendUrgentReminderNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    hoursLeft: number
  ): Promise<void> {
    const minutesLeft = Math.round(hoursLeft * 60);
    
    const notificationData: NotificationData = {
      title: "🚨 Urgent: Deadline Approaching!",
      message: `Only ${minutesLeft} minutes left for your "${commitTitle}" commitment. Don't break your streak!`,
      type: "urgent",
      priority: "high",
      data: {
        action: "submit_commitment",
        commitId: commitId,
        commitTitle: commitTitle,
        urgency: "high",
        minutesLeft: minutesLeft.toString()
      }
    };

    await this.sendFCMNotification(userId, notificationData);
  }

  /**
   * Send verification result notification
   */
  async sendVerificationResultNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    verified: boolean,
    verificationType: string,
    details?: any
  ): Promise<void> {
    let notificationData: NotificationData;

    if (verified) {
      notificationData = {
        title: "✅ Verification Successful!",
        message: `Great! Your "${commitTitle}" commitment was automatically verified.`,
        type: "verification",
        priority: "medium",
        data: {
          action: "view_progress",
          commitId: commitId,
          commitTitle: commitTitle,
          verificationType: verificationType,
          verified: "true"
        }
      };
    } else {
      notificationData = {
        title: "❌ Verification Failed",
        message: `We couldn't automatically verify your "${commitTitle}" commitment. You can submit manually.`,
        type: "verification",
        priority: "medium",
        data: {
          action: "submit_commitment",
          commitId: commitId,
          commitTitle: commitTitle,
          verificationType: verificationType,
          verified: "false"
        }
      };
    }

    await this.sendFCMNotification(userId, notificationData);
  }

  /**
   * Send deadline violation notification
   */
  async sendDeadlineViolationNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    hasGracePeriod: boolean = false
  ): Promise<void> {
    const notificationData: NotificationData = {
      title: hasGracePeriod ? "⏳ Grace Period Active" : "😔 Deadline Missed",
      message: hasGracePeriod 
        ? `You missed the deadline for "${commitTitle}", but you have a grace period to submit!`
        : `You missed today's deadline for "${commitTitle}". Your streak has been reset.`,
      type: "deadline",
      priority: "high",
      data: {
        action: hasGracePeriod ? "submit_commitment" : "view_progress",
        commitId: commitId,
        commitTitle: commitTitle,
        hasGracePeriod: hasGracePeriod.toString()
      }
    };

    await this.sendFCMNotification(userId, notificationData);
  }

  /**
   * Send commit completion notification
   */
  async sendCommitCompletionNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    totalDays: number,
    completionRate: number
  ): Promise<void> {
    const notificationData: NotificationData = {
      title: "🎉 Commitment Completed!",
      message: `Congratulations! You've completed your "${commitTitle}" commitment with a ${Math.round(completionRate * 100)}% success rate!`,
      type: "completion",
      priority: "medium",
      data: {
        action: "view_achievement",
        commitId: commitId,
        commitTitle: commitTitle,
        totalDays: totalDays.toString(),
        completionRate: completionRate.toString()
      }
    };

    await this.sendFCMNotification(userId, notificationData);
  }

  /**
   * Send FCM notification directly using Firebase Admin
   */
  private async sendFCMNotification(userId: string, notificationData: NotificationData): Promise<void> {
    try {
      // Get FCM tokens for the user
      const tokensSnapshot = await admin.firestore()
        .collection('users')
        .doc(userId)
        .collection('fcmTokens')
        .get();

      if (tokensSnapshot.empty) {
        return;
      }

      // Extract tokens
      const tokens = tokensSnapshot.docs.map(doc => doc.data().token);

      // Send FCM message
      const fcmMessage: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: notificationData.title,
          body: notificationData.message,
        },
        data: notificationData.data || {},
        android: {
          notification: {
            channelId: 'commit-reminders',
            priority: 'high' as const,
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: notificationData.title,
                body: notificationData.message,
              },
              badge: 1,
              sound: 'default',
            },
          },
        },
      };

      const response = await admin.messaging().sendMulticast(fcmMessage);

      if (response.failureCount > 0) {
        console.error(`❌ Failed to send FCM notifications to ${response.failureCount} devices`);
      }
    } catch (error) {
      console.error(`❌ Failed to send FCM notification to user ${userId}:`, error);
    }
  }

  /**
   * Check if notification should be sent today based on commit frequency
   */
  private async shouldSendNotificationToday(commit: any, timezone: string): Promise<boolean> {
    // const now = new Date();
    // const userDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
    
    switch (commit.schedule?.frequency) {
      case 'daily':
        return true; // Always send for daily commits
      
      case 'weekly':
        // Check if user hasn't completed weekly target AND hasn't submitted today
        const weeklyTarget = commit.schedule.timesPerWeek || 1;
        const currentWeekSubmissions = await this.getCurrentWeekSubmissions(commit.id, commit.userId);

        // Don't send notification if weekly target is already met
        if (currentWeekSubmissions >= weeklyTarget) {
          return false;
        }

        // Check if user has already submitted today (same logic as frontend)
        const hasSubmittedToday = await this.hasUserSubmittedTodayInCurrentWeek(commit.id, commit.userId, timezone);
        if (hasSubmittedToday) {
          return false;
        }

        return true;
      
      case 'monthly':
        // Smart monthly notification strategy - not every day!
        const monthlyTarget = commit.schedule.timesPerMonth || 1;
        const currentMonthSubmissions = await this.getCurrentMonthSubmissions(commit.id, commit.userId);

        // Don't send notification if monthly target is already met
        if (currentMonthSubmissions >= monthlyTarget) {
          return false;
        }

        // Check if user has already submitted today
        const hasSubmittedTodayMonthly = await this.hasUserSubmittedTodayInCurrentMonth(commit.id, commit.userId, timezone);
        if (hasSubmittedTodayMonthly) {
          return false;
        }

        // Smart frequency: Only send notifications on specific days to avoid spam
        const shouldSendMonthlyNotification = await this.shouldSendMonthlyNotificationToday(
          commit,
          currentMonthSubmissions,
          monthlyTarget,
          timezone
        );

        if (shouldSendMonthlyNotification) {
          return true;
        } else {
          return false;
        }
      
      case 'once':
        // Smart one-time commit notification logic
        const hasCompletedOnce = await this.hasCompletedOnceCommit(commit.id, commit.userId);

        // Don't send notification if already completed
        if (hasCompletedOnce) {
          return false;
        }

        // Check if user has already submitted today (avoid spam)
        const hasSubmittedTodayOnce = await this.hasUserSubmittedTodayForOnce(commit.id, commit.userId, timezone);
        if (hasSubmittedTodayOnce) {
          return false;
        }

        // Smart timing for one-time commits based on deadline proximity
        const shouldSendOnceNotification = await this.shouldSendOnceNotificationToday(commit, timezone);

        if (shouldSendOnceNotification) {
          return true;
        } else {
          return false;
        }
      
      default:
        return true;
    }
  }

  /**
   * Check if user has submitted today
   */
  private async hasUserSubmittedToday(commitId: string, userId: string, timezone: string): Promise<boolean> {
    const today = new Date().toLocaleDateString('en-CA', { timeZone: timezone }); // YYYY-MM-DD format
    
    // This would check the commit submissions for today
    // Implementation depends on your submission storage structure
    const submissions = await this.dbService.getCollection(`commits/${commitId}/submissions`);
    
    return submissions.some((submission: any) => 
      submission.timestamp && 
      submission.timestamp.split('T')[0] === today &&
      submission.status === 'submitted'
    );
  }

  /**
   * Calculate time until deadline in milliseconds
   * Fixed to properly handle timezone conversions and avoid date boundary issues
   */
  private calculateTimeUntilDeadline(commit: any, timezone: string): number {
    try {
      const now = new Date();

      // Get current time in user's timezone using proper timezone handling
      const userNowString = now.toLocaleString("en-US", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });

      // Parse the timezone-adjusted time
      const [datePart, timePart] = userNowString.split(', ');
      const [month, day, year] = datePart.split('/');
      const [hour, minute, second] = timePart.split(':');

      const userNow = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );

      let deadlineTime: Date;

      switch (commit.schedule?.deadline?.type) {
        case 'before':
          if (commit.schedule.deadline.time) {
            const [deadlineHour, deadlineMinute] = commit.schedule.deadline.time.split(':').map(Number);
            deadlineTime = new Date(userNow);
            deadlineTime.setHours(deadlineHour, deadlineMinute, 0, 0);

            // If time has passed today, it's for tomorrow
            if (deadlineTime <= userNow) {
              deadlineTime.setDate(deadlineTime.getDate() + 1);
            }
          } else {
            deadlineTime = new Date(userNow);
            deadlineTime.setHours(23, 59, 59, 999);
          }
          break;
        
        case 'after':
          if (commit.schedule.deadline.time) {
            const [deadlineHour, deadlineMinute] = commit.schedule.deadline.time.split(':').map(Number);
            deadlineTime = new Date(userNow);
            deadlineTime.setHours(deadlineHour, deadlineMinute, 0, 0);
            deadlineTime.setDate(deadlineTime.getDate() + 1); // Next day
          } else {
            deadlineTime = new Date(userNow);
            deadlineTime.setHours(23, 59, 59, 999);
          }
          break;

        case 'between':
          if (commit.schedule.deadline.endTime) {
            const [deadlineHour, deadlineMinute] = commit.schedule.deadline.endTime.split(':').map(Number);
            deadlineTime = new Date(userNow);
            deadlineTime.setHours(deadlineHour, deadlineMinute, 0, 0);

            if (deadlineTime <= userNow) {
              deadlineTime.setDate(deadlineTime.getDate() + 1);
            }
          } else {
            deadlineTime = new Date(userNow);
            deadlineTime.setHours(23, 59, 59, 999);
          }
          break;

        case 'midnight':
        default:
          deadlineTime = new Date(userNow);
          deadlineTime.setHours(23, 59, 59, 999);
          break;
      }

      return deadlineTime.getTime() - userNow.getTime();
    } catch (error) {
      console.error(`Error calculating time until deadline for timezone ${timezone}:`, error);
      // Fallback to 24 hours
      return 24 * 60 * 60 * 1000;
    }
  }

  /**
   * Get current day number for commit
   * Fixed to properly handle timezone conversions for accurate day calculation
   */
  private getCurrentDayForCommit(commit: any, timezone: string): number {
    if (!commit.schedule?.startDate) return 1;

    try {
      const startDate = new Date(commit.schedule.startDate);
      const now = new Date();

      // Get current date in user's timezone (date only, no time)
      const userDateString = now.toLocaleDateString("en-CA", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      const startDateString = startDate.toLocaleDateString("en-CA", {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });

      // Calculate difference in days using date strings to avoid timezone issues
      const userDate = new Date(userDateString + 'T00:00:00');
      const commitStartDate = new Date(startDateString + 'T00:00:00');

      const daysDiff = Math.floor((userDate.getTime() - commitStartDate.getTime()) / (1000 * 60 * 60 * 24));
      return Math.max(1, daysDiff + 1);
    } catch (error) {
      console.error(`Error calculating current day for commit in timezone ${timezone}:`, error);
      // Fallback calculation
      const startDate = new Date(commit.schedule.startDate);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return Math.max(1, daysDiff + 1);
    }
  }

  /**
   * Get current commitment week submissions count (synchronized with frontend)
   */
  private async getCurrentWeekSubmissions(commitId: string, userId: string): Promise<number> {
    try {
      // Get commit data to determine current commitment week
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit || !commit.schedule) {
        console.warn(`⚠️ Commit ${commitId} not found or missing schedule`);
        return 0;
      }

      // Calculate current commitment week (same logic as frontend)
      const currentWeekId = this.getCurrentCommitmentWeekId(commit);
      if (!currentWeekId) {
        return 0;
      }

      // Get submissions for the current commitment week from the hierarchical structure
      const weekSubmissionsPath = `commits/${commitId}/submissions/${currentWeekId}/submissions`;
      const weekSubmissions = await this.dbService.getCollection(weekSubmissionsPath);

      if (!weekSubmissions || weekSubmissions.length === 0) {
        return 0;
      }

      // Count submitted submissions in current commitment week
      const submittedCount = weekSubmissions.filter(submission =>
        submission.status === 'submitted'
      ).length;

      return submittedCount;

    } catch (error) {
      console.error(`❌ Failed to get current week submissions for commit ${commitId}:`, error);
      return 0;
    }
  }

  /**
   * Calculate current commitment week ID (synchronized with frontend logic)
   */
  private getCurrentCommitmentWeekId(commit: any): string | null {
    try {
      const now = new Date();
      const startDate = new Date(commit.schedule.startDate);
      const endDate = commit.schedule.endDate ? new Date(commit.schedule.endDate) : null;

      // Check if we're within the commitment period
      if (now < startDate) {
        return null; // Commitment hasn't started yet
      }

      if (endDate && now > endDate) {
        return null; // Commitment has ended
      }

      // Calculate which commitment week we're in
      const daysDiff = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const weekNumber = Math.floor(daysDiff / 7) + 1;

      // Validate week number against commitment duration
      const totalWeeks = commit.schedule.duration || Math.ceil(
        endDate ? (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7) : 1
      );

      if (weekNumber > totalWeeks) {
        return null; // Beyond commitment duration
      }

      return `Week ${weekNumber}`;

    } catch (error) {
      console.error(`❌ Failed to calculate current commitment week:`, error);
      return null;
    }
  }

  /**
   * Check if user has submitted today in current commitment week (synchronized with frontend)
   */
  private async hasUserSubmittedTodayInCurrentWeek(commitId: string, userId: string, timezone: string): Promise<boolean> {
    try {
      // Get commit data to determine current commitment week
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit || !commit.schedule) {
        return false;
      }

      // Calculate current commitment week
      const currentWeekId = this.getCurrentCommitmentWeekId(commit);
      if (!currentWeekId) {
        return false; // Not in an active week
      }

      // Get today's date in user's timezone (YYYY-MM-DD format)
      const now = new Date();
      const userDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
      const todayDateString = userDate.toISOString().split('T')[0];

      // Get submissions for the current commitment week
      const weekSubmissionsPath = `commits/${commitId}/submissions/${currentWeekId}/submissions`;
      const weekSubmissions = await this.dbService.getCollection(weekSubmissionsPath);

      if (!weekSubmissions || weekSubmissions.length === 0) {
        return false; // No submissions in current week
      }

      // Check if any submission was made today
      const todaySubmissionExists = weekSubmissions.some(submission =>
        submission.status === 'submitted' &&
        submission.timestamp &&
        submission.timestamp.split('T')[0] === todayDateString
      );

      return todaySubmissionExists;

    } catch (error) {
      console.error(`❌ Failed to check today's submission for commit ${commitId}:`, error);
      return false;
    }
  }

  /**
   * Get current commitment month submissions count (synchronized with frontend)
   */
  private async getCurrentMonthSubmissions(commitId: string, userId: string): Promise<number> {
    try {
      // Get commit data to determine current commitment month
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit || !commit.schedule) {
        console.warn(`⚠️ Commit ${commitId} not found or missing schedule`);
        return 0;
      }

      // Calculate current commitment month (same logic as frontend)
      const currentMonthId = this.getCurrentCommitmentMonthId(commit);
      if (!currentMonthId) {
        return 0;
      }

      // Get submissions for the current commitment month from the hierarchical structure
      const monthSubmissionsPath = `commits/${commitId}/submissions/${currentMonthId}/submissions`;
      const monthSubmissions = await this.dbService.getCollection(monthSubmissionsPath);

      if (!monthSubmissions || monthSubmissions.length === 0) {
        return 0;
      }

      // Count submitted submissions in current commitment month
      const submittedCount = monthSubmissions.filter(submission =>
        submission.status === 'submitted'
      ).length;

      return submittedCount;

    } catch (error) {
      console.error(`❌ Failed to get current month submissions for commit ${commitId}:`, error);
      return 0;
    }
  }

  /**
   * Calculate current commitment month ID (synchronized with frontend logic)
   */
  private getCurrentCommitmentMonthId(commit: any): string | null {
    try {
      const now = new Date();
      const startDate = new Date(commit.schedule.startDate);
      const endDate = commit.schedule.endDate ? new Date(commit.schedule.endDate) : null;

      // Check if we're within the commitment period
      if (now < startDate) {
        return null; // Commitment hasn't started yet
      }

      if (endDate && now > endDate) {
        return null; // Commitment has ended
      }

      // Calculate which commitment month we're in
      const daysDiff = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const monthNumber = Math.floor(daysDiff / 30) + 1; // Approximate 30 days per month

      // Validate month number against commitment duration
      const totalMonths = commit.schedule.duration || Math.ceil(
        endDate ? (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30) : 1
      );

      if (monthNumber > totalMonths) {
        return null; // Beyond commitment duration
      }

      return `Month ${monthNumber}`;

    } catch (error) {
      console.error(`❌ Failed to calculate current commitment month:`, error);
      return null;
    }
  }

  /**
   * Check if user has submitted today in current commitment month (synchronized with frontend)
   */
  private async hasUserSubmittedTodayInCurrentMonth(commitId: string, userId: string, timezone: string): Promise<boolean> {
    try {
      // Get commit data to determine current commitment month
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit || !commit.schedule) {
        return false;
      }

      // Calculate current commitment month
      const currentMonthId = this.getCurrentCommitmentMonthId(commit);
      if (!currentMonthId) {
        return false; // Not in an active month
      }

      // Get today's date in user's timezone (YYYY-MM-DD format)
      const now = new Date();
      const userDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
      const todayDateString = userDate.toISOString().split('T')[0];

      // Get submissions for the current commitment month
      const monthSubmissionsPath = `commits/${commitId}/submissions/${currentMonthId}/submissions`;
      const monthSubmissions = await this.dbService.getCollection(monthSubmissionsPath);

      if (!monthSubmissions || monthSubmissions.length === 0) {
        return false; // No submissions in current month
      }

      // Check if any submission was made today
      const todaySubmissionExists = monthSubmissions.some(submission =>
        submission.status === 'submitted' &&
        submission.timestamp &&
        submission.timestamp.split('T')[0] === todayDateString
      );

      return todaySubmissionExists;

    } catch (error) {
      console.error(`❌ Failed to check today's submission for monthly commit ${commitId}:`, error);
      return false;
    }
  }

  /**
   * Smart monthly notification timing - avoid daily spam
   */
  private async shouldSendMonthlyNotificationToday(
    commit: any,
    currentSubmissions: number,
    targetSubmissions: number,
    timezone: string
  ): Promise<boolean> {
    try {
      const now = new Date();
      const userDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
      const dayOfMonth = userDate.getDate();
      const daysInMonth = new Date(userDate.getFullYear(), userDate.getMonth() + 1, 0).getDate();

      // Calculate how many days are left in the month
      const daysRemaining = daysInMonth - dayOfMonth + 1;
      const submissionsNeeded = targetSubmissions - currentSubmissions;

      // Strategy based on target frequency
      if (targetSubmissions === 1) {
        // 1x per month: Send notifications on strategic days
        const strategicDays = [1, 7, 14, 21, 28]; // Weekly reminders
        const urgentDays = daysInMonth >= 30 ? [29, 30] : [daysInMonth - 1, daysInMonth]; // Last 2 days

        return strategicDays.includes(dayOfMonth) || urgentDays.includes(dayOfMonth);

      } else if (targetSubmissions === 2) {
        // 2x per month: More frequent but still strategic
        if (currentSubmissions === 0) {
          // No submissions yet - remind weekly + urgency in last week
          const strategicDays = [1, 7, 14, 21]; // Weekly reminders
          const urgentDays = dayOfMonth > 21 ? [22, 24, 26, 28] : []; // Every other day in last week

          return strategicDays.includes(dayOfMonth) || urgentDays.includes(dayOfMonth);

        } else if (currentSubmissions === 1) {
          // One submission done - remind less frequently unless urgent
          if (daysRemaining <= 7) {
            // Last week - every other day
            return dayOfMonth % 2 === 0;
          } else {
            // Still time - weekly reminders
            return [7, 14, 21].includes(dayOfMonth);
          }
        }

      } else {
        // 3+ per month: More frequent reminders needed
        if (submissionsNeeded > daysRemaining) {
          // Urgent: Need more submissions than days left
          return true; // Daily notifications
        } else if (submissionsNeeded * 2 > daysRemaining) {
          // Semi-urgent: Every other day
          return dayOfMonth % 2 === 0;
        } else {
          // Comfortable pace: Twice weekly
          return [1, 4, 8, 11, 15, 18, 22, 25, 29].includes(dayOfMonth);
        }
      }

      return false;

    } catch (error) {
      console.error(`❌ Failed to determine monthly notification timing:`, error);
      // Fallback to weekly notifications
      const now = new Date();
      const userDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
      return [1, 8, 15, 22, 29].includes(userDate.getDate());
    }
  }

  /**
   * Check if one-time commit has been completed
   */
  private async hasCompletedOnceCommit(commitId: string, userId: string): Promise<boolean> {
    try {
      // For "once" commits, check if "Day 1" submission exists and is completed
      const dayOneSubmissionPath = `commits/${commitId}/submissions`;
      const dayOneSubmission = await this.dbService.getDocument(dayOneSubmissionPath, 'Day 1');

      if (!dayOneSubmission) {
        return false; // No submission document exists yet
      }

      const isCompleted = dayOneSubmission.status === 'submitted';
      return isCompleted;

    } catch (error) {
      console.error(`❌ Failed to check one-time commit completion for ${commitId}:`, error);
      return false;
    }
  }

  /**
   * Check if user has submitted today for one-time commit
   * Fixed to properly handle timezone conversions for accurate date comparison
   */
  private async hasUserSubmittedTodayForOnce(commitId: string, userId: string, timezone: string): Promise<boolean> {
    try {
      // Get today's date in user's timezone (YYYY-MM-DD format)
      const now = new Date();
      const todayDateString = now.toLocaleDateString("en-CA", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      // Check if "Day 1" submission was made today
      const dayOneSubmissionPath = `commits/${commitId}/submissions`;
      const dayOneSubmission = await this.dbService.getDocument(dayOneSubmissionPath, 'Day 1');

      if (!dayOneSubmission || dayOneSubmission.status !== 'submitted') {
        return false; // No submission or not submitted
      }

      // Check if submission was made today
      const submissionDate = dayOneSubmission.timestamp ? dayOneSubmission.timestamp.split('T')[0] : null;
      const submittedToday = submissionDate === todayDateString;

      return submittedToday;

    } catch (error) {
      console.error(`❌ Failed to check today's submission for one-time commit ${commitId}:`, error);
      return false;
    }
  }

  /**
   * Smart timing for one-time commit notifications
   * Fixed to properly handle timezone conversions for accurate date comparisons
   */
  private async shouldSendOnceNotificationToday(commit: any, timezone: string): Promise<boolean> {
    try {
      const now = new Date();

      // Get current date in user's timezone (date only, no time)
      const userDateString = now.toLocaleDateString("en-CA", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      const userDate = new Date(userDateString + 'T00:00:00');

      // Get commit dates (normalized to date-only)
      const startDate = new Date(commit.schedule.startDate + 'T00:00:00');
      const endDate = commit.schedule.endDate ? new Date(commit.schedule.endDate + 'T00:00:00') : null;

      // Check if we're within the commitment period using proper date comparison
      if (userDate.getTime() < startDate.getTime()) {
        return false; // Commitment hasn't started yet
      }

      if (endDate && userDate.getTime() > endDate.getTime()) {
        return false; // Commitment has ended
      }

      // For one-time commits, use strategic notification timing
      if (endDate) {
        // Calculate days until deadline
        const daysUntilDeadline = Math.ceil((endDate.getTime() - userDate.getTime()) / (1000 * 60 * 60 * 24));

        if (daysUntilDeadline <= 0) {
          return false; // Past deadline
        } else if (daysUntilDeadline === 1) {
          return true; // Last day - definitely send
        } else if (daysUntilDeadline <= 3) {
          return true; // Final 3 days - send daily
        } else if (daysUntilDeadline <= 7) {
          return userDate.getDay() % 2 === 0; // Final week - every other day
        } else if (daysUntilDeadline <= 14) {
          return [1, 4, 7].includes(userDate.getDay()); // Twice weekly
        } else {
          return userDate.getDay() === 1; // Weekly on Mondays
        }
      } else {
        // No end date - send weekly reminders on Mondays
        return userDate.getDay() === 1;
      }

    } catch (error) {
      console.error(`❌ Failed to determine one-time notification timing:`, error);
      // Fallback to weekly notifications on Mondays
      const now = new Date();
      const userDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
      return userDate.getDay() === 1;
    }
  }
}
