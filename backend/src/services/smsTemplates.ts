/**
 * SMS Templates for Accustom Notifications
 * Provides text templates for different types of SMS notifications
 * Optimized for SMS character limits (160 characters)
 */

export interface SMSTemplateData {
  userName: string;
  programName?: string;
  dayNumber?: number;
  streakCount?: number;
  pointsEarned?: number;
  hoursLeft?: number;
  actionUrl?: string;
  [key: string]: any;
}

export class SMSTemplates {
  
  /**
   * Welcome SMS template
   */
  static getWelcomeTemplate(data: SMSTemplateData): string {
    return `Welcome to Accustom, ${data.userName}. Your journey toward lasting change begins now. Reply STOP to opt out.`;
  }

  /**
   * Daily reminder SMS template
   */
  static getReminderTemplate(data: SMSTemplateData): string {
    const programName = data.programName || 'your program';
    const dayNumber = data.dayNumber || 1;

    return `Day ${dayNumber} of "${programName}" awaits your attention. A moment of progress today builds tomorrow's success.`;
  }

  /**
   * Urgent reminder SMS template
   */
  static getUrgentReminderTemplate(data: SMSTemplateData): string {
    const programName = data.programName || 'your program';
    const hoursLeft = data.hoursLeft || 'few';

    return `${hoursLeft}h remaining to log "${programName}" progress. Your consistency matters more than perfection.`;
  }

  /**
   * Streak milestone SMS template
   */
  static getStreakMilestoneTemplate(data: SMSTemplateData): string {
    const streakCount = data.streakCount || 1;
    const programName = data.programName || 'your program';

    return `${streakCount} consecutive days of "${programName}" completed. Discipline is becoming your default.`;
  }

  /**
   * Points earned SMS template
   */
  static getPointsEarnedTemplate(data: SMSTemplateData): string {
    const points = data.pointsEarned || 0;
    const programName = data.programName || 'your program';

    return `${points} points earned from "${programName}". Progress measured, progress made.`;
  }

  /**
   * Program completion SMS template
   */
  static getProgramCompletionTemplate(data: SMSTemplateData): string {
    const programName = data.programName || 'your program';

    return `"${programName}" completed. You've proven that commitment creates results. Ready for your next challenge?`;
  }

  /**
   * Missed day SMS template
   */
  static getMissedDayTemplate(data: SMSTemplateData): string {
    const programName = data.programName || 'your program';

    return `"${programName}" missed today. Tomorrow offers a fresh start. One day doesn't define your journey.`;
  }

  /**
   * Program started SMS template
   */
  static getProgramStartedTemplate(data: SMSTemplateData): string {
    const programName = data.programName || 'your new program';

    return `"${programName}" begins today. Every expert was once a beginner. Your first step matters.`;
  }

  /**
   * Weekly progress SMS template
   */
  static getWeeklyProgressTemplate(data: SMSTemplateData): string {
    const programName = data.programName || 'your program';
    const completedDays = data.completedDays || 0;
    const totalDays = data.totalDays || 7;

    return `"${programName}" weekly summary: ${completedDays}/${totalDays} days completed. Steady progress builds lasting change.`;
  }

  /**
   * Account verification SMS template
   */
  static getVerificationTemplate(data: SMSTemplateData): string {
    const code = data.verificationCode || '000000';

    return `Your Accustom verification code: ${code}. Enter this code to verify your phone number.`;
  }

  /**
   * Password reset SMS template
   */
  static getPasswordResetTemplate(data: SMSTemplateData): string {
    return `Accustom password reset requested. If this wasn't you, ignore this message. Check your email for the reset link.`;
  }

  /**
   * Generic notification SMS template
   */
  static getGenericTemplate(data: SMSTemplateData): string {
    const title = data.title || 'Accustom Update';
    const message = data.message || 'You have a new notification.';

    // Combine title and message, keeping within SMS limits
    const combined = `${title}: ${message}`;

    // Truncate if too long (leaving space for "...")
    if (combined.length > 157) {
      return combined.substring(0, 154) + '...';
    }

    return combined;
  }

  /**
   * Test SMS template
   */
  static getTestTemplate(data: SMSTemplateData): string {
    return `Test message from Accustom. SMS notifications are working correctly. Reply STOP to opt out.`;
  }

  /**
   * Get template by type
   */
  static getTemplateByType(type: string, data: SMSTemplateData): string {
    switch (type) {
      case 'welcome':
        return this.getWelcomeTemplate(data);
      case 'reminder':
        return this.getReminderTemplate(data);
      case 'urgent_reminder':
        return this.getUrgentReminderTemplate(data);
      case 'streak_milestone':
        return this.getStreakMilestoneTemplate(data);
      case 'points_earned':
        return this.getPointsEarnedTemplate(data);
      case 'program_completion':
        return this.getProgramCompletionTemplate(data);
      case 'missed_day':
        return this.getMissedDayTemplate(data);
      case 'program_started':
        return this.getProgramStartedTemplate(data);
      case 'weekly_progress':
        return this.getWeeklyProgressTemplate(data);
      case 'verification':
        return this.getVerificationTemplate(data);
      case 'password_reset':
        return this.getPasswordResetTemplate(data);
      case 'test':
        return this.getTestTemplate(data);
      default:
        return this.getGenericTemplate(data);
    }
  }

  /**
   * Validate SMS message length
   */
  static validateMessageLength(message: string): { valid: boolean; length: number; maxLength: number } {
    const maxLength = 160;
    return {
      valid: message.length <= maxLength,
      length: message.length,
      maxLength
    };
  }

  /**
   * Truncate message to fit SMS limits
   */
  static truncateMessage(message: string, maxLength: number = 160): string {
    if (message.length <= maxLength) {
      return message;
    }
    
    return message.substring(0, maxLength - 3) + '...';
  }

  /**
   * Add opt-out message to SMS
   */
  static addOptOutMessage(message: string): string {
    const optOut = ' Reply STOP to opt out.';
    const maxLength = 160 - optOut.length;
    
    if (message.length > maxLength) {
      message = message.substring(0, maxLength - 3) + '...';
    }
    
    return message + optOut;
  }
}
