/**
 * Backend Notification Service for Cloud Functions
 * Handles daily notification delivery and scheduling
 */

import { DatabaseService } from './databaseService';
import { NotificationDispatcher } from './notificationDispatcher';
import { ContextualMessagingService, MessageContext } from './contextualMessaging';
import * as admin from 'firebase-admin';

export interface DailyNotificationResult {
  success: boolean;
  message: string;
  programId: string;
  userId: string;
  participantName?: string;
  action: 'notification_sent' | 'program_not_started' | 'program_ended' | 'no_notification_needed' | 'error';
  notificationsSent?: number;
  error?: string;
}

export class NotificationService {
  private dbService: DatabaseService;
  private notificationDispatcher: NotificationDispatcher;

  constructor() {
    this.dbService = new DatabaseService();
    this.notificationDispatcher = new NotificationDispatcher();
  }

  /**
   * Send daily notifications for a participant
   * Called by Cloud Scheduler at specified times
   */
  async sendDailyNotifications(
    programId: string,
    userId: string,
    schedulerData?: any
  ): Promise<DailyNotificationResult> {

    try {
      // Get participant details
      const participant = await this.dbService.getParticipant(programId, userId);

      console.log(`📬 Processing notification for participant:`, {
        id: participant.id,
        fname: participant.fname,
        personalProgramStatus: participant.personalProgramStatus,
        personalStartDate: participant.personalStartDate,
        personalEndDate: participant.personalEndDate,
        timezone: participant.timezone
      });

      // Check if participant has individual program status fields
      if (!participant.personalProgramStatus) {
        return {
          success: false,
          message: 'Participant missing individual program status fields',
          programId,
          userId,
          action: 'error',
          error: 'Missing individual status'
        };
      }

      // Handle recurring notification logic
      if (schedulerData?.isRecurring) {
        const result = await this.handleRecurringNotification(participant, programId, schedulerData);
        if (result) {
          return result;
        }
      }

      // Default notification logic for non-recurring
      const result = await this.sendParticipantNotifications(participant, programId);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error in daily notification delivery for user ${userId}:`, errorMessage);
      console.error(`📋 Error stack:`, error);

      return {
        success: false,
        message: `Daily notification delivery failed: ${errorMessage}`,
        programId,
        userId,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Handle recurring notification logic - determines if notifications should be sent today
   */
  private async handleRecurringNotification(
    participant: any,
    programId: string,
    schedulerData: any
  ): Promise<DailyNotificationResult | null> {
    const now = new Date();
    const personalStartDate = new Date(schedulerData.personalStartDate);
    const personalEndDate = new Date(schedulerData.personalEndDate);

    console.log(`🕐 Notification timing check for user ${participant.id}:`, {
      now: now.toISOString(),
      personalStartDate: personalStartDate.toISOString(),
      personalEndDate: personalEndDate.toISOString(),
      timezone: participant.timezone,
      nowInUserTz: now.toLocaleString("en-US", { timeZone: participant.timezone }),
      startDateInUserTz: personalStartDate.toLocaleString("en-US", { timeZone: participant.timezone })
    });

    // Check if we're within the program duration for this user
    // Use user's timezone for date comparison with improved timezone handling
    const userTimezone = participant.timezone || 'UTC';

    // Get current date in user's timezone using proper timezone conversion
    const nowDateInUserTz = this.getDateInTimezone(now, userTimezone);

    // Get program start/end dates in user's timezone
    const startDateOnlyInUserTz = this.getDateInTimezone(personalStartDate, userTimezone);
    const endDateOnlyInUserTz = this.getDateInTimezone(personalEndDate, userTimezone);

    console.log(`🕐 Notification timing check for user ${participant.id}:`, {
      nowDateInUserTz: nowDateInUserTz.toISOString(),
      startDateOnlyInUserTz: startDateOnlyInUserTz.toISOString(),
      endDateOnlyInUserTz: endDateOnlyInUserTz.toISOString(),
      isBeforeStart: nowDateInUserTz.getTime() < startDateOnlyInUserTz.getTime(),
      isAfterEnd: nowDateInUserTz.getTime() > endDateOnlyInUserTz.getTime(),
      userTimezone: userTimezone
    });

    // Use proper date comparison to avoid timezone conversion issues
    if (nowDateInUserTz.getTime() < startDateOnlyInUserTz.getTime()) {
      return {
        success: true,
        message: `Program hasn't started yet for user ${participant.fname} (starts ${startDateOnlyInUserTz.toDateString()})`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'program_not_started'
      };
    }

    if (nowDateInUserTz.getTime() > endDateOnlyInUserTz.getTime()) {
      return {
        success: true,
        message: `Program has ended for user ${participant.fname} (ended ${endDateOnlyInUserTz.toDateString()})`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'program_ended'
      };
    }


    // Check if user's program is ongoing
    if (participant.personalProgramStatus !== 'ongoing') {
      return {
        success: true,
        message: `User program status is ${participant.personalProgramStatus}, no notifications needed. Change participant status to 'ongoing' to receive notifications.`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'no_notification_needed'
      };
    }

    // Send notifications for ongoing program
    return await this.sendParticipantNotifications(participant, programId);
  }

  /**
   * Send appropriate notifications to a participant
   */
  private async sendParticipantNotifications(
    participant: any,
    programId: string
  ): Promise<DailyNotificationResult> {
    let notificationsSent = 0;

    try {
      // Get program details
      const program = await this.dbService.getProgram(programId);
      
      // Calculate current day for the participant
      const personalStartDate = new Date(participant.personalStartDate);
      const now = new Date();
      const daysSinceStart = Math.floor((now.getTime() - personalStartDate.getTime()) / (1000 * 60 * 60 * 24));
      const currentDay = daysSinceStart + 1;

      // Check if participant has submitted today
      const hasSubmittedToday = await this.checkTodaySubmission(programId, participant.id, currentDay);

      if (!hasSubmittedToday) {
        // Send reminder notification
        await this.sendReminderNotification(participant.id, program.name, programId, currentDay);
        notificationsSent++;

        // If it's close to deadline, send urgent notification
        const hoursUntilDeadline = this.calculateHoursUntilDeadline(participant.timezone);
        if (hoursUntilDeadline <= 2 && hoursUntilDeadline > 0) {
          await this.sendUrgentReminderNotification(participant.id, program.name, programId, hoursUntilDeadline);
          notificationsSent++;
        }
      }

      return {
        success: true,
        message: `Daily notifications processed for ${participant.fname}`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'notification_sent',
        notificationsSent
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error sending notifications to participant ${participant.id}:`, errorMessage);
      
      return {
        success: false,
        message: `Failed to send notifications: ${errorMessage}`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Check if participant has submitted for today
   */
  private async checkTodaySubmission(programId: string, userId: string, dayNumber: number): Promise<boolean> {
    try {
      const submission = await this.dbService.getParticipantSubmission(programId, userId, dayNumber);
      return submission !== null && submission.status === 'submitted';
    } catch (error) {
      console.error(`Error checking today's submission for user ${userId}:`, error);
      return false; // Assume not submitted if we can't check
    }
  }

  /**
   * Send reminder notification using multi-channel dispatcher with contextual messaging
   */
  private async sendReminderNotification(
    userId: string,
    programName: string,
    programId: string,
    dayNumber: number
  ): Promise<void> {
    // Get user data for context
    const userData = await this.dbService.getDocument('users', userId);
    const userName = userData?.fname || 'there';
    const timezone = userData?.timezone || 'UTC';

    // Build message context
    const context: MessageContext = {
      userName,
      programName,
      dayNumber,
      timeOfDay: ContextualMessagingService.getTimeOfDay(timezone),
      timezone
    };

    // Generate contextual message
    const contextualMessage = ContextualMessagingService.generateReminderMessage(context);

    const notification = {
      title: contextualMessage.title,
      message: contextualMessage.message,
      type: "reminder" as const,
      priority: "medium" as const,
      data: {
        action: "submit_progress",
        programId: programId,
        programName: programName,
        dayNumber: dayNumber.toString()
      }
    };

    // Use multi-channel dispatcher to send to all enabled channels
    await this.notificationDispatcher.dispatchNotification(userId, notification);
  }

  /**
   * Send urgent reminder notification using multi-channel dispatcher
   */
  private async sendUrgentReminderNotification(
    userId: string,
    programName: string,
    programId: string,
    hoursLeft: number
  ): Promise<void> {
    const notification = {
      title: "Deadline Approaching",
      message: `${hoursLeft}h remaining to log "${programName}" progress. Your consistency matters.`,
      type: "reminder" as const,
      priority: "high" as const,
      data: {
        action: "submit_progress",
        programId: programId,
        programName: programName,
        urgency: "high",
        hoursLeft: hoursLeft.toString()
      }
    };

    // Use multi-channel dispatcher to send to all enabled channels
    await this.notificationDispatcher.dispatchNotification(userId, notification);
  }

  /**
   * Send FCM notification using the existing FCM system
   */
  private async sendFCMNotification(userId: string, notificationData: any): Promise<void> {
    try {
      // Get FCM tokens for the user
      const tokensSnapshot = await admin.firestore()
        .collection('users')
        .doc(userId)
        .collection('fcmTokens')
        .get();

      if (tokensSnapshot.empty) {
        return;
      }

      // Extract tokens
      const tokens = tokensSnapshot.docs.map(doc => doc.data().token);

      // Separate Expo tokens from FCM tokens
      const expoTokens = tokens.filter(token => token.startsWith('ExponentPushToken['));
      const fcmTokens = tokens.filter(token => !token.startsWith('ExponentPushToken['));

      // Send to Expo tokens using Expo's push service
      if (expoTokens.length > 0) {
        await this.sendExpoNotifications(expoTokens, notificationData);
      }

      // Send to FCM tokens using Firebase messaging
      if (fcmTokens.length > 0) {
        await this.sendFCMMessages(fcmTokens, notificationData);
      }

    } catch (error) {
      console.error('Error sending FCM notification:', error);
      throw error;
    }
  }

  /**
   * Send notifications to Expo tokens
   */
  private async sendExpoNotifications(tokens: string[], notification: any): Promise<void> {
    try {
      const messages = tokens.map(token => ({
        to: token,
        title: notification.title,
        body: notification.message,
        data: notification.data,
        sound: 'default',
        badge: 1,
      }));

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messages),
      });

      if (!response.ok) {
        throw new Error(`Expo push service error: ${response.status}`);
      }

    } catch (error) {
      console.error('Error sending Expo notifications:', error);
      throw error;
    }
  }

  /**
   * Send notifications to FCM tokens
   */
  private async sendFCMMessages(tokens: string[], notification: any): Promise<void> {
    try {
      const fcmMessage: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: notification.title,
          body: notification.message,
        },
        data: notification.data,
        android: {
          notification: {
            channelId: 'accustom-reminder',
            priority: 'high' as const,
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: notification.title,
                body: notification.message,
              },
              badge: 1,
              sound: 'default',
            },
          },
        },
      };

      const response = await admin.messaging().sendMulticast(fcmMessage);

      if (response.failureCount > 0) {
        console.error(`Failed to send FCM notifications to ${response.failureCount} devices`);
      }
    } catch (error) {
      console.error('Error sending FCM messages:', error);
      throw error;
    }
  }

  /**
   * Get date-only representation in a specific timezone
   * Avoids timezone conversion issues by properly handling date boundaries
   */
  private getDateInTimezone(date: Date, timezone: string): Date {
    try {
      // Get the date string in the target timezone
      const dateString = date.toLocaleDateString("en-CA", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      // Create a new date object representing just the date (no time) in the target timezone
      const [year, month, day] = dateString.split('-').map(Number);
      return new Date(year, month - 1, day, 0, 0, 0, 0);
    } catch (error) {
      console.error(`Error converting date to timezone ${timezone}:`, error);
      // Fallback to UTC date
      return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0, 0);
    }
  }

  /**
   * Calculate hours until midnight deadline in user's timezone
   * Fixed to properly handle timezone conversions and avoid date boundary issues
   */
  private calculateHoursUntilDeadline(timezone: string): number {
    try {
      const now = new Date();

      // Get current time in user's timezone using proper timezone handling
      const userNowString = now.toLocaleString("en-US", {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });

      // Parse the timezone-adjusted time
      const [datePart, timePart] = userNowString.split(', ');
      const [month, day, year] = datePart.split('/');
      const [hour, minute, second] = timePart.split(':');

      const userNow = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );

      // Calculate next midnight in user's timezone
      const midnight = new Date(userNow);
      midnight.setHours(24, 0, 0, 0); // Next midnight

      const hoursUntilDeadline = (midnight.getTime() - userNow.getTime()) / (1000 * 60 * 60);
      return Math.max(0, hoursUntilDeadline);
    } catch (error) {
      console.error(`Error calculating hours until deadline for timezone ${timezone}:`, error);
      // Fallback to UTC calculation
      const now = new Date();
      const midnight = new Date(now);
      midnight.setUTCHours(24, 0, 0, 0);
      return Math.max(0, (midnight.getTime() - now.getTime()) / (1000 * 60 * 60));
    }
  }

  /**
   * Send commit completion notification
   */
  async sendCommitCompletionNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    totalReports: number,
    completionRate: number
  ): Promise<void> {
    const notificationData = {
      title: "🎉 Commitment Completed!",
      message: `Congratulations! You've completed your '${commitTitle}' commitment with a ${Math.round(completionRate * 100)}% success rate!`,
      type: "commitment_completed",
      priority: "medium",
      data: {
        action: "view_commitment_stats",
        commitId: commitId,
        commitTitle: commitTitle,
        totalReports: totalReports.toString(),
        completionRate: completionRate.toString()
      }
    };

    await this.sendFCMNotification(userId, notificationData);
  }

  /**
   * Send commit failure notification
   */
  async sendCommitFailureNotification(
    userId: string,
    commitTitle: string,
    commitId: string,
    completedReports: number,
    totalReports: number
  ): Promise<void> {
    const notificationData = {
      title: "😔 Commitment Ended",
      message: `Your '${commitTitle}' commitment has ended. You completed ${completedReports} out of ${totalReports} reports. Ready to try again?`,
      type: "commitment_failed",
      priority: "medium",
      data: {
        action: "view_commitment_stats",
        commitId: commitId,
        commitTitle: commitTitle,
        completedReports: completedReports.toString(),
        totalReports: totalReports.toString()
      }
    };

    await this.sendFCMNotification(userId, notificationData);
  }
}
