/**
 * Commit Service
 * Handles commit lifecycle management and integration with scheduling system
 */

import { DatabaseService } from './databaseService';
import { CommitSchedulerService } from './commitSchedulerService';
import { CommitVerificationService } from './commitVerificationService';
import { CommitNotificationService } from './commitNotificationService';

export interface CommitStartResult {
  success: boolean;
  message: string;
  commitId: string;
  userId: string;
  action: string;
  schedulingResult?: any;
}

export interface CommitEndResult {
  success: boolean;
  message: string;
  commitId: string;
  userId: string;
  reason: string;
  action: string;
  cleanupResult?: any;
}

export interface CommitCheckResult {
  success: boolean;
  message: string;
  commitId: string;
  userId: string;
  action: string;
  verificationResult?: any;
  statusUpdated?: boolean;
}

export class CommitService {
  private dbService: DatabaseService;
  private schedulerService: CommitSchedulerService;
  private verificationService: CommitVerificationService;
  private notificationService: CommitNotificationService;

  constructor() {
    this.dbService = new DatabaseService();
    this.schedulerService = new CommitSchedulerService();
    this.verificationService = new CommitVerificationService();
    this.notificationService = new CommitNotificationService();
  }

  /**
   * Start a commit (called by commitStarter cloud function)
   */
  async startCommit(
    commitId: string, 
    userId: string, 
    schedulingConfig?: any
  ): Promise<CommitStartResult> {

    try {
      // Get commit data
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit) {
        return {
          success: false,
          message: 'Commit not found',
          commitId,
          userId,
          action: 'error'
        };
      }

      // Verify commit belongs to user
      if (commit.userId !== userId) {
        return {
          success: false,
          message: 'Commit does not belong to user',
          commitId,
          userId,
          action: 'error'
        };
      }

      // Update commit status to active (don't change setupStatus - that's for category setup)
      await this.dbService.updateDocument('commits', commitId, {
        status: 'active',
        'scheduling.enabled': true,
        'scheduling.nextVerificationTime': new Date().toISOString()
      });

      // Update user commit status
      await this.dbService.updateDocument(`users/${userId}/commits`, commitId, {
        status: 'active'
      });

      // Activate commit scheduling
      if (schedulingConfig) {
        const schedulingResult = await this.schedulerService.activateCommitScheduling(schedulingConfig);

        return {
          success: true,
          message: 'Commit started and scheduling activated',
          commitId,
          userId,
          action: 'started_with_scheduling',
          schedulingResult
        };
      } else {
        return {
          success: true,
          message: 'Commit started (no scheduling config provided)',
          commitId,
          userId,
          action: 'started_no_scheduling'
        };
      }

    } catch (error) {
      console.error(`❌ Failed to start commit ${commitId}:`, error);
      return {
        success: false,
        message: `Failed to start commit: ${error}`,
        commitId,
        userId,
        action: 'error'
      };
    }
  }

  /**
   * End a commit (called by commitEnder cloud function)
   */
  async endCommit(
    commitId: string, 
    userId: string, 
    reason: string
  ): Promise<CommitEndResult> {

    try {
      // Get commit data
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit) {
        return {
          success: false,
          message: 'Commit not found',
          commitId,
          userId,
          reason,
          action: 'error'
        };
      }

      // Determine final status based on reason
      let finalStatus: 'completed' | 'failed' | 'cancelled';
      switch (reason) {
        case 'duration_completed':
        case 'all_reports_completed':
          finalStatus = 'completed';
          break;
        case 'too_many_failures':
        case 'consecutive_failures':
          finalStatus = 'failed';
          break;
        case 'user_cancelled':
        case 'manual_cancellation':
          finalStatus = 'cancelled';
          break;
        default:
          finalStatus = 'completed';
      }

      // Update commit status
      await this.dbService.updateDocument('commits', commitId, {
        status: finalStatus,
        'scheduling.enabled': false,
        endedAt: new Date().toISOString(),
        endReason: reason
      });

      // Update user commit status and calculate final progress
      const userCommit = await this.dbService.getDocument(`users/${userId}/commits`, commitId);
      if (userCommit) {
        const completionRate = userCommit.progress.totalReports > 0 
          ? userCommit.progress.completedReports / userCommit.progress.totalReports 
          : 0;

        await this.dbService.updateDocument(`users/${userId}/commits`, commitId, {
          status: finalStatus,
          completionRate,
          endedAt: new Date().toISOString()
        });

        // Send completion notification
        if (finalStatus === 'completed') {
          await this.notificationService.sendCommitCompletionNotification(
            userId,
            commit.title,
            commitId,
            userCommit.progress.totalReports,
            completionRate
          );
        }
      }

      // Clean up scheduler jobs
      const cleanupResult = await this.schedulerService.deleteCommitScheduling(commitId, userId);

      return {
        success: true,
        message: `Commit ended with status: ${finalStatus}`,
        commitId,
        userId,
        reason,
        action: 'ended',
        cleanupResult
      };

    } catch (error) {
      console.error(`❌ Failed to end commit ${commitId}:`, error);
      return {
        success: false,
        message: `Failed to end commit: ${error}`,
        commitId,
        userId,
        reason,
        action: 'error'
      };
    }
  }

  /**
   * Perform daily commit check (called by commitDailyChecker cloud function)
   */
  async performDailyCommitCheck(
    commitId: string, 
    userId: string, 
    schedulerData: any
  ): Promise<CommitCheckResult> {

    try {
      // Get commit data
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit) {
        return {
          success: false,
          message: 'Commit not found',
          commitId,
          userId,
          action: 'error'
        };
      }

      // Check if commit is still active
      if (commit.status !== 'active') {
        return {
          success: true,
          message: 'Commit not active, skipping check',
          commitId,
          userId,
          action: 'skipped_inactive'
        };
      }

      // Handle recurring check logic
      if (schedulerData?.isRecurring) {
        const shouldCheck = await this.shouldPerformCheckToday(commit, schedulerData);
        if (!shouldCheck) {
          return {
            success: true,
            message: 'No check needed today',
            commitId,
            userId,
            action: 'skipped_not_needed'
          };
        }
      }

      // Check if deadline has passed
      const deadlinePassed = await this.hasDeadlinePassed(commit, schedulerData);
      
      if (deadlinePassed) {
        // Check if user has submitted
        const hasSubmitted = await this.hasUserSubmittedToday(commitId, userId, commit.timezone || 'UTC');
        
        if (!hasSubmitted) {
          // Process deadline violation
          await this.verificationService.processDeadlineViolation(commitId, new Date());
          
          // Send deadline violation notification
          const hasGracePeriod = schedulerData.gracePeriodHours > 0;
          await this.notificationService.sendDeadlineViolationNotification(
            userId,
            commit.title,
            commitId,
            hasGracePeriod
          );

          return {
            success: true,
            message: 'Deadline violation processed',
            commitId,
            userId,
            action: 'deadline_violation',
            statusUpdated: true
          };
        }
      }

      // Perform auto-verification if enabled
      if (commit.verification?.autoVerificationEnabled) {
        const verificationResult = await this.performAutoVerification(commit, schedulerData);
        
        return {
          success: true,
          message: 'Daily check completed with auto-verification',
          commitId,
          userId,
          action: 'checked_with_verification',
          verificationResult,
          statusUpdated: verificationResult?.verified || false
        };
      }

      return {
        success: true,
        message: 'Daily check completed',
        commitId,
        userId,
        action: 'checked'
      };

    } catch (error) {
      console.error(`❌ Failed to perform daily check for commit ${commitId}:`, error);
      return {
        success: false,
        message: `Failed to perform daily check: ${error}`,
        commitId,
        userId,
        action: 'error'
      };
    }
  }

  /**
   * Check if we should perform check today based on commit frequency
   * Fixed to properly handle timezone conversions for accurate date calculations
   */
  private async shouldPerformCheckToday(commit: any, schedulerData: any): Promise<boolean> {
    try {
      const now = new Date();
      const userTimezone = commit.timezone || 'UTC';

      // Get current date in user's timezone (date only, no time)
      const userDateString = now.toLocaleDateString("en-CA", {
        timeZone: userTimezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Returns YYYY-MM-DD format

      const userDate = new Date(userDateString + 'T00:00:00');

      // For daily commits, always check
      if (commit.schedule?.frequency === 'daily') {
        return true;
      }

      // For weekly commits, check if we're in an active week
      if (commit.schedule?.frequency === 'weekly') {
        const startDate = new Date(commit.schedule.startDate + 'T00:00:00');
        const weeksDiff = Math.floor((userDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
        const currentWeek = weeksDiff + 1;
        const totalWeeks = commit.schedule.duration || 1;

        return currentWeek <= totalWeeks;
      }

      // For other frequencies, default to true
      return true;
    } catch (error) {
      console.error(`Error checking if should perform check today for commit ${commit.id}:`, error);
      // Fallback to true to ensure checks continue
      return true;
    }
  }

  /**
   * Check if deadline has passed
   * Fixed to properly handle timezone conversions and avoid date boundary issues
   */
  private async hasDeadlinePassed(commit: any, schedulerData: any): Promise<boolean> {
    try {
      const now = new Date();
      const userTimezone = commit.timezone || 'UTC';

      // Get current time in user's timezone using proper timezone handling
      const userNowString = now.toLocaleString("en-US", {
        timeZone: userTimezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });

      // Parse the timezone-adjusted time
      const [datePart, timePart] = userNowString.split(', ');
      const [month, day, year] = datePart.split('/');
      const [hour, minute, second] = timePart.split(':');

      const userNow = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );

      const deadlineType = schedulerData.deadlineType || commit.schedule?.deadline?.type || 'midnight';
      const deadlineTime = schedulerData.deadlineTime || commit.schedule?.deadline?.time;
      const deadlineEndTime = schedulerData.deadlineEndTime || commit.schedule?.deadline?.endTime;

      let deadline: Date;

      switch (deadlineType) {
        case 'before':
          if (deadlineTime) {
            const [deadlineHour, deadlineMinute] = deadlineTime.split(':').map(Number);
            deadline = new Date(userNow);
            deadline.setHours(deadlineHour, deadlineMinute, 0, 0);
          } else {
            deadline = new Date(userNow);
            deadline.setHours(23, 59, 59, 999);
          }
          break;

        case 'after':
          if (deadlineTime) {
            const [deadlineHour, deadlineMinute] = deadlineTime.split(':').map(Number);
            deadline = new Date(userNow);
            deadline.setHours(deadlineHour, deadlineMinute, 0, 0);
            deadline.setDate(deadline.getDate() + 1); // Next day
          } else {
            deadline = new Date(userNow);
            deadline.setHours(23, 59, 59, 999);
          }
          break;

        case 'between':
          if (deadlineEndTime) {
            const [deadlineHour, deadlineMinute] = deadlineEndTime.split(':').map(Number);
            deadline = new Date(userNow);
            deadline.setHours(deadlineHour, deadlineMinute, 0, 0);
          } else {
            deadline = new Date(userNow);
            deadline.setHours(23, 59, 59, 999);
          }
          break;

        case 'midnight':
        default:
          deadline = new Date(userNow);
          deadline.setHours(23, 59, 59, 999);
          break;
      }

      return userNow.getTime() > deadline.getTime();
    } catch (error) {
      console.error(`Error checking if deadline has passed for commit ${commit.id}:`, error);
      // Fallback to false to avoid false positives
      return false;
    }
  }

  /**
   * Check if user has submitted today
   */
  private async hasUserSubmittedToday(commitId: string, userId: string, timezone: string): Promise<boolean> {
    const today = new Date().toLocaleDateString('en-CA', { timeZone: timezone }); // YYYY-MM-DD format
    
    // Check commit submissions for today
    const submissions = await this.dbService.getCollection(`commits/${commitId}/submissions`);
    
    return submissions.some((submission: any) => 
      submission.timestamp && 
      submission.timestamp.split('T')[0] === today &&
      submission.status === 'submitted'
    );
  }

  /**
   * Perform auto-verification for commit
   */
  private async performAutoVerification(commit: any, schedulerData: any): Promise<any> {
    const verificationType = schedulerData.verificationType || commit.verification?.type;
    
    switch (verificationType) {
      case 'github':
        return await this.verificationService.verifyGitHubCommit(commit.id, {
          repositoryName: schedulerData.repositoryName || commit.verification?.repositoryName,
          minCommits: schedulerData.minCommits || 1,
          verificationWindow: schedulerData.verificationWindow || 24,
          userId: commit.userId
        });
        
      case 'strava':
        return await this.verificationService.verifyStravaActivity(commit.id, {
          activityType: schedulerData.activityType,
          minDuration: schedulerData.minDuration,
          verificationWindow: schedulerData.verificationWindow || 24,
          userId: commit.userId
        });
        
      default:
        return null;
    }
  }
}
