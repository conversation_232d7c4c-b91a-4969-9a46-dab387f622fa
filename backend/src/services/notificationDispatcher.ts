/**
 * Multi-Channel Notification Dispatcher
 * Routes notifications to different channels (app, email, SMS, WhatsApp) based on user preferences
 */

import { EmailService, EmailNotification } from './emailService';
import { SMSService, SMSNotification } from './smsService';
import { DatabaseService } from './databaseService';
import * as admin from 'firebase-admin';

export interface NotificationChannels {
  app?: boolean;
  email?: boolean;
  sms?: boolean;
  whatsapp?: boolean;
}

export interface DispatchResult {
  success: boolean;
  channels: {
    app?: { success: boolean; messageId?: string; timestamp?: string; error?: string };
    email?: { success: boolean; messageId?: string; timestamp?: string; error?: string };
    sms?: { success: boolean; messageId?: string; timestamp?: string; error?: string };
    whatsapp?: { success: boolean; messageId?: string; timestamp?: string; error?: string };
  };
  timestamp: string;
}

export class NotificationDispatcher {
  private emailService: EmailService;
  private smsService: SMSService;
  private dbService: DatabaseService;

  constructor() {
    this.emailService = new EmailService();
    this.smsService = new SMSService();
    this.dbService = new DatabaseService();
  }

  /**
   * Dispatch notification to all enabled channels
   */
  async dispatchNotification(
    userId: string,
    notification: EmailNotification,
    channels?: NotificationChannels
  ): Promise<DispatchResult> {
    const result: DispatchResult = {
      success: true,
      channels: {},
      timestamp: new Date().toISOString()
    };

    try {
      // Get user preferences if channels not specified
      const userChannels = channels || await this.getUserChannelPreferences(userId);

      // Dispatch to each enabled channel
      const promises: Promise<void>[] = [];

      if (userChannels.app !== false) {
        promises.push(this.dispatchToApp(userId, notification, result));
      }

      if (userChannels.email !== false) {
        promises.push(this.dispatchToEmail(userId, notification, result));
      }

      if (userChannels.sms === true) {
        promises.push(this.dispatchToSMS(userId, notification, result));
      }

      if (userChannels.whatsapp === true) {
        promises.push(this.dispatchToWhatsApp(userId, notification, result));
      }

      // Wait for all dispatches to complete
      await Promise.allSettled(promises);

      // Check if any channel succeeded
      const channelResults = Object.values(result.channels);
      result.success = channelResults.some(channel => channel.success);

    } catch (error) {
      console.error('Error in notification dispatch:', error);
      result.success = false;
    }

    return result;
  }

  /**
   * Send welcome notification to all enabled channels
   */
  async sendWelcomeNotification(userId: string, userName: string): Promise<DispatchResult> {
    const welcomeNotification: EmailNotification = {
      title: `Welcome to Accustom, ${userName}!`,
      message: `Hi ${userName}, welcome to Accustom! We're excited to help you build lasting habits and achieve your goals.`,
      type: 'account',
      priority: 'medium'
    };

    // For welcome notifications, enable app and email by default
    const channels: NotificationChannels = {
      app: true,
      email: true,
      sms: false,
      whatsapp: false
    };

    return this.dispatchNotification(userId, welcomeNotification, channels);
  }

  /**
   * Dispatch notification to app (push notification)
   */
  private async dispatchToApp(
    userId: string,
    notification: EmailNotification,
    result: DispatchResult
  ): Promise<void> {
    try {
      // Use existing FCM notification system
      await this.sendFCMNotification(userId, {
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        data: notification.data || {}
      });

      result.channels.app = {
        success: true,
        messageId: 'fcm-sent'
      };

    } catch (error) {
      console.error('Error dispatching to app:', error);
      result.channels.app = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Dispatch notification to email
   */
  private async dispatchToEmail(
    userId: string,
    notification: EmailNotification,
    result: DispatchResult
  ): Promise<void> {
    try {
      const emailResult = await this.emailService.sendEmailNotification(userId, notification);
      result.channels.email = emailResult;

    } catch (error) {
      console.error('Error dispatching to email:', error);
      result.channels.email = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Dispatch notification to SMS using Twilio
   */
  private async dispatchToSMS(
    userId: string,
    notification: EmailNotification,
    result: DispatchResult
  ): Promise<void> {
    try {
      // Convert EmailNotification to SMSNotification
      const smsNotification: SMSNotification = {
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        data: notification.data
      };

      // Send SMS using SMS service
      const smsResult = await this.smsService.sendSMSNotification(userId, smsNotification);

      result.channels.sms = {
        success: smsResult.success,
        messageId: smsResult.messageId,
        timestamp: smsResult.timestamp,
        error: smsResult.error
      };

    } catch (error) {
      console.error('Error dispatching to SMS:', error);
      result.channels.sms = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Dispatch notification to WhatsApp (placeholder - to be implemented)
   */
  private async dispatchToWhatsApp(
    userId: string,
    notification: EmailNotification,
    result: DispatchResult
  ): Promise<void> {
    try {
      // TODO: Implement WhatsApp service (WhatsApp Business API, Twilio, etc.)
      console.log('WhatsApp dispatch not yet implemented');
      
      result.channels.whatsapp = {
        success: false,
        error: 'WhatsApp service not implemented'
      };

    } catch (error) {
      console.error('Error dispatching to WhatsApp:', error);
      result.channels.whatsapp = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get user channel preferences
   */
  private async getUserChannelPreferences(userId: string): Promise<NotificationChannels> {
    try {
      const result = await this.dbService.getDocument('users', userId);
      const preferences = result.data?.notificationPreferences;

      return {
        app: preferences?.channels?.includes('app') !== false,
        email: preferences?.channels?.includes('email') !== false,
        sms: preferences?.channels?.includes('sms') === true,
        whatsapp: preferences?.channels?.includes('whatsapp') === true
      };

    } catch (error) {
      console.error('Error getting user channel preferences:', error);
      // Default to app and email enabled
      return {
        app: true,
        email: true,
        sms: false,
        whatsapp: false
      };
    }
  }

  /**
   * Send FCM notification (reusing existing logic)
   */
  private async sendFCMNotification(userId: string, notificationData: any): Promise<void> {
    try {
      // First, store the notification in Firestore
      const notificationRef = admin.firestore()
        .collection('users')
        .doc(userId)
        .collection('notifications')
        .doc();

      const notification = {
        id: notificationRef.id,
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type,
        priority: notificationData.priority,
        time: admin.firestore.FieldValue.serverTimestamp(),
        read: false,
        data: notificationData.data || {},
      };

      await notificationRef.set(notification);

      // Get FCM tokens for the user
      const tokensSnapshot = await admin.firestore()
        .collection('users')
        .doc(userId)
        .collection('fcm_tokens')
        .get();

      if (tokensSnapshot.empty) {
        console.log('No FCM tokens found for user:', userId);
        return;
      }

      const tokens = tokensSnapshot.docs.map(doc => doc.data().token);

      // Send FCM message
      const fcmMessage: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: notificationData.title,
          body: notificationData.message,
        },
        data: notificationData.data || {},
        android: {
          notification: {
            channelId: 'accustom-reminder',
            priority: 'high' as const,
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: notificationData.title,
                body: notificationData.message,
              },
              badge: 1,
              sound: 'default',
            },
          },
        },
      };

      const response = await admin.messaging().sendMulticast(fcmMessage);

      if (response.failureCount > 0) {
        console.error(`Failed to send FCM notifications to ${response.failureCount} devices`);
      }

    } catch (error) {
      console.error('Error sending FCM notification:', error);
      throw error;
    }
  }
}
