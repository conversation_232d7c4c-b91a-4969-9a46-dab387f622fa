/**
 * Service Container for Dependency Injection
 * Provides centralized service management and reduces instantiation overhead
 */

import { ProgramService } from './programService';
import { MigrationService } from './migrationService';
import { DatabaseService } from './databaseService';
import { SchedulerService } from './schedulerService';
import { ValidationService } from './validationService';
import { NotificationService } from './notificationService';
import { CommitSchedulerService } from './commitSchedulerService';
import { CommitVerificationService } from './commitVerificationService';
import { CommitNotificationService } from './commitNotificationService';
import { CommitService } from './commitService';

export class ServiceContainer {
  private static instance: ServiceContainer;
  
  // Service instances
  private _databaseService: DatabaseService | null = null;
  private _schedulerService: SchedulerService | null = null;
  private _programService: ProgramService | null = null;
  private _migrationService: MigrationService | null = null;
  private _notificationService: NotificationService | null = null;
  private _commitSchedulerService: CommitSchedulerService | null = null;
  private _commitVerificationService: CommitVerificationService | null = null;
  private _commitNotificationService: CommitNotificationService | null = null;
  private _commitService: CommitService | null = null;

  constructor() {
    if (ServiceContainer.instance) {
      return ServiceContainer.instance;
    }
    ServiceContainer.instance = this;
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Get database service (singleton)
   */
  getDatabaseService(): DatabaseService {
    if (!this._databaseService) {
      this._databaseService = new DatabaseService();
    }
    return this._databaseService;
  }

  /**
   * Get scheduler service (singleton)
   */
  getSchedulerService(): SchedulerService {
    if (!this._schedulerService) {
      this._schedulerService = new SchedulerService();
    }
    return this._schedulerService;
  }

  /**
   * Get program service (singleton)
   */
  getProgramService(): ProgramService {
    if (!this._programService) {
      this._programService = new ProgramService();
    }
    return this._programService;
  }

  /**
   * Get migration service (singleton)
   */
  getMigrationService(): MigrationService {
    if (!this._migrationService) {
      this._migrationService = new MigrationService();
    }
    return this._migrationService;
  }

  /**
   * Get notification service (singleton)
   */
  getNotificationService(): NotificationService {
    if (!this._notificationService) {
      this._notificationService = new NotificationService();
    }
    return this._notificationService;
  }

  /**
   * Get commit scheduler service (singleton)
   */
  getCommitSchedulerService(): CommitSchedulerService {
    if (!this._commitSchedulerService) {
      this._commitSchedulerService = new CommitSchedulerService();
    }
    return this._commitSchedulerService;
  }

  /**
   * Get commit verification service (singleton)
   */
  getCommitVerificationService(): CommitVerificationService {
    if (!this._commitVerificationService) {
      this._commitVerificationService = new CommitVerificationService();
    }
    return this._commitVerificationService;
  }

  /**
   * Get commit notification service (singleton)
   */
  getCommitNotificationService(): CommitNotificationService {
    if (!this._commitNotificationService) {
      this._commitNotificationService = new CommitNotificationService();
    }
    return this._commitNotificationService;
  }

  /**
   * Get commit service (singleton)
   */
  getCommitService(): CommitService {
    if (!this._commitService) {
      this._commitService = new CommitService();
    }
    return this._commitService;
  }

  /**
   * Get validation service (static methods, no instantiation needed)
   */
  getValidationService(): typeof ValidationService {
    return ValidationService;
  }

  /**
   * Reset all services (useful for testing)
   */
  reset(): void {
    this._databaseService = null;
    this._schedulerService = null;
    this._programService = null;
    this._migrationService = null;
    this._notificationService = null;
    this._commitSchedulerService = null;
    this._commitVerificationService = null;
    this._commitNotificationService = null;
    this._commitService = null;
  }
}
