/**
 * SMS Service using Twilio
 * Handles SMS sending functionality for notifications
 */

import { Twilio } from 'twilio';
import { getSMSConfig } from '../utils/config';
import { DatabaseService } from './databaseService';
import { SMSTemplates, SMSTemplateData } from './smsTemplates';

export interface SMSNotification {
  title: string;
  message: string;
  type: 'account' | 'program' | 'points' | 'reminder';
  priority: 'low' | 'medium' | 'high';
  data?: Record<string, string>;
}

export interface SMSResult {
  success: boolean;
  messageId?: string;
  timestamp?: string;
  error?: string;
}

export class SMSService {
  private twilioClient!: Twilio;
  private dbService: DatabaseService;
  private smsConfig: any;

  constructor() {
    this.dbService = new DatabaseService();
    this.initializeTwilio();
  }

  /**
   * Initialize Twilio client with credentials
   */
  private initializeTwilio(): void {
    try {
      this.smsConfig = getSMSConfig();
      this.twilioClient = new Twilio(
        this.smsConfig.twilioAccountSid,
        this.smsConfig.twilioAuthToken
      );
    } catch (error) {
      console.error('Failed to initialize Twilio:', error);
      throw error;
    }
  }

  /**
   * Send SMS notification to a user
   */
  async sendSMSNotification(userId: string, notification: SMSNotification): Promise<SMSResult> {
    try {
      // Get user phone number from database
      const userData = await this.dbService.getDocument('users', userId);
      if (!userData) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const userPhone = userData.phoneNumber;
      if (!userPhone) {
        return {
          success: false,
          error: 'User phone number not found'
        };
      }

      // Check user SMS preferences
      const smsPrefs = await this.getUserSMSPreferences(userId);
      if (!this.shouldSendSMS(notification.type, smsPrefs)) {
        return {
          success: true,
          messageId: 'skipped',
          timestamp: new Date().toISOString()
        };
      }

      // Format SMS message
      const smsMessage = this.formatSMSMessage(notification);

      // Send SMS using Twilio
      const result = await this.sendSMS(userPhone, smsMessage);
      
      return result;

    } catch (error) {
      console.error('Error sending SMS notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send welcome SMS
   */
  async sendWelcomeSMS(userId: string, userName: string): Promise<SMSResult> {
    const welcomeNotification: SMSNotification = {
      title: `Welcome to Accustom!`,
      message: `Hi ${userName}, welcome to Accustom! We're excited to help you build lasting habits. Reply STOP to opt out.`,
      type: 'account',
      priority: 'medium'
    };

    return this.sendSMSNotification(userId, welcomeNotification);
  }

  /**
   * Send SMS using Twilio
   */
  private async sendSMS(to: string, message: string): Promise<SMSResult> {
    try {
      // Ensure phone number is in E.164 format
      const formattedPhone = this.formatPhoneNumber(to);
      
      const twilioMessage = await this.twilioClient.messages.create({
        body: message,
        from: this.smsConfig.twilioPhoneNumber,
        to: formattedPhone
      });

      return {
        success: true,
        messageId: twilioMessage.sid,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error sending SMS via Twilio:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send SMS'
      };
    }
  }

  /**
   * Format SMS message using templates
   */
  private formatSMSMessage(notification: SMSNotification): string {
    // Create template data from notification
    const templateData: SMSTemplateData = {
      userName: 'User', // Will be populated from user data if needed
      title: notification.title,
      message: notification.message,
      ...notification.data
    };

    // Use template based on notification type
    let templateType = 'generic';
    switch (notification.type) {
      case 'reminder':
        templateType = notification.priority === 'high' ? 'urgent_reminder' : 'reminder';
        break;
      case 'account':
        templateType = 'generic';
        break;
      case 'program':
        templateType = 'generic';
        break;
      case 'points':
        templateType = 'points_earned';
        break;
    }

    return SMSTemplates.getTemplateByType(templateType, templateData);
  }

  /**
   * Format phone number to E.164 format
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // If it starts with 1 and has 11 digits, it's likely US/Canada
    if (digits.length === 11 && digits.startsWith('1')) {
      return `+${digits}`;
    }
    
    // If it has 10 digits, assume US/Canada and add +1
    if (digits.length === 10) {
      return `+1${digits}`;
    }
    
    // If it already starts with +, return as is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }
    
    // Otherwise, add + prefix
    return `+${digits}`;
  }

  /**
   * Get user SMS preferences
   */
  private async getUserSMSPreferences(userId: string): Promise<any> {
    try {
      const userData = await this.dbService.getDocument('users', userId);
      return userData?.notificationPreferences?.smsNotifications || {
        reminders: true,
        urgentOnly: false
      };
    } catch (error) {
      console.error('Error getting SMS preferences:', error);
      // Default to enabled for important notifications
      return {
        reminders: true,
        urgentOnly: false
      };
    }
  }

  /**
   * Check if SMS should be sent based on type and preferences
   */
  private shouldSendSMS(type: string, preferences: any): boolean {
    switch (type) {
      case 'reminder':
        return preferences.reminders !== false;
      case 'account':
        // Account notifications are always sent if SMS is enabled
        return true;
      case 'program':
        // Program notifications depend on urgentOnly setting
        return !preferences.urgentOnly;
      case 'points':
        // Points notifications are typically not urgent
        return !preferences.urgentOnly;
      default:
        return !preferences.urgentOnly;
    }
  }

  /**
   * Validate phone number format
   */
  static isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic validation for international phone numbers
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    return phoneRegex.test(`+${cleanNumber}`) && cleanNumber.length >= 10;
  }
}

// Export singleton instance
let smsServiceInstance: SMSService | null = null;

export function getSMSService(): SMSService {
  if (!smsServiceInstance) {
    smsServiceInstance = new SMSService();
  }
  return smsServiceInstance;
}
