/**
 * Commit Verification Service
 * Handles automatic and manual verification for different commit types
 */

import { DatabaseService } from './databaseService';
import { monitorPerformance, commitMonitoringService } from '../utils/commitMonitoring';

export interface VerificationResult {
  success: boolean;
  verified: boolean;
  data?: any;
  error?: string;
  timestamp: string;
  verificationType: 'automatic' | 'manual';
  details?: {
    commitCount?: number;
    commits?: any[];
    activityData?: any;
    submissionData?: any;
  };
}

export interface GitHubVerificationConfig {
  repositoryName: string;
  minCommits: number;
  verificationWindow: number; // hours
  userId: string;
}

export interface StravaVerificationConfig {
  activityType?: string;
  minDuration?: number; // minutes
  verificationWindow: number; // hours
  userId: string;
}

export interface PhotoVerificationConfig {
  allowMultiple: boolean;
  requiredCount?: number;
  userId: string;
}

export interface ManualVerificationConfig {
  submissionType: 'text' | 'photo' | 'video';
  validationRules?: any;
  userId: string;
}

export class CommitVerificationService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = new DatabaseService();
  }

  /**
   * Verify GitHub commits for a user
   */
  @monitorPerformance('verifyGitHubCommit')
  async verifyGitHubCommit(
    commitId: string, 
    config: GitHubVerificationConfig
  ): Promise<VerificationResult> {
    const startTime = Date.now();
    try {

      // Get user's GitHub integration
      const integration = await this.dbService.getIntegrationByProvider(config.userId, 'github');
      if (!integration) {
        return {
          success: false,
          verified: false,
          error: 'GitHub integration not found',
          timestamp: new Date().toISOString(),
          verificationType: 'automatic'
        };
      }

      // Calculate time window
      const since = new Date(Date.now() - config.verificationWindow * 60 * 60 * 1000);
      
      // This would integrate with your existing GitHub service
      // For now, we'll simulate the verification logic
      const githubService = await this.getGitHubService(integration.accessToken);
      const commits = await githubService.getRecentCommits(config.repositoryName, since, config.userId);

      const verified = commits.length >= config.minCommits;

      // Update verification status in database
      await this.updateVerificationStatus(commitId, {
        success: true,
        verified,
        timestamp: new Date().toISOString(),
        verificationType: 'automatic',
        details: {
          commitCount: commits.length,
          commits: commits.slice(0, 5) // Store first 5 commits
        }
      });

      // Log verification attempt
      await commitMonitoringService.logVerificationAttempt(
        commitId,
        config.userId,
        'github',
        true,
        verified,
        Date.now() - startTime
      );

      return {
        success: true,
        verified,
        timestamp: new Date().toISOString(),
        verificationType: 'automatic',
        details: {
          commitCount: commits.length,
          commits: commits.slice(0, 5)
        }
      };

    } catch (error) {
      console.error(`❌ GitHub verification failed for commit ${commitId}:`, error);
      
      const result: VerificationResult = {
        success: false,
        verified: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        verificationType: 'automatic'
      };

      await this.updateVerificationStatus(commitId, result);
      return result;
    }
  }

  /**
   * Verify Strava activity for a user
   */
  async verifyStravaActivity(
    commitId: string, 
    config: StravaVerificationConfig
  ): Promise<VerificationResult> {
    try {

      // Get user's Strava integration
      const integration = await this.dbService.getIntegrationByProvider(config.userId, 'strava');
      if (!integration) {
        return {
          success: false,
          verified: false,
          error: 'Strava integration not found',
          timestamp: new Date().toISOString(),
          verificationType: 'automatic'
        };
      }

      // Calculate time window
      const since = new Date(Date.now() - config.verificationWindow * 60 * 60 * 1000);
      
      // This would integrate with Strava API
      // For now, we'll simulate the verification logic
      const stravaService = await this.getStravaService(integration.accessToken);
      const activities = await stravaService.getRecentActivities(since, config.activityType);

      let verified = activities.length > 0;
      
      // Check duration if specified
      if (verified && config.minDuration) {
        verified = activities.some((activity: any) => 
          activity.moving_time >= (config.minDuration || 0) * 60
        );
      }

      const result: VerificationResult = {
        success: true,
        verified,
        timestamp: new Date().toISOString(),
        verificationType: 'automatic',
        details: {
          activityData: activities.slice(0, 3) // Store first 3 activities
        }
      };

      await this.updateVerificationStatus(commitId, result);
      return result;

    } catch (error) {
      console.error(`❌ Strava verification failed for commit ${commitId}:`, error);
      
      const result: VerificationResult = {
        success: false,
        verified: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        verificationType: 'automatic'
      };

      await this.updateVerificationStatus(commitId, result);
      return result;
    }
  }

  /**
   * Verify photo submission
   */
  async verifyPhotoSubmission(
    commitId: string, 
    config: PhotoVerificationConfig,
    submissionData: any
  ): Promise<VerificationResult> {
    try {

      const photos = Array.isArray(submissionData.photos) ? submissionData.photos : [submissionData.photo];
      const photoCount = photos.filter((photo: any) => photo && photo.trim() !== '').length;

      let verified = photoCount > 0;
      
      // Check required count if specified
      if (verified && config.requiredCount) {
        verified = photoCount >= config.requiredCount;
      }

      // Check multiple photos requirement
      if (verified && config.allowMultiple === false && photoCount > 1) {
        verified = false;
      }

      const result: VerificationResult = {
        success: true,
        verified,
        timestamp: new Date().toISOString(),
        verificationType: 'manual',
        details: {
          submissionData: {
            photoCount,
            photos: photos.slice(0, 3) // Store first 3 photo URLs
          }
        }
      };

      await this.updateVerificationStatus(commitId, result);
      return result;

    } catch (error) {
      console.error(`❌ Photo verification failed for commit ${commitId}:`, error);
      
      const result: VerificationResult = {
        success: false,
        verified: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        verificationType: 'manual'
      };

      await this.updateVerificationStatus(commitId, result);
      return result;
    }
  }

  /**
   * Verify manual submission (text, video, etc.)
   */
  async verifyManualSubmission(
    commitId: string, 
    config: ManualVerificationConfig,
    submissionData: any
  ): Promise<VerificationResult> {
    try {

      let verified = false;

      switch (config.submissionType) {
        case 'text':
          verified = submissionData.text && submissionData.text.trim().length > 0;
          if (verified && config.validationRules?.minLength) {
            verified = submissionData.text.trim().length >= config.validationRules.minLength;
          }
          break;

        case 'video':
          verified = submissionData.videoUrl && submissionData.videoUrl.trim() !== '';
          break;

        case 'photo':
          verified = submissionData.photoUrl && submissionData.photoUrl.trim() !== '';
          break;

        default:
          verified = Object.keys(submissionData).length > 0;
      }

      const result: VerificationResult = {
        success: true,
        verified,
        timestamp: new Date().toISOString(),
        verificationType: 'manual',
        details: {
          submissionData
        }
      };

      await this.updateVerificationStatus(commitId, result);
      return result;

    } catch (error) {
      console.error(`❌ Manual verification failed for commit ${commitId}:`, error);
      
      const result: VerificationResult = {
        success: false,
        verified: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        verificationType: 'manual'
      };

      await this.updateVerificationStatus(commitId, result);
      return result;
    }
  }

  /**
   * Update verification status in database
   */
  private async updateVerificationStatus(commitId: string, result: VerificationResult): Promise<void> {
    try {
      // Update the commit document with verification result
      await this.dbService.updateDocument('commits', commitId, {
        'verification.lastAttempt': {
          timestamp: result.timestamp,
          success: result.success,
          verified: result.verified,
          error: result.error,
          data: result.details
        },
        'verification.totalAttempts': this.dbService.increment(1),
        'verification.consecutiveFailures': result.verified ? 0 : this.dbService.increment(1)
      });

    } catch (error) {
      console.error(`❌ Failed to update verification status for commit ${commitId}:`, error);
    }
  }

  /**
   * Handle verification failure with retry logic
   */
  async handleVerificationFailure(commitId: string, error: any): Promise<void> {
    try {
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit) return;

      const consecutiveFailures = (commit.verification?.consecutiveFailures || 0) + 1;
      const maxRetries = 3;

      if (consecutiveFailures >= maxRetries) {
        // Mark as failed after max retries
        await this.dbService.updateDocument('commits', commitId, {
          status: 'failed',
          'verification.consecutiveFailures': consecutiveFailures,
          'verification.lastAttempt': {
            timestamp: new Date().toISOString(),
            success: false,
            error: `Max retries exceeded: ${error.message || error}`
          }
        });

      } else {
        // Schedule retry
        const retryDelay = Math.pow(2, consecutiveFailures) * 60 * 60 * 1000; // Exponential backoff in hours
        const nextRetryTime = new Date(Date.now() + retryDelay);

        await this.dbService.updateDocument('commits', commitId, {
          'verification.consecutiveFailures': consecutiveFailures,
          'verification.nextRetryTime': nextRetryTime.toISOString(),
          'verification.lastAttempt': {
            timestamp: new Date().toISOString(),
            success: false,
            error: error.message || error
          }
        });

      }
    } catch (updateError) {
      console.error(`❌ Failed to handle verification failure for commit ${commitId}:`, updateError);
    }
  }

  /**
   * Process deadline violation
   */
  async processDeadlineViolation(commitId: string, deadline: Date): Promise<void> {
    try {
      const commit = await this.dbService.getDocument('commits', commitId);
      if (!commit) return;

      // Update submission status to missed
      await this.dbService.updateDocument('commits', commitId, {
        'verification.lastAttempt': {
          timestamp: new Date().toISOString(),
          success: true,
          verified: false,
          error: `Deadline violation: ${deadline.toISOString()}`
        }
      });

      // Update user commit progress
      const userCommit = await this.dbService.getDocument(`users/${commit.userId}/commits`, commitId);
      if (userCommit) {
        await this.dbService.updateDocument(`users/${commit.userId}/commits`, commitId, {
          'progress.missedReports': this.dbService.increment(1),
          'progress.currentStreak': 0,
          'schedulingStatus.missedDeadlines': this.dbService.increment(1)
        });
      }

    } catch (error) {
      console.error(`❌ Failed to process deadline violation for commit ${commitId}:`, error);
    }
  }

  /**
   * Get GitHub service instance (placeholder)
   */
  private async getGitHubService(accessToken: string): Promise<any> {
    // This would return your existing GitHub service instance
    // For now, return a mock service
    return {
      getRecentCommits: async (repo: string, since: Date, userId: string) => {
        // Mock implementation - replace with actual GitHub API calls
        return [];
      }
    };
  }

  /**
   * Get Strava service instance (placeholder)
   */
  private async getStravaService(accessToken: string): Promise<any> {
    // This would return your Strava service instance
    // For now, return a mock service
    return {
      getRecentActivities: async (since: Date, activityType?: string) => {
        // Mock implementation - replace with actual Strava API calls
        return [];
      }
    };
  }

  /**
   * Perform auto-verification based on commit type
   */
  async performAutoVerification(
    commitId: string,
    userId: string,
    schedulerData: any
  ): Promise<any> {
    try {

      switch (schedulerData.verificationType) {
        case 'github':
          return await this.verifyGitHubCommit(commitId, {
            repositoryName: schedulerData.repositoryName,
            minCommits: schedulerData.minCommits || 1,
            verificationWindow: schedulerData.verificationWindow || 24,
            userId
          });

        case 'strava':
          return await this.verifyStravaActivity(commitId, {
            activityType: schedulerData.activityType,
            minDuration: schedulerData.minDuration,
            verificationWindow: schedulerData.verificationWindow || 24,
            userId
          });

        default:
          return {
            success: false,
            verified: false,
            error: `Unsupported verification type: ${schedulerData.verificationType}`,
            timestamp: new Date().toISOString(),
            verificationType: 'automatic'
          };
      }

    } catch (error) {
      console.error(`❌ Auto-verification failed for commit ${commitId}:`, error);
      return {
        success: false,
        verified: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        verificationType: 'automatic'
      };
    }
  }
}
