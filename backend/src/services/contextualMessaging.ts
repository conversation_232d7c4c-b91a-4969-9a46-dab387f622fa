/**
 * Contextual Messaging Service
 * Provides sophisticated, context-aware notification content
 * Adapts messaging based on user progress, time of day, and program characteristics
 */

export interface MessageContext {
  userName: string;
  activityName: string; // Generic name for program or commitment
  activityType: 'program' | 'commitment';

  // Program-specific fields
  dayNumber?: number;
  totalDays?: number;
  programType?: string;

  // Commitment-specific fields
  commitmentTitle?: string;
  reportingFrequency?: 'daily' | 'weekly' | 'monthly' | 'once';
  amountAtRisk?: number;
  amountLost?: number;
  verificationType?: string;
  hoursUntilDeadline?: number;

  // Common fields
  streakCount?: number;
  lastSubmissionDays?: number; // Days since last submission
  progressPercentage?: number; // 0-100
  timeOfDay?: 'morning' | 'afternoon' | 'evening' | 'night';
  isWeekend?: boolean;
  timezone?: string;
  totalReports?: number;
  completedReports?: number;
  missedReports?: number;
}

export interface ContextualMessage {
  title: string;
  message: string;
  tone: 'encouraging' | 'neutral' | 'urgent' | 'celebratory';
}

export class ContextualMessagingService {
  
  /**
   * Generate contextual reminder message for programs or commitments
   */
  static generateReminderMessage(context: MessageContext): ContextualMessage {
    if (context.activityType === 'commitment') {
      return this.generateCommitmentReminderMessage(context);
    } else {
      return this.generateProgramReminderMessage(context);
    }
  }

  /**
   * Generate contextual reminder message for programs
   */
  private static generateProgramReminderMessage(context: MessageContext): ContextualMessage {
    // Determine message tone based on context
    const tone = this.determineTone(context);

    // Generate time-appropriate greeting
    const greeting = this.getTimeBasedGreeting(context.timeOfDay);

    // Generate progress-aware content
    const progressContext = this.getProgressContext(context);

    // Generate sophisticated message
    const title = this.generateTitle(context, tone);
    const message = this.generateMessage(context, tone, greeting, progressContext);

    return { title, message, tone };
  }

  /**
   * Generate contextual reminder message for commitments
   */
  private static generateCommitmentReminderMessage(context: MessageContext): ContextualMessage {
    // Determine tone based on financial stakes and deadline
    const tone = this.determineCommitmentTone(context);

    // Generate commitment-specific title and message
    const title = this.generateCommitmentTitle(context, tone);
    const message = this.generateCommitmentMessage(context, tone);

    return { title, message, tone };
  }

  /**
   * Generate urgent reminder message for programs or commitments
   */
  static generateUrgentMessage(context: MessageContext & { hoursLeft: number }): ContextualMessage {
    if (context.activityType === 'commitment') {
      return this.generateCommitmentUrgentMessage(context);
    } else {
      return this.generateProgramUrgentMessage(context);
    }
  }

  /**
   * Generate urgent reminder message for programs
   */
  private static generateProgramUrgentMessage(context: MessageContext & { hoursLeft: number }): ContextualMessage {
    const { hoursLeft, activityName } = context;

    const title = "Deadline Approaching";
    const message = `${hoursLeft}h remaining to log "${activityName}" progress. Your consistency defines your success.`;

    return { title, message, tone: 'urgent' };
  }

  /**
   * Generate urgent reminder message for commitments
   */
  private static generateCommitmentUrgentMessage(context: MessageContext & { hoursLeft: number }): ContextualMessage {
    const { hoursLeft, activityName, amountAtRisk } = context;

    const title = hoursLeft <= 2 ? "Deadline Imminent" : "Commitment Due Soon";
    const stakeText = amountAtRisk ? `$${amountAtRisk}` : 'Your stake';
    const message = `${stakeText} at risk. "${activityName}" deadline in ${hoursLeft}h. Immediate action required.`;

    return { title, message, tone: 'urgent' };
  }

  /**
   * Generate milestone celebration message for programs or commitments
   */
  static generateMilestoneMessage(context: MessageContext): ContextualMessage {
    if (context.activityType === 'commitment') {
      return this.generateCommitmentMilestoneMessage(context);
    } else {
      return this.generateProgramMilestoneMessage(context);
    }
  }

  /**
   * Generate milestone message for programs
   */
  private static generateProgramMilestoneMessage(context: MessageContext): ContextualMessage {
    const { streakCount, activityName } = context;

    const title = "Milestone Achieved";
    const message = `${streakCount} consecutive days of "${activityName}" completed. Excellence is becoming your standard.`;

    return { title, message, tone: 'celebratory' };
  }

  /**
   * Generate milestone message for commitments
   */
  private static generateCommitmentMilestoneMessage(context: MessageContext): ContextualMessage {
    const { streakCount, activityName, amountAtRisk } = context;

    const title = "Commitment Milestone";
    const savedAmount = amountAtRisk && streakCount ? amountAtRisk * streakCount : 0;
    const message = savedAmount > 0
      ? `${streakCount} consecutive reports for "${activityName}". You've protected $${savedAmount} through consistent action.`
      : `${streakCount} consecutive reports for "${activityName}" completed. Your discipline is remarkable.`;

    return { title, message, tone: 'celebratory' };
  }

  /**
   * Determine appropriate tone based on context (for programs)
   */
  private static determineTone(context: MessageContext): 'encouraging' | 'neutral' | 'urgent' | 'celebratory' {
    const { lastSubmissionDays, streakCount, progressPercentage } = context;

    // Celebratory for milestones
    if (streakCount && (streakCount % 7 === 0 || streakCount % 30 === 0)) {
      return 'celebratory';
    }

    // Encouraging for those who missed recent days
    if (lastSubmissionDays && lastSubmissionDays > 1) {
      return 'encouraging';
    }

    // Urgent for low progress
    if (progressPercentage && progressPercentage < 30) {
      return 'urgent';
    }

    return 'neutral';
  }

  /**
   * Determine appropriate tone for commitments based on financial stakes and deadline
   */
  private static determineCommitmentTone(context: MessageContext): 'encouraging' | 'neutral' | 'urgent' | 'celebratory' {
    const { hoursUntilDeadline, amountAtRisk, streakCount, missedReports } = context;

    // Celebratory for streak milestones
    if (streakCount && (streakCount % 5 === 0 || streakCount % 10 === 0)) {
      return 'celebratory';
    }

    // Urgent for approaching deadlines with money at risk
    if (hoursUntilDeadline && hoursUntilDeadline <= 6 && amountAtRisk && amountAtRisk > 0) {
      return 'urgent';
    }

    // Urgent for high-value commitments
    if (amountAtRisk && amountAtRisk >= 100) {
      return 'urgent';
    }

    // Encouraging for those who have missed reports
    if (missedReports && missedReports > 0) {
      return 'encouraging';
    }

    return 'neutral';
  }

  /**
   * Generate time-based greeting
   */
  private static getTimeBasedGreeting(timeOfDay?: string): string {
    switch (timeOfDay) {
      case 'morning':
        return 'Good morning';
      case 'afternoon':
        return 'Good afternoon';
      case 'evening':
        return 'Good evening';
      default:
        return 'Hello';
    }
  }

  /**
   * Generate progress context
   */
  private static getProgressContext(context: MessageContext): string {
    const { dayNumber, totalDays, streakCount, progressPercentage } = context;
    
    if (progressPercentage && progressPercentage > 80) {
      return 'You\'re in the final stretch.';
    }
    
    if (streakCount && streakCount > 14) {
      return 'Your momentum is building.';
    }
    
    if (dayNumber && totalDays && dayNumber / totalDays > 0.5) {
      return 'You\'ve crossed the halfway point.';
    }
    
    return 'Every step forward matters.';
  }

  /**
   * Generate sophisticated title for programs
   */
  private static generateTitle(context: MessageContext, tone: string): string {
    const { dayNumber } = context;

    switch (tone) {
      case 'celebratory':
        return 'Achievement Unlocked';
      case 'urgent':
        return 'Action Required';
      case 'encouraging':
        return 'Ready to Continue';
      default:
        return dayNumber ? `Day ${dayNumber} Check-in` : 'Progress Check-in';
    }
  }

  /**
   * Generate sophisticated title for commitments
   */
  private static generateCommitmentTitle(context: MessageContext, tone: string): string {
    const { amountAtRisk, hoursUntilDeadline } = context;

    switch (tone) {
      case 'celebratory':
        return 'Streak Milestone';
      case 'urgent':
        if (hoursUntilDeadline && hoursUntilDeadline <= 2) {
          return 'Deadline Imminent';
        }
        return amountAtRisk && amountAtRisk >= 100 ? 'High-Stakes Reminder' : 'Commitment Due';
      case 'encouraging':
        return 'Back on Track';
      default:
        return 'Commitment Check-in';
    }
  }

  /**
   * Generate sophisticated message for programs
   */
  private static generateMessage(
    context: MessageContext,
    tone: string,
    _greeting: string,
    progressContext: string
  ): string {
    const { activityName, dayNumber } = context;

    const baseMessage = dayNumber
      ? `Day ${dayNumber} of "${activityName}" awaits your attention.`
      : `Time to log your "${activityName}" progress.`;

    switch (tone) {
      case 'celebratory':
        return `${baseMessage} ${progressContext} Your dedication is remarkable.`;
      case 'urgent':
        return `${baseMessage} Consistency creates transformation.`;
      case 'encouraging':
        return `${baseMessage} ${progressContext} Your journey continues.`;
      default:
        return `${baseMessage} ${progressContext}`;
    }
  }

  /**
   * Generate sophisticated message for commitments
   */
  private static generateCommitmentMessage(context: MessageContext, tone: string): string {
    const {
      activityName,
      amountAtRisk,
      hoursUntilDeadline,
      streakCount,
      verificationType,
      reportingFrequency
    } = context;

    const verificationText = this.getVerificationText(verificationType);
    const stakeText = amountAtRisk ? `$${amountAtRisk}` : 'your stake';

    switch (tone) {
      case 'celebratory':
        return `${streakCount} consecutive reports for "${activityName}" completed. Your commitment is paying dividends.`;
      case 'urgent':
        if (hoursUntilDeadline && hoursUntilDeadline <= 2) {
          return `${stakeText} at risk. "${activityName}" deadline in ${hoursUntilDeadline}h. ${verificationText} required now.`;
        }
        return `"${activityName}" commitment due. ${stakeText} depends on your action. ${verificationText} to secure your stake.`;
      case 'encouraging':
        return `"${activityName}" awaits your return. Previous setbacks don't define your commitment. ${verificationText} to continue.`;
      default:
        return `"${activityName}" ${reportingFrequency} check-in due. ${verificationText} to maintain your commitment.`;
    }
  }

  /**
   * Get verification text based on verification type
   */
  private static getVerificationText(verificationType?: string): string {
    switch (verificationType) {
      case 'camera':
      case 'photo':
        return 'Photo verification';
      case 'gps':
        return 'Location check-in';
      case 'camera+gps':
        return 'Photo and location verification';
      case 'github':
        return 'Code commit verification';
      case 'text':
        return 'Text submission';
      default:
        return 'Verification';
    }
  }

  /**
   * Generate contextual SMS message (shorter format) for programs or commitments
   */
  static generateSMSMessage(context: MessageContext): string {
    if (context.activityType === 'commitment') {
      return this.generateCommitmentSMSMessage(context);
    } else {
      return this.generateProgramSMSMessage(context);
    }
  }

  /**
   * Generate SMS message for programs
   */
  private static generateProgramSMSMessage(context: MessageContext): string {
    const { activityName, dayNumber, streakCount } = context;

    if (streakCount && streakCount > 0) {
      return `Day ${dayNumber || 1} of "${activityName}" awaits. ${streakCount}-day streak continues with your next action.`;
    }

    return `Day ${dayNumber || 1} of "${activityName}" awaits your attention. Progress builds momentum.`;
  }

  /**
   * Generate SMS message for commitments
   */
  private static generateCommitmentSMSMessage(context: MessageContext): string {
    const { activityName, amountAtRisk, hoursUntilDeadline, streakCount } = context;

    if (hoursUntilDeadline && hoursUntilDeadline <= 6 && amountAtRisk) {
      return `"${activityName}" deadline in ${hoursUntilDeadline}h. $${amountAtRisk} at risk. Action required.`;
    }

    if (streakCount && streakCount > 0) {
      return `"${activityName}" check-in due. ${streakCount}-report streak continues with your submission.`;
    }

    return `"${activityName}" commitment due. Your stake depends on consistent action.`;
  }

  /**
   * Generate contextual email subject for programs or commitments
   */
  static generateEmailSubject(context: MessageContext): string {
    if (context.activityType === 'commitment') {
      return this.generateCommitmentEmailSubject(context);
    } else {
      return this.generateProgramEmailSubject(context);
    }
  }

  /**
   * Generate email subject for programs
   */
  private static generateProgramEmailSubject(context: MessageContext): string {
    const { activityName, dayNumber, streakCount } = context;

    if (streakCount && streakCount % 7 === 0) {
      return `Week ${Math.floor(streakCount / 7)} Complete - ${activityName}`;
    }

    if (dayNumber) {
      return `${activityName} - Day ${dayNumber} Progress`;
    }

    return `${activityName} - Progress Check-in`;
  }

  /**
   * Generate email subject for commitments
   */
  private static generateCommitmentEmailSubject(context: MessageContext): string {
    const { activityName, amountAtRisk, hoursUntilDeadline, streakCount, reportingFrequency } = context;

    if (hoursUntilDeadline && hoursUntilDeadline <= 6 && amountAtRisk) {
      return `Urgent: ${activityName} - $${amountAtRisk} at Risk`;
    }

    if (streakCount && streakCount % 5 === 0) {
      return `${streakCount} Reports Strong - ${activityName}`;
    }

    const frequencyText = reportingFrequency ? reportingFrequency.charAt(0).toUpperCase() + reportingFrequency.slice(1) : '';
    return `${activityName} - ${frequencyText} Check-in`;
  }

  /**
   * Get current time of day based on timezone
   */
  static getTimeOfDay(timezone: string = 'UTC'): 'morning' | 'afternoon' | 'evening' | 'night' {
    const now = new Date();
    const hour = parseInt(now.toLocaleString('en-US', { 
      timeZone: timezone, 
      hour12: false, 
      hour: '2-digit' 
    }));
    
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 22) return 'evening';
    return 'night';
  }

  /**
   * Calculate progress percentage
   */
  static calculateProgressPercentage(dayNumber: number, totalDays: number): number {
    return Math.round((dayNumber / totalDays) * 100);
  }
}
