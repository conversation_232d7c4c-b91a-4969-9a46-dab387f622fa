/**
 * Commit Scheduler Service
 * Handles timezone-aware commit scheduling operations including lifecycle management,
 * daily checkers, notifications, and auto-verification
 */

import { SchedulerService } from './schedulerService';
// import { getCloudConfig, getSchedulerConfig } from '../utils/config';
import { monitorPerformance, logSchedulerEvent } from '../utils/commitMonitoring';

export interface CommitSchedulerConfig {
  commitId: string;
  userId: string;
  timezone: string;
  startDate: Date;
  endDate?: Date;
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
  deadlineType: 'before' | 'after' | 'between' | 'midnight';
  deadlineTime?: string; // HH:MM format
  deadlineEndTime?: string; // For 'between' type
  autoVerificationEnabled: boolean;
  verificationType: string;
  notificationPreferences?: CommitNotificationPreferences;
}

export interface CommitNotificationPreferences {
  reminderTime: string; // HH:MM format, default "19:00"
  urgentRemindersEnabled: boolean;
  weeklyDigestEnabled: boolean;
  weeklyDigestDay: 'sunday' | 'monday';
  autoVerificationNotifications: boolean;
}

export interface CommitCheckerConfig {
  commitId: string;
  userId: string;
  timezone: string;
  deadlineType: 'before' | 'after' | 'between' | 'midnight';
  deadlineTime?: string;
  deadlineEndTime?: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
  gracePeriodHours?: number;
}

export interface CommitVerificationConfig {
  commitId: string;
  userId: string;
  timezone: string;
  verificationType: string;
  verificationWindow: number; // hours
  repositoryName?: string; // for GitHub
  minCommits?: number; // for GitHub
}

export interface CommitNotificationConfig {
  commitId: string;
  userId: string;
  timezone: string;
  commitTitle: string;
  reminderTime: string;
  urgentRemindersEnabled: boolean;
  deadlineType: 'before' | 'after' | 'between' | 'midnight';
  deadlineTime?: string;
  deadlineEndTime?: string;
}

export class CommitSchedulerService {
  private schedulerService: SchedulerService;

  constructor() {
    this.schedulerService = new SchedulerService();
  }

  /**
   * Set up complete commit scheduling when commit is created
   */
  @monitorPerformance('setupCommitScheduling')
  async setupCommitScheduling(config: CommitSchedulerConfig): Promise<{
    success: boolean;
    message: string;
    jobsCreated: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let jobsCreated = 0;

    try {

      // 1. Create commit start scheduler (if start date is in future)
      if (config.startDate > new Date()) {
        try {
          await this.createCommitStartJob(config);
          jobsCreated++;
          await logSchedulerEvent('create', `commit-starter-${config.commitId}`, true);
        } catch (error) {
          const errorMsg = `Failed to create commit start job: ${error}`;
          errors.push(errorMsg);
          console.error(errorMsg);
          await logSchedulerEvent('create', `commit-starter-${config.commitId}`, false, errorMsg);
        }
      } else {
        // Start immediately if start date is now or in past
        try {
          await this.activateCommitScheduling(config);
          jobsCreated += await this.getActiveJobsCount(config);
        } catch (error) {
          const errorMsg = `Failed to activate commit scheduling: ${error}`;
          errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      // 2. Create commit end scheduler (if end date is specified)
      if (config.endDate) {
        try {
          await this.createCommitEndJob(config);
          jobsCreated++;
        } catch (error) {
          const errorMsg = `Failed to create commit end job: ${error}`;
          errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      return {
        success: errors.length === 0,
        message: errors.length === 0 
          ? `Commit scheduling set up successfully with ${jobsCreated} jobs`
          : `Commit scheduling set up with ${errors.length} errors`,
        jobsCreated,
        errors
      };

    } catch (error) {
      const errorMsg = `Failed to set up commit scheduling: ${error}`;
      console.error(errorMsg);
      return {
        success: false,
        message: errorMsg,
        jobsCreated,
        errors: [errorMsg]
      };
    }
  }

  /**
   * Create commit start job
   */
  private async createCommitStartJob(config: CommitSchedulerConfig): Promise<void> {
    const jobName = `commit-starter-${config.commitId}`;

    // Normalize start date to midnight in user's timezone
    const startDateAtMidnight = this.normalizeToMidnight(config.startDate, config.timezone);

    // Create cron expression for specific date at midnight in user's timezone
    const startCron = this.createCronForSpecificDateAtMidnight(startDateAtMidnight, config.timezone);

    await this.schedulerService.createJob({
      name: jobName,
      functionName: 'commitStarter',
      schedule: startCron,
      timeZone: config.timezone,
      data: {
        commitId: config.commitId,
        userId: config.userId,
        schedulingConfig: {
          ...config,
          startDate: startDateAtMidnight // Use normalized start date
        }
      },
      description: `Start commit ${config.commitId} for user ${config.userId} at midnight on ${startDateAtMidnight.toDateString()} in ${config.timezone}`
    });

    // Track the job ID in the commit document
    await this.addSchedulerJobId(config.commitId, jobName);
  }

  /**
   * Create commit end job
   */
  private async createCommitEndJob(config: CommitSchedulerConfig): Promise<void> {
    if (!config.endDate) return;

    const jobName = `commit-ender-${config.commitId}`;

    // Normalize end date to end of day (23:59:59) in user's timezone
    const endDateAtEndOfDay = this.normalizeToEndOfDay(config.endDate, config.timezone);

    // Create cron expression for specific date at end of day in user's timezone
    const endCron = this.createCronForSpecificDateAtEndOfDay(endDateAtEndOfDay, config.timezone);

    await this.schedulerService.createJob({
      name: jobName,
      functionName: 'commitEnder',
      schedule: endCron,
      timeZone: config.timezone,
      data: {
        commitId: config.commitId,
        userId: config.userId,
        reason: 'duration_completed'
      },
      description: `End commit ${config.commitId} for user ${config.userId} at end of day on ${endDateAtEndOfDay.toDateString()} in ${config.timezone}`
    });

    // Track the job ID in the commit document
    await this.addSchedulerJobId(config.commitId, jobName);
  }

  /**
   * Activate commit scheduling (called when commit starts)
   */
  async activateCommitScheduling(config: CommitSchedulerConfig): Promise<void> {
    // 1. Create daily checker job
    await this.createCommitDailyChecker(config);
    
    // 2. Create notification scheduler
    await this.createCommitNotificationScheduler(config);
    
    // 3. Create auto-verification job (if enabled)
    if (config.autoVerificationEnabled) {
      await this.createCommitAutoVerificationJob(config);
    }
  }

  /**
   * Get count of active jobs for a commit
   */
  private async getActiveJobsCount(config: CommitSchedulerConfig): Promise<number> {
    let count = 2; // daily checker + notification scheduler
    if (config.autoVerificationEnabled) count++;
    return count;
  }

  /**
   * Normalize a date to midnight in the specified timezone
   */
  private normalizeToMidnight(date: Date, timezone: string): Date {
    // Create a new date at midnight in the user's timezone
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    // Create date at midnight in local time, then adjust for timezone
    const midnightLocal = new Date(year, month, day, 0, 0, 0, 0);

    // For now, return the midnight date (timezone conversion can be added later if needed)
    return midnightLocal;
  }

  /**
   * Normalize a date to end of day (23:59:59) in the specified timezone
   */
  private normalizeToEndOfDay(date: Date, timezone: string): Date {
    // Create a new date at end of day in the user's timezone
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    // Create date at end of day in local time (23:59:59)
    const endOfDayLocal = new Date(year, month, day, 23, 59, 59, 999);

    // For now, return the end of day date (timezone conversion can be added later if needed)
    return endOfDayLocal;
  }

  /**
   * Create cron expression for specific date at midnight in user's timezone
   */
  private createCronForSpecificDateAtMidnight(date: Date, timezone: string): string {
    const minute = 0;
    const hour = 0;
    const day = date.getDate();
    const month = date.getMonth() + 1; // JavaScript months are 0-based
    return `${minute} ${hour} ${day} ${month} *`;
  }

  /**
   * Create cron expression for specific date at end of day in user's timezone
   */
  private createCronForSpecificDateAtEndOfDay(date: Date, timezone: string): string {
    const minute = 59;
    const hour = 23;
    const day = date.getDate();
    const month = date.getMonth() + 1; // JavaScript months are 0-based
    return `${minute} ${hour} ${day} ${month} *`;
  }

  /**
   * Create daily checker job for deadline enforcement
   */
  async createCommitDailyChecker(config: CommitSchedulerConfig | CommitCheckerConfig): Promise<void> {
    const sanitizedUserId = this.schedulerService.sanitizeUserIdForJobName(config.userId);
    const jobName = `commit-daily-checker-${config.commitId}-${sanitizedUserId}`;

    // Calculate when to run the checker based on deadline type
    const checkerSchedule = this.calculateCheckerSchedule(config);

    await this.schedulerService.createJob({
      name: jobName,
      functionName: 'commitDailyChecker',
      schedule: checkerSchedule.cronExpression,
      timeZone: config.timezone,
      data: {
        commitId: config.commitId,
        userId: config.userId,
        deadlineType: config.deadlineType,
        deadlineTime: config.deadlineTime,
        deadlineEndTime: config.deadlineEndTime,
        frequency: config.frequency,
        gracePeriodHours: (config as CommitCheckerConfig).gracePeriodHours || 0,
        isRecurring: true
      },
      description: `Daily checker for commit ${config.commitId}, user ${config.userId} - ${checkerSchedule.description}`
    });

    // Track the job ID in the commit document
    await this.addSchedulerJobId(config.commitId, jobName);
  }

  /**
   * Create notification scheduler job
   */
  async createCommitNotificationScheduler(config: CommitSchedulerConfig | CommitNotificationConfig): Promise<void> {
    const sanitizedUserId = this.schedulerService.sanitizeUserIdForJobName(config.userId);
    const jobName = `commit-notification-${config.commitId}-${sanitizedUserId}`;

    const notificationPrefs = (config as CommitSchedulerConfig).notificationPreferences || {
      reminderTime: '19:00',
      urgentRemindersEnabled: true,
      weeklyDigestEnabled: false,
      weeklyDigestDay: 'sunday' as const,
      autoVerificationNotifications: true
    };

    const reminderTime = (config as CommitNotificationConfig).reminderTime || notificationPrefs.reminderTime;
    const [hour, minute] = reminderTime.split(':').map(Number);

    await this.schedulerService.createJob({
      name: jobName,
      functionName: 'commitNotificationScheduler',
      schedule: `${minute} ${hour} * * *`, // Daily at specified time
      timeZone: config.timezone,
      data: {
        commitId: config.commitId,
        userId: config.userId,
        commitTitle: (config as CommitNotificationConfig).commitTitle || 'Your Commitment',
        reminderTime: reminderTime,
        urgentRemindersEnabled: (config as CommitNotificationConfig).urgentRemindersEnabled ?? notificationPrefs.urgentRemindersEnabled,
        deadlineType: config.deadlineType,
        deadlineTime: config.deadlineTime,
        deadlineEndTime: config.deadlineEndTime,
        isRecurring: true
      },
      description: `Notification scheduler for commit ${config.commitId}, user ${config.userId} at ${reminderTime}`
    });

    // Track the job ID in the commit document
    await this.addSchedulerJobId(config.commitId, jobName);
  }

  /**
   * Create auto-verification job (for GitHub/Strava)
   */
  async createCommitAutoVerificationJob(config: CommitSchedulerConfig | CommitVerificationConfig): Promise<void> {
    const sanitizedUserId = this.schedulerService.sanitizeUserIdForJobName(config.userId);
    const jobName = `commit-auto-verify-${config.commitId}-${sanitizedUserId}`;

    await this.schedulerService.createJob({
      name: jobName,
      functionName: 'commitAutoVerifier',
      schedule: '0 0 * * *', // Daily at midnight in user's timezone
      timeZone: config.timezone,
      data: {
        commitId: config.commitId,
        userId: config.userId,
        verificationType: config.verificationType,
        verificationWindow: (config as CommitVerificationConfig).verificationWindow || 24,
        repositoryName: (config as CommitVerificationConfig).repositoryName,
        minCommits: (config as CommitVerificationConfig).minCommits || 1,
        isRecurring: true
      },
      description: `Auto-verification for commit ${config.commitId}, user ${config.userId} - ${config.verificationType}`
    });

    // Track the job ID in the commit document
    await this.addSchedulerJobId(config.commitId, jobName);
  }

  /**
   * Calculate when to run the daily checker based on deadline configuration
   */
  private calculateCheckerSchedule(config: CommitSchedulerConfig | CommitCheckerConfig): {
    cronExpression: string;
    description: string;
  } {
    const gracePeriodHours = (config as CommitCheckerConfig).gracePeriodHours || 1;

    switch (config.deadlineType) {
      case 'before':
        if (config.deadlineTime) {
          const [hour, minute] = config.deadlineTime.split(':').map(Number);
          const checkerHour = (hour + gracePeriodHours) % 24;
          return {
            cronExpression: `${minute} ${checkerHour} * * *`,
            description: `Check ${gracePeriodHours}h after ${config.deadlineTime} deadline`
          };
        }
        break;

      case 'after':
        if (config.deadlineTime) {
          const [hour, minute] = config.deadlineTime.split(':').map(Number);
          const checkerHour = (hour + gracePeriodHours) % 24;
          return {
            cronExpression: `${minute} ${checkerHour} * * *`,
            description: `Check ${gracePeriodHours}h after ${config.deadlineTime} start time`
          };
        }
        break;

      case 'between':
        if (config.deadlineEndTime) {
          const [hour, minute] = config.deadlineEndTime.split(':').map(Number);
          const checkerHour = (hour + gracePeriodHours) % 24;
          return {
            cronExpression: `${minute} ${checkerHour} * * *`,
            description: `Check ${gracePeriodHours}h after ${config.deadlineEndTime} end time`
          };
        }
        break;

      case 'midnight':
      default:
        return {
          cronExpression: `0 ${gracePeriodHours} * * *`,
          description: `Check ${gracePeriodHours}h after midnight deadline`
        };
    }

    // Fallback to 1 AM check
    return {
      cronExpression: '0 1 * * *',
      description: 'Check 1h after midnight (fallback)'
    };
  }

  /**
   * Delete all scheduler jobs for a commit
   */
  async deleteCommitScheduling(commitId: string, userId: string): Promise<{
    success: boolean;
    message: string;
    jobsDeleted: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let jobsDeleted = 0;

    try {
      // Get the actual job IDs from the commit document
      const jobsToDelete = await this.getSchedulerJobIds(commitId);

      if (jobsToDelete.length === 0) {
        return {
          success: true,
          message: 'No scheduler jobs to delete',
          jobsDeleted: 0,
          errors: []
        };
      }


      for (const jobName of jobsToDelete) {
        try {
          await this.schedulerService.deleteJob(jobName);
          jobsDeleted++;
          await logSchedulerEvent('delete', jobName, true);
        } catch (error: any) {
          if (error.code === 5) { // NOT_FOUND
            // Still count as "deleted" since it's gone
            jobsDeleted++;
          } else {
            const errorMsg = `Failed to delete job ${jobName}: ${error}`;
            errors.push(errorMsg);
            console.error(errorMsg);
            await logSchedulerEvent('delete', jobName, false, errorMsg);
          }
        }
      }

      // Clear the scheduler job IDs from the commit document
      await this.clearSchedulerJobIds(commitId);

    } catch (error: any) {
      const errorMsg = `Failed to get scheduler jobs for commit ${commitId}: ${error}`;
      errors.push(errorMsg);
      console.error(errorMsg);
    }

    return {
      success: errors.length === 0,
      message: errors.length === 0
        ? `Successfully deleted ${jobsDeleted} scheduler jobs`
        : `Deleted ${jobsDeleted} jobs with ${errors.length} errors`,
      jobsDeleted,
      errors
    };
  }

  /**
   * Update commit scheduling configuration
   */
  async updateCommitScheduling(
    commitId: string,
    userId: string,
    newConfig: Partial<CommitSchedulerConfig>
  ): Promise<{
    success: boolean;
    message: string;
    jobsUpdated: number;
    errors: string[];
  }> {
    try {
      // Delete existing jobs
      const deleteResult = await this.deleteCommitScheduling(commitId, userId);

      // Create new jobs with updated config
      const fullConfig: CommitSchedulerConfig = {
        commitId,
        userId,
        timezone: newConfig.timezone || 'UTC',
        startDate: newConfig.startDate || new Date(),
        endDate: newConfig.endDate,
        frequency: newConfig.frequency || 'daily',
        deadlineType: newConfig.deadlineType || 'midnight',
        deadlineTime: newConfig.deadlineTime,
        deadlineEndTime: newConfig.deadlineEndTime,
        autoVerificationEnabled: newConfig.autoVerificationEnabled || false,
        verificationType: newConfig.verificationType || 'manual',
        notificationPreferences: newConfig.notificationPreferences
      };

      const setupResult = await this.setupCommitScheduling(fullConfig);

      return {
        success: setupResult.success,
        message: `Updated commit scheduling: ${deleteResult.jobsDeleted} deleted, ${setupResult.jobsCreated} created`,
        jobsUpdated: setupResult.jobsCreated,
        errors: [...deleteResult.errors, ...setupResult.errors]
      };

    } catch (error) {
      const errorMsg = `Failed to update commit scheduling: ${error}`;
      console.error(errorMsg);
      return {
        success: false,
        message: errorMsg,
        jobsUpdated: 0,
        errors: [errorMsg]
      };
    }
  }

  /**
   * Clean up expired commit jobs
   */
  async cleanupExpiredCommitJobs(commitId: string, userId: string): Promise<void> {
    await this.deleteCommitScheduling(commitId, userId);
  }

  /**
   * Check if commit has active scheduling
   */
  async hasActiveScheduling(commitId: string, userId: string): Promise<boolean> {
    const sanitizedUserId = this.schedulerService.sanitizeUserIdForJobName(userId);
    const checkerJobName = `commit-daily-checker-${commitId}-${sanitizedUserId}`;

    try {
      return await this.schedulerService.jobExists(checkerJobName);
    } catch (error) {
      console.error(`Error checking active scheduling for commit ${commitId}:`, error);
      return false;
    }
  }

  /**
   * Pause commit scheduling (disable jobs without deleting)
   */
  async pauseCommitScheduling(commitId: string, userId: string): Promise<{
    success: boolean;
    message: string;
  }> {
    // Note: Google Cloud Scheduler doesn't support pausing jobs directly
    // We would need to delete and recreate them, or implement a flag in the job data
    // For now, we'll delete the jobs and require reactivation
    const result = await this.deleteCommitScheduling(commitId, userId);

    return {
      success: result.success,
      message: result.success
        ? `Commit scheduling paused (${result.jobsDeleted} jobs deleted)`
        : `Failed to pause commit scheduling: ${result.errors.join(', ')}`
    };
  }

  /**
   * Resume commit scheduling
   */
  async resumeCommitScheduling(config: CommitSchedulerConfig): Promise<{
    success: boolean;
    message: string;
  }> {
    // Activate scheduling immediately (since we're resuming)
    try {
      await this.activateCommitScheduling(config);
      return {
        success: true,
        message: 'Commit scheduling resumed successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to resume commit scheduling: ${error}`
      };
    }
  }

  /**
   * Get scheduler job IDs from commit document
   */
  private async getSchedulerJobIds(commitId: string): Promise<string[]> {
    try {
      const commitDoc = await this.dbService.getDocument('commits', commitId);

      if (!commitDoc) {
        console.warn(`⚠️ Commit ${commitId} not found when getting scheduler job IDs`);
        return [];
      }

      const schedulerJobIds = commitDoc.scheduling?.schedulerJobIds || [];

      return schedulerJobIds;
    } catch (error) {
      console.error(`❌ Failed to get scheduler job IDs for commit ${commitId}:`, error);
      return [];
    }
  }

  /**
   * Add scheduler job ID to commit document
   */
  private async addSchedulerJobId(commitId: string, jobName: string): Promise<void> {
    try {
      await this.dbService.updateDocument('commits', commitId, {
        'scheduling.schedulerJobIds': this.dbService.arrayUnion([jobName])
      });
    } catch (error) {
      console.error(`❌ Failed to add scheduler job ID ${jobName} to commit ${commitId}:`, error);
    }
  }

  /**
   * Clear all scheduler job IDs from commit document
   */
  private async clearSchedulerJobIds(commitId: string): Promise<void> {
    try {
      await this.dbService.updateDocument('commits', commitId, {
        'scheduling.schedulerJobIds': [],
        'scheduling.clearedAt': new Date().toISOString()
      });
    } catch (error) {
      console.error(`❌ Failed to clear scheduler job IDs for commit ${commitId}:`, error);
    }
  }

  /**
   * Get database service instance
   */
  private get dbService() {
    if (!this._dbService) {
      const { DatabaseService } = require('./databaseService');
      this._dbService = new DatabaseService();
    }
    return this._dbService;
  }

  private _dbService: any;
}
