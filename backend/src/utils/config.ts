/**
 * Configuration service for Firebase Cloud Functions
 * Centralizes environment variable access and configuration management
 */

export interface CloudConfig {
  projectId: string;
  location: string;
  functionRegion: string;
}

export interface DatabaseConfig {
  collections: {
    programs: string;
    participants: string;
    submissions: string;
    githubOAuthTokens: string;
    schedulerLocks: string;
    users: string;
    notifications: string;
  };
}

export interface SchedulerConfig {
  defaultLocation: string;
  defaultTimeZone: string;
  retryAttempts: number;
  timeoutMs: number;
}

export interface EmailConfig {
  sendgridApiKey: string;
  fromEmail: string;
  fromName: string;
  replyToEmail?: string;
}

export interface SMSConfig {
  twilioAccountSid: string;
  twilioAuthToken: string;
  twilioPhoneNumber: string;
}

/**
 * Get cloud configuration from environment variables
 */
export function getCloudConfig(): CloudConfig {
  const projectId = process.env.GCLOUD_PROJECT || process.env.GCP_PROJECT;
  if (!projectId) {
    throw new Error('Project ID not found in environment variables');
  }

  return {
    projectId,
    location: process.env.FUNCTIONS_REGION || 'us-central1',
    functionRegion: 'us-central1'
  };
}

/**
 * Get database configuration
 */
export function getDatabaseConfig(): DatabaseConfig {
  return {
    collections: {
      programs: 'programs',
      participants: 'participants',
      submissions: 'submissions',
      githubOAuthTokens: 'github_oauth_tokens',
      schedulerLocks: 'scheduler_locks',
      users: 'users',
      notifications: 'notifications'
    }
  };
}

/**
 * Get scheduler configuration
 */
export function getSchedulerConfig(): SchedulerConfig {
  return {
    defaultLocation: 'us-central1',
    defaultTimeZone: 'UTC',
    retryAttempts: 3,
    timeoutMs: 30000
  };
}



/**
 * Get function URL for a given function name
 */
export function getFunctionUrl(functionName: string, config?: CloudConfig): string {
  const cloudConfig = config || getCloudConfig();
  return `https://${cloudConfig.location}-${cloudConfig.projectId}.cloudfunctions.net/${functionName}`;
}

/**
 * Get email configuration from environment variables or Firebase Functions config
 */
export function getEmailConfig(): EmailConfig {
  // Load environment variables from .env file if available
  try {
    require('dotenv').config();
  } catch (error) {
    // dotenv not available, continue
  }

  // Try environment variables first (recommended approach)
  let sendgridApiKey = process.env.SENDGRID_API_KEY;
  let fromEmail = process.env.FROM_EMAIL;
  let fromName = process.env.FROM_NAME;
  let replyToEmail = process.env.REPLY_TO_EMAIL;

  // Fallback to Firebase Functions config (deprecated but still supported)
  if (!sendgridApiKey) {
    try {
      const functions = require('firebase-functions');
      const config = functions.config();

      sendgridApiKey = config.sendgrid?.api_key;
      fromEmail = fromEmail || config.email?.from_email;
      fromName = fromName || config.email?.from_name;
      replyToEmail = replyToEmail || config.email?.reply_to_email;
    } catch (error) {
      // Functions config not available, continue with env vars only
    }
  }

  if (!sendgridApiKey) {
    throw new Error('SendGrid API key not configured. Set SENDGRID_API_KEY environment variable or run: firebase functions:config:set sendgrid.api_key="your-key"');
  }

  return {
    sendgridApiKey,
    fromEmail: fromEmail || '<EMAIL>',
    fromName: fromName || 'Accustom',
    replyToEmail,
  };
}

/**
 * Get SMS configuration from environment variables or Firebase Functions config
 */
export function getSMSConfig(): SMSConfig {
  // Load environment variables from .env file if available
  try {
    require('dotenv').config();
  } catch (error) {
    // dotenv not available, continue
  }

  // Try environment variables first (recommended approach)
  let twilioAccountSid = process.env.TWILIO_ACCOUNT_SID;
  let twilioAuthToken = process.env.TWILIO_AUTH_TOKEN;
  let twilioPhoneNumber = process.env.TWILIO_PHONE_NUMBER;

  // Fallback to Firebase Functions config (deprecated but still supported)
  if (!twilioAccountSid) {
    try {
      const functions = require('firebase-functions');
      const config = functions.config();

      twilioAccountSid = config.twilio?.account_sid;
      twilioAuthToken = twilioAuthToken || config.twilio?.auth_token;
      twilioPhoneNumber = twilioPhoneNumber || config.twilio?.phone_number;
    } catch (error) {
      // Functions config not available, continue with env vars only
    }
  }

  if (!twilioAccountSid || !twilioAuthToken || !twilioPhoneNumber) {
    throw new Error('Twilio configuration incomplete. Set TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, and TWILIO_PHONE_NUMBER environment variables');
  }

  return {
    twilioAccountSid,
    twilioAuthToken,
    twilioPhoneNumber,
  };
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(): void {
  const required = ['GCLOUD_PROJECT', 'GCP_PROJECT'];
  const missing = required.filter(env => !process.env[env]);

  if (missing.length > 0 && !process.env.GCLOUD_PROJECT && !process.env.GCP_PROJECT) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
