/**
 * Comprehensive Error Handling and Retry Mechanisms for Commit Scheduling
 */

import { commitMonitoringService } from './commitMonitoring';

export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeoutMs: number;
  monitoringWindowMs: number;
}

export class CommitErrorHandler {
  private static instance: CommitErrorHandler;
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();

  private constructor() {}

  static getInstance(): CommitErrorHandler {
    if (!CommitErrorHandler.instance) {
      CommitErrorHandler.instance = new CommitErrorHandler();
    }
    return CommitErrorHandler.instance;
  }

  /**
   * Execute function with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig,
    context: {
      operationName: string;
      commitId?: string;
      userId?: string;
    }
  ): Promise<T> {
    let lastError: Error | null = null;
    let attempt = 0;

    while (attempt <= config.maxRetries) {
      try {
        const result = await operation();
        
        // Reset circuit breaker on success
        this.recordSuccess(context.operationName);
        
        if (attempt > 0) {
          await commitMonitoringService.createAlert({
            type: 'info',
            severity: 'low',
            title: 'Operation Recovered',
            message: `${context.operationName} succeeded after ${attempt} retries`,
            commitId: context.commitId,
            userId: context.userId,
            metadata: { attempts: attempt + 1 }
          });
        }
        
        return result;
      } catch (error) {
        lastError = error as Error;
        attempt++;

        // Check if error is retryable
        if (!this.isRetryableError(lastError, config.retryableErrors)) {
          break;
        }

        // Check circuit breaker
        if (this.isCircuitOpen(context.operationName)) {
          throw new Error(`Circuit breaker open for ${context.operationName}`);
        }

        if (attempt <= config.maxRetries) {
          const delay = this.calculateDelay(attempt, config);
          
          await this.sleep(delay);
        }

        // Record failure for circuit breaker
        this.recordFailure(context.operationName);
      }
    }

    // All retries exhausted
    const errorMessage = lastError?.message || 'Unknown error';
    console.error(`❌ Operation ${context.operationName} failed after ${config.maxRetries} retries: ${errorMessage}`);

    await commitMonitoringService.createAlert({
      type: 'error',
      severity: 'high',
      title: 'Operation Failed After Retries',
      message: `${context.operationName} failed after ${config.maxRetries} retries: ${errorMessage}`,
      commitId: context.commitId,
      userId: context.userId,
      metadata: {
        maxRetries: config.maxRetries,
        finalError: errorMessage
      }
    });

    throw lastError || new Error(`Operation ${context.operationName} failed after ${config.maxRetries} retries`);
  }

  /**
   * Execute function with circuit breaker pattern
   */
  async executeWithCircuitBreaker<T>(
    operation: () => Promise<T>,
    operationName: string,
    config: CircuitBreakerConfig
  ): Promise<T> {
    // Initialize circuit breaker if not exists
    if (!this.circuitBreakers.has(operationName)) {
      this.circuitBreakers.set(operationName, {
        state: 'closed',
        failureCount: 0,
        lastFailureTime: 0,
        successCount: 0,
        config
      });
    }

    const breaker = this.circuitBreakers.get(operationName)!;

    // Check if circuit is open
    if (breaker.state === 'open') {
      const timeSinceLastFailure = Date.now() - breaker.lastFailureTime;
      
      if (timeSinceLastFailure < config.resetTimeoutMs) {
        throw new Error(`Circuit breaker open for ${operationName}`);
      } else {
        // Try to reset circuit breaker
        breaker.state = 'half-open';
      }
    }

    try {
      const result = await operation();
      
      // Success - reset or close circuit
      if (breaker.state === 'half-open') {
        breaker.state = 'closed';
        breaker.failureCount = 0;
      }
      
      breaker.successCount++;
      return result;
      
    } catch (error) {
      breaker.failureCount++;
      breaker.lastFailureTime = Date.now();
      
      // Check if we should open the circuit
      if (breaker.failureCount >= config.failureThreshold) {
        breaker.state = 'open';
        
        await commitMonitoringService.createAlert({
          type: 'error',
          severity: 'critical',
          title: 'Circuit Breaker Opened',
          message: `Circuit breaker opened for ${operationName} after ${breaker.failureCount} failures`,
          metadata: { 
            operationName,
            failureCount: breaker.failureCount,
            threshold: config.failureThreshold
          }
        });
      }
      
      throw error;
    }
  }

  /**
   * Handle specific commit scheduling errors
   */
  async handleCommitSchedulingError(
    error: Error,
    context: {
      operation: string;
      commitId: string;
      userId: string;
      jobName?: string;
    }
  ): Promise<void> {
    console.error(`🚨 Commit scheduling error in ${context.operation}:`, error);

    let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';
    let alertType: 'error' | 'warning' | 'info' = 'error';

    // Categorize error severity
    if (error.message.includes('ALREADY_EXISTS')) {
      severity = 'low';
      alertType = 'info';
    } else if (error.message.includes('NOT_FOUND')) {
      severity = 'low';
      alertType = 'warning';
    } else if (error.message.includes('PERMISSION_DENIED')) {
      severity = 'critical';
    } else if (error.message.includes('QUOTA_EXCEEDED')) {
      severity = 'critical';
    } else if (error.message.includes('DEADLINE_EXCEEDED')) {
      severity = 'high';
    }

    await commitMonitoringService.createAlert({
      type: alertType,
      severity,
      title: `Commit Scheduling Error: ${context.operation}`,
      message: error.message,
      commitId: context.commitId,
      userId: context.userId,
      metadata: {
        operation: context.operation,
        jobName: context.jobName,
        errorStack: error.stack
      }
    });

    // Take corrective action based on error type
    await this.takeCorrectiveAction(error, context);
  }

  /**
   * Handle verification errors with specific retry strategies
   */
  async handleVerificationError(
    error: Error,
    context: {
      verificationType: string;
      commitId: string;
      userId: string;
      attempt: number;
    }
  ): Promise<boolean> { // Returns true if should retry
    console.error(`🔍 Verification error for ${context.verificationType}:`, error);

    // GitHub API rate limiting
    if (context.verificationType === 'github' && error.message.includes('rate limit')) {
      await commitMonitoringService.createAlert({
        type: 'warning',
        severity: 'medium',
        title: 'GitHub Rate Limit Hit',
        message: 'GitHub API rate limit exceeded, will retry later',
        commitId: context.commitId,
        userId: context.userId
      });
      
      // Wait longer for rate limit reset
      await this.sleep(60 * 60 * 1000); // 1 hour
      return context.attempt < 3;
    }

    // Strava API errors
    if (context.verificationType === 'strava' && error.message.includes('unauthorized')) {
      await commitMonitoringService.createAlert({
        type: 'error',
        severity: 'high',
        title: 'Strava Authorization Failed',
        message: 'Strava token expired or invalid, user needs to re-authorize',
        commitId: context.commitId,
        userId: context.userId
      });
      
      // Don't retry auth errors
      return false;
    }

    // Network errors - retry with backoff
    if (error.message.includes('ECONNRESET') || error.message.includes('ETIMEDOUT')) {
      return context.attempt < 5;
    }

    // Default: retry up to 3 times
    return context.attempt < 3;
  }

  /**
   * Graceful degradation for commit system
   */
  async handleSystemDegradation(
    systemComponent: string,
    error: Error
  ): Promise<void> {
    console.warn(`⚠️ System degradation detected in ${systemComponent}: ${error.message}`);

    await commitMonitoringService.createAlert({
      type: 'warning',
      severity: 'high',
      title: 'System Degradation Detected',
      message: `${systemComponent} is experiencing issues: ${error.message}`,
      metadata: {
        component: systemComponent,
        degradationTime: new Date().toISOString()
      }
    });

    // Implement fallback strategies
    switch (systemComponent) {
      case 'scheduler':
        // Fall back to database-based scheduling
        break;
        
      case 'verification':
        // Fall back to manual verification only
        break;
        
      case 'notifications':
        // Queue notifications for later delivery
        break;
    }
  }

  private isRetryableError(error: Error, retryableErrors: string[]): boolean {
    return retryableErrors.some(retryableError => 
      error.message.toLowerCase().includes(retryableError.toLowerCase())
    );
  }

  private calculateDelay(attempt: number, config: RetryConfig): number {
    const delay = config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt - 1);
    return Math.min(delay, config.maxDelayMs);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isCircuitOpen(operationName: string): boolean {
    const breaker = this.circuitBreakers.get(operationName);
    return breaker?.state === 'open';
  }

  private recordSuccess(operationName: string): void {
    const breaker = this.circuitBreakers.get(operationName);
    if (breaker) {
      breaker.successCount++;
      if (breaker.state === 'half-open') {
        breaker.state = 'closed';
        breaker.failureCount = 0;
      }
    }
  }

  private recordFailure(operationName: string): void {
    const breaker = this.circuitBreakers.get(operationName);
    if (breaker) {
      breaker.failureCount++;
      breaker.lastFailureTime = Date.now();
    }
  }

  private async takeCorrectiveAction(
    error: Error,
    context: {
      operation: string;
      commitId: string;
      userId: string;
      jobName?: string;
    }
  ): Promise<void> {
    // Implement specific corrective actions based on error type
    if (error.message.includes('ALREADY_EXISTS') && context.jobName) {
      // Could implement job deletion and recreation here
    }
    
    if (error.message.includes('QUOTA_EXCEEDED')) {
      // Could implement job batching or rate limiting here
    }
  }
}

interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
  config: CircuitBreakerConfig;
}

// Default retry configurations for different operations
export const RETRY_CONFIGS = {
  SCHEDULER_OPERATION: {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 10000,
    backoffMultiplier: 2,
    retryableErrors: ['DEADLINE_EXCEEDED', 'UNAVAILABLE', 'INTERNAL', 'ECONNRESET', 'ETIMEDOUT']
  },
  
  GITHUB_VERIFICATION: {
    maxRetries: 5,
    baseDelayMs: 2000,
    maxDelayMs: 30000,
    backoffMultiplier: 2,
    retryableErrors: ['rate limit', 'ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND']
  },
  
  STRAVA_VERIFICATION: {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 15000,
    backoffMultiplier: 2,
    retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND']
  },
  
  NOTIFICATION_DELIVERY: {
    maxRetries: 5,
    baseDelayMs: 500,
    maxDelayMs: 5000,
    backoffMultiplier: 1.5,
    retryableErrors: ['UNAVAILABLE', 'DEADLINE_EXCEEDED', 'ECONNRESET']
  }
};

// Circuit breaker configurations
export const CIRCUIT_BREAKER_CONFIGS = {
  SCHEDULER: {
    failureThreshold: 5,
    resetTimeoutMs: 60000, // 1 minute
    monitoringWindowMs: 300000 // 5 minutes
  },
  
  VERIFICATION: {
    failureThreshold: 10,
    resetTimeoutMs: 300000, // 5 minutes
    monitoringWindowMs: 600000 // 10 minutes
  },
  
  NOTIFICATION: {
    failureThreshold: 20,
    resetTimeoutMs: 120000, // 2 minutes
    monitoringWindowMs: 300000 // 5 minutes
  }
};

// Export singleton instance
export const commitErrorHandler = CommitErrorHandler.getInstance();
