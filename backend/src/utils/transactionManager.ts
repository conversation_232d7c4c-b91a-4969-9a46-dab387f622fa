/**
 * Transaction Manager for complex multi-step operations
 * Provides rollback capabilities and ensures data consistency
 */

import * as admin from 'firebase-admin';
import { AppError, ErrorType } from './errorHandler';

export interface TransactionStep {
  id: string;
  operation: () => Promise<any>;
  rollback?: () => Promise<void>;
  description: string;
}

export interface TransactionResult<T = any> {
  success: boolean;
  result?: T;
  error?: string;
  completedSteps: string[];
  failedStep?: string;
  rollbackResults?: { [stepId: string]: boolean };
}

export class TransactionManager {
  private db: admin.firestore.Firestore;
  private completedSteps: Map<string, any> = new Map();
  private rollbackFunctions: Map<string, () => Promise<void>> = new Map();

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Execute a series of operations with automatic rollback on failure
   */
  async executeTransaction<T = any>(
    steps: TransactionStep[],
    transactionId?: string
  ): Promise<TransactionResult<T>> {
    const txId = transactionId || `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const completedStepIds: string[] = [];
    let result: T | undefined;


    try {
      // Execute each step
      for (const step of steps) {
        
        try {
          const stepResult = await step.operation();
          this.completedSteps.set(step.id, stepResult);
          completedStepIds.push(step.id);
          
          // Store rollback function if provided
          if (step.rollback) {
            this.rollbackFunctions.set(step.id, step.rollback);
          }
          
          // If this is the last step, capture the result
          if (step === steps[steps.length - 1]) {
            result = stepResult;
          }
          
        } catch (stepError) {
          console.error(`Step ${step.id} failed:`, stepError);
          
          // Rollback completed steps
          const rollbackResults = await this.rollbackSteps(completedStepIds);
          
          return {
            success: false,
            error: `Transaction failed at step ${step.id}: ${stepError}`,
            completedSteps: completedStepIds,
            failedStep: step.id,
            rollbackResults
          };
        }
      }

      return {
        success: true,
        result,
        completedSteps: completedStepIds
      };

    } catch (error) {
      console.error(`Transaction ${txId} failed:`, error);
      
      // Rollback completed steps
      const rollbackResults = await this.rollbackSteps(completedStepIds);
      
      return {
        success: false,
        error: `Transaction failed: ${error}`,
        completedSteps: completedStepIds,
        rollbackResults
      };
    } finally {
      // Clean up
      this.completedSteps.clear();
      this.rollbackFunctions.clear();
    }
  }

  /**
   * Execute operations within a Firestore transaction
   */
  async executeFirestoreTransaction<T = any>(
    operation: (transaction: admin.firestore.Transaction) => Promise<T>
  ): Promise<T> {
    return await this.db.runTransaction(operation);
  }

  /**
   * Execute operations with retry logic
   */
  async executeWithRetry<T = any>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000
  ): Promise<T> {
    let lastError: Error | undefined;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`Operation failed on attempt ${attempt}/${maxRetries}:`, error);
        
        if (attempt < maxRetries) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
        }
      }
    }
    
    throw new AppError(
      `Operation failed after ${maxRetries} attempts: ${lastError?.message}`,
      ErrorType.INTERNAL_ERROR,
      lastError
    );
  }

  /**
   * Rollback completed steps in reverse order
   */
  private async rollbackSteps(stepIds: string[]): Promise<{ [stepId: string]: boolean }> {
    const rollbackResults: { [stepId: string]: boolean } = {};
    
    // Rollback in reverse order
    const reversedSteps = [...stepIds].reverse();
    
    for (const stepId of reversedSteps) {
      const rollbackFn = this.rollbackFunctions.get(stepId);
      
      if (rollbackFn) {
        try {
          await rollbackFn();
          rollbackResults[stepId] = true;
        } catch (rollbackError) {
          console.error(`Failed to rollback step ${stepId}:`, rollbackError);
          rollbackResults[stepId] = false;
        }
      } else {
        console.warn(`No rollback function available for step: ${stepId}`);
        rollbackResults[stepId] = false;
      }
    }
    
    return rollbackResults;
  }

  /**
   * Create a compensating transaction step
   */
  static createCompensatingStep(
    id: string,
    description: string,
    operation: () => Promise<any>,
    rollback: () => Promise<void>
  ): TransactionStep {
    return {
      id,
      description,
      operation,
      rollback
    };
  }

  /**
   * Create a simple transaction step without rollback
   */
  static createSimpleStep(
    id: string,
    description: string,
    operation: () => Promise<any>
  ): TransactionStep {
    return {
      id,
      description,
      operation
    };
  }

  /**
   * Create a Firestore document creation step with rollback
   */
  static createDocumentCreationStep(
    id: string,
    collection: string,
    docId: string,
    data: any,
    db: admin.firestore.Firestore
  ): TransactionStep {
    return {
      id,
      description: `Create document ${docId} in ${collection}`,
      operation: async () => {
        await db.collection(collection).doc(docId).set(data);
        return { collection, docId, data };
      },
      rollback: async () => {
        await db.collection(collection).doc(docId).delete();
      }
    };
  }

  /**
   * Create a Firestore document update step with rollback
   */
  static createDocumentUpdateStep(
    id: string,
    collection: string,
    docId: string,
    updateData: any,
    db: admin.firestore.Firestore
  ): TransactionStep {
    let originalData: any;
    
    return {
      id,
      description: `Update document ${docId} in ${collection}`,
      operation: async () => {
        // Get original data for rollback
        const doc = await db.collection(collection).doc(docId).get();
        originalData = doc.data();
        
        // Perform update
        await db.collection(collection).doc(docId).update(updateData);
        return { collection, docId, updateData };
      },
      rollback: async () => {
        if (originalData) {
          await db.collection(collection).doc(docId).set(originalData);
        }
      }
    };
  }
}
