/**
 * Commit Scheduling Monitoring and Logging Utilities
 * Provides comprehensive monitoring, metrics, and alerting for the commit scheduling system
 */

import * as admin from 'firebase-admin';

export interface CommitMetrics {
  totalActiveCommits: number;
  totalScheduledJobs: number;
  verificationSuccessRate: number;
  notificationDeliveryRate: number;
  averageResponseTime: number;
  errorRate: number;
  deadlineViolations: number;
  autoVerificationCount: number;
  manualVerificationCount: number;
}

export interface CommitAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  commitId?: string;
  userId?: string;
  functionName?: string;
  metadata?: any;
}

export interface PerformanceMetric {
  functionName: string;
  executionTime: number;
  timestamp: string;
  success: boolean;
  error?: string;
  commitId?: string;
  userId?: string;
}

export class CommitMonitoringService {
  private db: admin.firestore.Firestore;
  private metricsCollection = 'commit_metrics';
  private alertsCollection = 'commit_alerts';
  private performanceCollection = 'commit_performance';

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Log performance metrics for commit functions
   */
  async logPerformanceMetric(metric: PerformanceMetric): Promise<void> {
    try {
      await this.db.collection(this.performanceCollection).add({
        ...metric,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });

      // Log to console for immediate visibility
      const status = metric.success ? '✅' : '❌';
      console.log(`${status} Performance metric logged: ${metric.functionName} - ${metric.executionTime}ms`);

      // Check for performance alerts
      if (metric.executionTime > 30000) { // 30 seconds
        await this.createAlert({
          type: 'warning',
          severity: 'medium',
          title: 'Slow Function Execution',
          message: `${metric.functionName} took ${metric.executionTime}ms to execute`,
          functionName: metric.functionName,
          commitId: metric.commitId,
          userId: metric.userId,
          metadata: { executionTime: metric.executionTime }
        });
      }

      if (!metric.success && metric.error) {
        await this.createAlert({
          type: 'error',
          severity: 'high',
          title: 'Function Execution Failed',
          message: `${metric.functionName} failed: ${metric.error}`,
          functionName: metric.functionName,
          commitId: metric.commitId,
          userId: metric.userId,
          metadata: { error: metric.error }
        });
      }

    } catch (error) {
      console.error('Failed to log performance metric:', error);
    }
  }

  /**
   * Create and store alert
   */
  async createAlert(alert: Omit<CommitAlert, 'id' | 'timestamp'>): Promise<void> {
    try {
      const alertDoc = {
        ...alert,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        id: this.db.collection(this.alertsCollection).doc().id
      };

      await this.db.collection(this.alertsCollection).add(alertDoc);

      // Log critical alerts to console immediately
      if (alert.severity === 'critical') {
        console.error(`🚨 CRITICAL ALERT: ${alert.title} - ${alert.message}`);
      } else if (alert.severity === 'high') {
        console.warn(`⚠️ HIGH ALERT: ${alert.title} - ${alert.message}`);
      }

    } catch (error) {
      console.error('Failed to create alert:', error);
    }
  }

  /**
   * Log commit verification attempt
   */
  async logVerificationAttempt(
    commitId: string,
    userId: string,
    verificationType: string,
    success: boolean,
    verified: boolean,
    executionTime: number,
    error?: string
  ): Promise<void> {
    try {
      const logData = {
        commitId,
        userId,
        verificationType,
        success,
        verified,
        executionTime,
        error,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      };

      await this.db.collection('commit_verification_logs').add(logData);

      // Create alerts for verification issues
      if (!success) {
        await this.createAlert({
          type: 'error',
          severity: 'medium',
          title: 'Verification Failed',
          message: `${verificationType} verification failed for commit ${commitId}: ${error}`,
          commitId,
          userId,
          metadata: { verificationType, error }
        });
      } else if (success && !verified) {
        await this.createAlert({
          type: 'info',
          severity: 'low',
          title: 'Verification Not Met',
          message: `${verificationType} verification completed but requirements not met for commit ${commitId}`,
          commitId,
          userId,
          metadata: { verificationType }
        });
      }

    } catch (error) {
      console.error('Failed to log verification attempt:', error);
    }
  }

  /**
   * Log notification delivery attempt
   */
  async logNotificationDelivery(
    commitId: string,
    userId: string,
    notificationType: string,
    success: boolean,
    error?: string
  ): Promise<void> {
    try {
      const logData = {
        commitId,
        userId,
        notificationType,
        success,
        error,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      };

      await this.db.collection('commit_notification_logs').add(logData);

      if (!success) {
        await this.createAlert({
          type: 'warning',
          severity: 'low',
          title: 'Notification Delivery Failed',
          message: `Failed to deliver ${notificationType} notification for commit ${commitId}: ${error}`,
          commitId,
          userId,
          metadata: { notificationType, error }
        });
      }

    } catch (error) {
      console.error('Failed to log notification delivery:', error);
    }
  }

  /**
   * Log scheduler job creation/deletion
   */
  async logSchedulerJobOperation(
    operation: 'create' | 'delete' | 'update',
    jobName: string,
    commitId: string,
    userId: string,
    success: boolean,
    error?: string
  ): Promise<void> {
    try {
      const logData = {
        operation,
        jobName,
        commitId,
        userId,
        success,
        error,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      };

      await this.db.collection('commit_scheduler_logs').add(logData);

      if (!success) {
        await this.createAlert({
          type: 'error',
          severity: 'medium',
          title: 'Scheduler Job Operation Failed',
          message: `Failed to ${operation} scheduler job ${jobName} for commit ${commitId}: ${error}`,
          commitId,
          userId,
          metadata: { operation, jobName, error }
        });
      }

    } catch (error) {
      console.error('Failed to log scheduler job operation:', error);
    }
  }

  /**
   * Calculate and store daily metrics
   */
  async calculateDailyMetrics(): Promise<CommitMetrics> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get active commits count
      const activeCommitsSnapshot = await this.db
        .collection('commits')
        .where('status', '==', 'active')
        .get();
      const totalActiveCommits = activeCommitsSnapshot.size;

      // Get verification logs for today
      const verificationLogsSnapshot = await this.db
        .collection('commit_verification_logs')
        .where('timestamp', '>=', today)
        .where('timestamp', '<', tomorrow)
        .get();

      const verificationLogs = verificationLogsSnapshot.docs.map(doc => doc.data());
      const totalVerifications = verificationLogs.length;
      const successfulVerifications = verificationLogs.filter(log => log.success).length;
      const verificationSuccessRate = totalVerifications > 0 ? successfulVerifications / totalVerifications : 0;

      // Get notification logs for today
      const notificationLogsSnapshot = await this.db
        .collection('commit_notification_logs')
        .where('timestamp', '>=', today)
        .where('timestamp', '<', tomorrow)
        .get();

      const notificationLogs = notificationLogsSnapshot.docs.map(doc => doc.data());
      const totalNotifications = notificationLogs.length;
      const successfulNotifications = notificationLogs.filter(log => log.success).length;
      const notificationDeliveryRate = totalNotifications > 0 ? successfulNotifications / totalNotifications : 0;

      // Get performance metrics for today
      const performanceLogsSnapshot = await this.db
        .collection(this.performanceCollection)
        .where('timestamp', '>=', today)
        .where('timestamp', '<', tomorrow)
        .get();

      const performanceLogs = performanceLogsSnapshot.docs.map(doc => doc.data());
      const totalExecutions = performanceLogs.length;
      const averageResponseTime = totalExecutions > 0 
        ? performanceLogs.reduce((sum, log) => sum + log.executionTime, 0) / totalExecutions 
        : 0;
      const errorRate = totalExecutions > 0 
        ? performanceLogs.filter(log => !log.success).length / totalExecutions 
        : 0;

      // Count deadline violations
      const deadlineViolations = verificationLogs.filter(log => 
        log.success && !log.verified && log.verificationType === 'deadline_check'
      ).length;

      // Count auto vs manual verifications
      const autoVerificationCount = verificationLogs.filter(log => 
        log.verificationType === 'github' || log.verificationType === 'strava'
      ).length;
      const manualVerificationCount = totalVerifications - autoVerificationCount;

      const metrics: CommitMetrics = {
        totalActiveCommits,
        totalScheduledJobs: 0, // Would need to query Cloud Scheduler API
        verificationSuccessRate,
        notificationDeliveryRate,
        averageResponseTime,
        errorRate,
        deadlineViolations,
        autoVerificationCount,
        manualVerificationCount
      };

      // Store metrics
      await this.db.collection(this.metricsCollection).add({
        ...metrics,
        date: today.toISOString().split('T')[0],
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      });

      return metrics;

    } catch (error) {
      console.error('Failed to calculate daily metrics:', error);
      throw error;
    }
  }

  /**
   * Get recent alerts
   */
  async getRecentAlerts(limit: number = 50): Promise<CommitAlert[]> {
    try {
      const snapshot = await this.db
        .collection(this.alertsCollection)
        .orderBy('timestamp', 'desc')
        .limit(limit)
        .get();

      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as CommitAlert));

    } catch (error) {
      console.error('Failed to get recent alerts:', error);
      return [];
    }
  }

  /**
   * Get performance metrics for a specific function
   */
  async getFunctionPerformanceMetrics(
    functionName: string,
    hours: number = 24
  ): Promise<PerformanceMetric[]> {
    try {
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);

      const snapshot = await this.db
        .collection(this.performanceCollection)
        .where('functionName', '==', functionName)
        .where('timestamp', '>=', since)
        .orderBy('timestamp', 'desc')
        .get();

      return snapshot.docs.map(doc => doc.data() as PerformanceMetric);

    } catch (error) {
      console.error('Failed to get function performance metrics:', error);
      return [];
    }
  }

  /**
   * Health check for commit scheduling system
   */
  async performHealthCheck(): Promise<{
    healthy: boolean;
    issues: string[];
    metrics: Partial<CommitMetrics>;
  }> {
    const issues: string[] = [];
    let healthy = true;

    try {
      // Check recent error rate
      const recentMetrics = await this.getFunctionPerformanceMetrics('commitDailyChecker', 1);
      const errorRate = recentMetrics.length > 0 
        ? recentMetrics.filter(m => !m.success).length / recentMetrics.length 
        : 0;

      if (errorRate > 0.1) { // More than 10% error rate
        issues.push(`High error rate: ${(errorRate * 100).toFixed(1)}%`);
        healthy = false;
      }

      // Check for stuck jobs (no activity in last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentActivity = await this.db
        .collection(this.performanceCollection)
        .where('timestamp', '>=', oneHourAgo)
        .limit(1)
        .get();

      if (recentActivity.empty) {
        issues.push('No recent scheduler activity detected');
        healthy = false;
      }

      // Check for critical alerts in last hour
      const criticalAlerts = await this.db
        .collection(this.alertsCollection)
        .where('severity', '==', 'critical')
        .where('timestamp', '>=', oneHourAgo)
        .get();

      if (!criticalAlerts.empty) {
        issues.push(`${criticalAlerts.size} critical alerts in last hour`);
        healthy = false;
      }

      return {
        healthy,
        issues,
        metrics: {
          errorRate,
          averageResponseTime: recentMetrics.length > 0 
            ? recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / recentMetrics.length 
            : 0
        }
      };

    } catch (error) {
      console.error('Health check failed:', error);
      return {
        healthy: false,
        issues: [`Health check failed: ${error}`],
        metrics: {}
      };
    }
  }
}

// Export singleton instance
export const commitMonitoringService = new CommitMonitoringService();

/**
 * Performance monitoring decorator for commit functions
 */
export function monitorPerformance(functionName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      let success = true;
      let error: string | undefined;

      try {
        const result = await method.apply(this, args);
        return result;
      } catch (err) {
        success = false;
        error = err instanceof Error ? err.message : String(err);
        throw err;
      } finally {
        const executionTime = Date.now() - startTime;
        
        // Extract commitId and userId from args if available
        const commitId = args.find(arg => typeof arg === 'string' && arg.includes('commit')) || 
                        args.find(arg => arg?.commitId);
        const userId = args.find(arg => arg?.userId);

        await commitMonitoringService.logPerformanceMetric({
          functionName,
          executionTime,
          timestamp: new Date().toISOString(),
          success,
          error,
          commitId: typeof commitId === 'string' ? commitId : commitId?.commitId,
          userId: typeof userId === 'string' ? userId : userId?.userId
        });
      }
    };
  };
}

/**
 * Utility functions for logging
 */
export const logCommitEvent = async (
  event: string,
  commitId: string,
  userId: string,
  metadata?: any
) => {
  
  // Store in Firestore for analysis
  try {
    await admin.firestore().collection('commit_events').add({
      event,
      commitId,
      userId,
      metadata,
      timestamp: admin.firestore.FieldValue.serverTimestamp()
    });
  } catch (error) {
    console.error('Failed to log commit event:', error);
  }
};

export const logSchedulerEvent = async (
  operation: string,
  jobName: string,
  success: boolean,
  error?: string
) => {
  const status = success ? '✅' : '❌';
  console.log(`${status} Scheduler event: ${operation} - ${jobName}`);

  await commitMonitoringService.logSchedulerJobOperation(
    operation as any,
    jobName,
    jobName.split('-')[2] || 'unknown', // Extract commit ID from job name
    jobName.split('-')[3] || 'unknown', // Extract user ID from job name
    success,
    error
  );
};
