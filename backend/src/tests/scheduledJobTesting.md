# Testing Scheduled Jobs & Cloud Functions

## 1. Unit Testing (Core Logic)

```typescript
// Test the job execution logic without scheduling
describe('Scheduled Job Logic', () => {
  test('should process notifications correctly', async () => {
    const notificationService = new NotificationService();
    
    // Mock the scheduler trigger data
    const mockJobData = {
      programId: 'test-program',
      participantId: 'test-user',
      timezone: 'America/New_York'
    };
    
    // Test the actual job execution logic
    const result = await notificationService.handleRecurringNotification(mockJobData);
    expect(result.success).toBe(true);
  });
});
```

## 2. Integration Testing (With Firebase Emulator)

### Setup Firebase Emulator Suite
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Initialize emulators
firebase init emulators

# Start emulator suite
firebase emulators:start
```

### Test with Emulator
```typescript
// Test with Firebase emulator
describe('Cloud Function Integration', () => {
  beforeAll(async () => {
    // Connect to emulator
    process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
    process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';
  });

  test('should trigger scheduled function', async () => {
    // Trigger the cloud function directly
    const functions = require('firebase-functions-test')();
    const myFunction = require('../functions/scheduledJobs');
    
    const data = { programId: 'test' };
    const context = { timestamp: new Date().toISOString() };
    
    const result = await myFunction.dailyNotificationScheduler(data, context);
    expect(result).toBeDefined();
  });
});
```

## 3. Manual Testing (Local Development)

### Test Individual Job Functions
```typescript
// Create test scripts to manually trigger jobs
// backend/scripts/testJobs.ts

import { NotificationService } from '../src/services/notificationService';
import { ProgramService } from '../src/services/programService';

async function testDailyNotifications() {
  const service = new NotificationService();
  
  // Simulate job trigger with test data
  const result = await service.handleRecurringNotification({
    programId: 'your-test-program-id',
    participantId: 'your-test-user-id',
    timezone: 'America/New_York'
  });
  
  console.log('Notification job result:', result);
}

async function testProgramCheckup() {
  const service = new ProgramService();
  
  const result = await service.performIndividualDailyCheckup({
    programId: 'your-test-program-id',
    participantId: 'your-test-user-id',
    personalStartDate: '2024-01-01',
    personalEndDate: '2024-01-31',
    timezone: 'America/New_York'
  });
  
  console.log('Checkup job result:', result);
}

// Run tests
testDailyNotifications();
testProgramCheckup();
```

### Run Test Scripts
```bash
# Run individual job tests
npx tsx backend/scripts/testJobs.ts
```

## 4. Staging Environment Testing

### Deploy to Staging
```bash
# Deploy functions to staging
firebase deploy --only functions --project staging

# Deploy with specific functions
firebase deploy --only functions:dailyNotificationScheduler --project staging
```

### Test Scheduled Jobs in Staging
```typescript
// Create staging test utilities
// backend/scripts/stagingTests.ts

import admin from 'firebase-admin';

// Initialize with staging credentials
admin.initializeApp({
  credential: admin.credential.cert(stagingServiceAccount),
  databaseURL: 'https://your-staging-project.firebaseio.com'
});

async function createTestProgram() {
  // Create a test program that starts "now" for immediate testing
  const programData = {
    name: 'Test Program',
    startDate: new Date().toISOString().split('T')[0], // Today
    duration: 7, // 7 days
    participants: [
      {
        id: 'test-user-1',
        timezone: 'America/New_York',
        personalStartDate: new Date().toISOString().split('T')[0]
      }
    ]
  };
  
  const programRef = await admin.firestore().collection('programs').add(programData);
  console.log('Created test program:', programRef.id);
  return programRef.id;
}

async function scheduleTestJobs(programId: string) {
  // Manually trigger job scheduling
  const schedulerService = new SchedulerService();
  await schedulerService.scheduleAllProgramJobs(programId);
  console.log('Scheduled jobs for program:', programId);
}
```

## 5. Production Testing (Monitoring)

### Cloud Function Logs
```bash
# View function logs
firebase functions:log --project production

# View specific function logs
firebase functions:log --only dailyNotificationScheduler --project production

# Real-time log streaming
firebase functions:log --project production --follow
```

### Monitoring Dashboard
```typescript
// Add monitoring to your functions
// functions/src/monitoring.ts

export const logJobExecution = (jobName: string, data: any, result: any) => {
  console.log(`[${jobName}] Execution:`, {
    timestamp: new Date().toISOString(),
    input: data,
    output: result,
    success: result.success,
    duration: result.duration
  });
  
  // Send to monitoring service (e.g., Google Cloud Monitoring)
  if (!result.success) {
    console.error(`[${jobName}] Failed:`, result.error);
  }
};
```

## 6. End-to-End Testing

### Automated E2E Tests
```typescript
// Test complete user journey
describe('E2E Scheduled Jobs', () => {
  test('should send notifications at correct times', async () => {
    // 1. Create test program
    const programId = await createTestProgram();
    
    // 2. Add test participant
    const participantId = await addTestParticipant(programId);
    
    // 3. Wait for scheduled job to run (or trigger manually)
    await waitForJobExecution(5000); // 5 seconds
    
    // 4. Verify notification was sent
    const notifications = await getNotificationHistory(participantId);
    expect(notifications.length).toBeGreaterThan(0);
    
    // 5. Verify timing was correct
    const lastNotification = notifications[0];
    const expectedTime = calculateExpectedNotificationTime(participant.timezone);
    expect(lastNotification.timestamp).toBeCloseTo(expectedTime, 60000); // Within 1 minute
  });
});
```

## 7. Testing Different Scenarios

### Timezone Testing
```typescript
// Test across multiple timezones
const testTimezones = [
  'America/New_York',    // UTC-5/-4
  'Europe/London',       // UTC+0/+1  
  'Asia/Tokyo',          // UTC+9
  'Australia/Sydney'     // UTC+10/+11
];

testTimezones.forEach(timezone => {
  test(`should handle ${timezone} correctly`, async () => {
    const participant = createTestParticipant({ timezone });
    const result = await processScheduledJob(participant);
    
    // Verify job ran at correct local time
    const localTime = new Date().toLocaleString('en-US', { timeZone: timezone });
    expect(result.executionTime).toMatchTimezone(timezone);
  });
});
```

### DST Transition Testing
```typescript
// Test around DST transitions
test('should handle DST transitions', async () => {
  // Spring forward (March 10, 2024)
  const springTransition = new Date('2024-03-10T07:00:00Z');
  
  // Fall back (November 3, 2024)  
  const fallTransition = new Date('2024-11-03T06:00:00Z');
  
  [springTransition, fallTransition].forEach(async (testDate) => {
    // Mock system time
    jest.useFakeTimers().setSystemTime(testDate);
    
    const result = await processTimezoneJob('America/New_York');
    expect(result.success).toBe(true);
    
    jest.useRealTimers();
  });
});
```

## 8. Performance Testing

### Load Testing
```typescript
// Test with many concurrent jobs
test('should handle multiple concurrent jobs', async () => {
  const promises = [];
  
  // Create 100 concurrent job executions
  for (let i = 0; i < 100; i++) {
    promises.push(processScheduledJob({
      programId: `test-program-${i}`,
      participantId: `test-user-${i}`,
      timezone: 'America/New_York'
    }));
  }
  
  const results = await Promise.all(promises);
  
  // All should succeed
  expect(results.every(r => r.success)).toBe(true);
  
  // Should complete within reasonable time
  const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  expect(avgDuration).toBeLessThan(5000); // 5 seconds average
});
```

## 9. Debugging Failed Jobs

### Debug Utilities
```typescript
// Create debugging utilities
// backend/scripts/debugJobs.ts

async function debugFailedJob(jobId: string) {
  // Get job details from scheduler
  const jobDetails = await getScheduledJobDetails(jobId);
  console.log('Job details:', jobDetails);
  
  // Get related data
  const program = await getProgram(jobDetails.programId);
  const participant = await getParticipant(jobDetails.participantId);
  
  console.log('Program:', program);
  console.log('Participant:', participant);
  
  // Simulate job execution with debug logging
  const result = await executeJobWithDebug(jobDetails);
  console.log('Debug result:', result);
}

async function replayFailedJob(jobId: string) {
  // Get original job data
  const originalData = await getFailedJobData(jobId);
  
  // Re-execute with current system state
  const result = await processScheduledJob(originalData);
  console.log('Replay result:', result);
}
```

## 10. Best Practices

### Job Testing Checklist
- [ ] Unit test core job logic
- [ ] Test with Firebase emulator
- [ ] Test timezone handling
- [ ] Test DST transitions  
- [ ] Test error scenarios
- [ ] Test performance with load
- [ ] Monitor in staging
- [ ] Verify logs in production
- [ ] Test rollback procedures
- [ ] Document debugging steps

### Monitoring Setup
```typescript
// Set up comprehensive monitoring
export const monitorScheduledJobs = {
  // Track job success rates
  trackSuccess: (jobName: string, success: boolean) => {
    // Send to monitoring service
  },
  
  // Track execution times
  trackDuration: (jobName: string, duration: number) => {
    // Send to monitoring service
  },
  
  // Alert on failures
  alertOnFailure: (jobName: string, error: Error) => {
    // Send alert to team
  }
};
```
