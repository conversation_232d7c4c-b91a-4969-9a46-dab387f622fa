/**
 * Comprehensive timezone utility tests
 * Tests timezone handling across different scenarios including DST transitions,
 * multiple timezones, and edge cases
 */

import {
  isValidTimezone,
  getSafeTimezone,
  getTimezoneDisplayName,
  getCurrentDateInTimezone,
  convertDateToTimezone,
  findEarliestTimezone,
  findLatestTimezone,
  getMidnightInTimezone,
  getTimezoneOffsetForDate,
  isDSTTransitionPeriod,
  getSafeMidnightInTimezone,
  extractParticipantTimezones
} from '../utils/timezoneUtils';

describe('Timezone Utilities', () => {
  
  describe('isValidTimezone', () => {
    test('should validate common timezones', () => {
      expect(isValidTimezone('America/New_York')).toBe(true);
      expect(isValidTimezone('Europe/London')).toBe(true);
      expect(isValidTimezone('Asia/Tokyo')).toBe(true);
      expect(isValidTimezone('UTC')).toBe(true);
      expect(isValidTimezone('America/Los_Angeles')).toBe(true);
    });

    test('should reject invalid timezones', () => {
      expect(isValidTimezone('Invalid/Timezone')).toBe(false);
      expect(isValidTimezone('EST')).toBe(false); // Deprecated format
      expect(isValidTimezone('')).toBe(false);
      expect(isValidTimezone(null as any)).toBe(false);
      expect(isValidTimezone(undefined as any)).toBe(false);
    });

    test('should handle edge cases', () => {
      expect(isValidTimezone('America/Argentina/Buenos_Aires')).toBe(true);
      expect(isValidTimezone('Pacific/Chatham')).toBe(true); // UTC+12:45
      expect(isValidTimezone('Asia/Kathmandu')).toBe(true); // UTC+5:45
    });
  });

  describe('getSafeTimezone', () => {
    test('should return valid timezone as-is', () => {
      expect(getSafeTimezone('America/New_York')).toBe('America/New_York');
      expect(getSafeTimezone('UTC')).toBe('UTC');
    });

    test('should fallback to UTC for invalid timezones', () => {
      expect(getSafeTimezone('Invalid/Timezone')).toBe('UTC');
      expect(getSafeTimezone('')).toBe('UTC');
    });

    test('should use custom fallback', () => {
      expect(getSafeTimezone('Invalid/Timezone', 'America/New_York')).toBe('America/New_York');
    });
  });

  describe('getTimezoneDisplayName', () => {
    test('should return display names for valid timezones', () => {
      const nyName = getTimezoneDisplayName('America/New_York');
      expect(nyName).toContain('Eastern');
      
      const utcName = getTimezoneDisplayName('UTC');
      expect(utcName).toContain('UTC');
    });

    test('should handle invalid timezones gracefully', () => {
      const invalidName = getTimezoneDisplayName('Invalid/Timezone');
      expect(invalidName).toBe('Invalid/Timezone'); // Should return input as fallback
    });
  });

  describe('getCurrentDateInTimezone', () => {
    test('should return current date in specified timezone', () => {
      const nyDate = getCurrentDateInTimezone('America/New_York');
      const utcDate = getCurrentDateInTimezone('UTC');
      
      expect(nyDate).toBeInstanceOf(Date);
      expect(utcDate).toBeInstanceOf(Date);
      
      // Should be date-only (time should be 00:00:00)
      expect(nyDate.getHours()).toBe(0);
      expect(nyDate.getMinutes()).toBe(0);
      expect(nyDate.getSeconds()).toBe(0);
    });

    test('should handle invalid timezones', () => {
      const result = getCurrentDateInTimezone('Invalid/Timezone');
      expect(result).toBeInstanceOf(Date);
      // Should fallback to UTC
    });
  });

  describe('convertDateToTimezone', () => {
    test('should convert date to specified timezone', () => {
      const testDate = new Date('2024-01-15T12:00:00Z');
      
      const nyDate = convertDateToTimezone(testDate, 'America/New_York');
      const utcDate = convertDateToTimezone(testDate, 'UTC');
      
      expect(nyDate).toBeInstanceOf(Date);
      expect(utcDate).toBeInstanceOf(Date);
      
      // Should be date-only
      expect(nyDate.getHours()).toBe(0);
      expect(utcDate.getHours()).toBe(0);
    });

    test('should handle invalid timezones', () => {
      const testDate = new Date('2024-01-15T12:00:00Z');
      const result = convertDateToTimezone(testDate, 'Invalid/Timezone');
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe('findEarliestTimezone', () => {
    test('should find earliest timezone', () => {
      const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo'];
      const referenceDate = new Date('2024-01-15T00:00:00Z');
      
      const earliest = findEarliestTimezone(timezones, referenceDate);
      expect(earliest).toBe('Asia/Tokyo'); // UTC+9, experiences midnight first
    });

    test('should handle single timezone', () => {
      const result = findEarliestTimezone(['America/New_York'], new Date());
      expect(result).toBe('America/New_York');
    });

    test('should handle empty array', () => {
      const result = findEarliestTimezone([], new Date());
      expect(result).toBe('UTC');
    });
  });

  describe('findLatestTimezone', () => {
    test('should find latest timezone', () => {
      const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo'];
      const referenceDate = new Date('2024-01-15T00:00:00Z');
      
      const latest = findLatestTimezone(timezones, referenceDate);
      expect(latest).toBe('America/New_York'); // UTC-5, experiences midnight last
    });

    test('should handle single timezone', () => {
      const result = findLatestTimezone(['Europe/London'], new Date());
      expect(result).toBe('Europe/London');
    });

    test('should handle empty array', () => {
      const result = findLatestTimezone([], new Date());
      expect(result).toBe('UTC');
    });
  });

  describe('getMidnightInTimezone', () => {
    test('should get midnight in specified timezone', () => {
      const midnight = getMidnightInTimezone('2024-01-15', 'America/New_York');
      
      expect(midnight).toBeInstanceOf(Date);
      // Should represent midnight in NY time converted to UTC
    });

    test('should handle UTC timezone', () => {
      const midnight = getMidnightInTimezone('2024-01-15', 'UTC');
      expect(midnight.getUTCHours()).toBe(0);
      expect(midnight.getUTCMinutes()).toBe(0);
    });
  });

  describe('getTimezoneOffsetForDate', () => {
    test('should get timezone offset for specific date', () => {
      const summerDate = new Date('2024-07-15T12:00:00Z');
      const winterDate = new Date('2024-01-15T12:00:00Z');
      
      const nySummerOffset = getTimezoneOffsetForDate('America/New_York', summerDate);
      const nyWinterOffset = getTimezoneOffsetForDate('America/New_York', winterDate);
      
      expect(typeof nySummerOffset).toBe('number');
      expect(typeof nyWinterOffset).toBe('number');
      
      // NY should have different offsets in summer vs winter due to DST
      expect(nySummerOffset).not.toBe(nyWinterOffset);
    });
  });

  describe('isDSTTransitionPeriod', () => {
    test('should detect DST transitions', () => {
      // Test around known DST transition dates
      const springTransition = new Date('2024-03-10T12:00:00Z'); // Spring forward in US
      const fallTransition = new Date('2024-11-03T12:00:00Z'); // Fall back in US
      
      const springResult = isDSTTransitionPeriod(springTransition, 'America/New_York');
      const fallResult = isDSTTransitionPeriod(fallTransition, 'America/New_York');
      
      expect(typeof springResult).toBe('boolean');
      expect(typeof fallResult).toBe('boolean');
    });

    test('should handle non-DST timezones', () => {
      const testDate = new Date('2024-03-10T12:00:00Z');
      const result = isDSTTransitionPeriod(testDate, 'UTC');
      expect(result).toBe(false); // UTC doesn't have DST
    });
  });

  describe('getSafeMidnightInTimezone', () => {
    test('should get safe midnight accounting for DST', () => {
      const result = getSafeMidnightInTimezone('2024-03-10', 'America/New_York');
      expect(result).toBeInstanceOf(Date);
    });

    test('should handle non-DST dates normally', () => {
      const result = getSafeMidnightInTimezone('2024-01-15', 'America/New_York');
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe('extractParticipantTimezones', () => {
    test('should extract valid timezones from participant objects', () => {
      const participants = [
        { timezone: 'America/New_York' },
        { timezone: 'Europe/London' },
        { timezone: 'Invalid/Timezone' }, // Should be filtered out
        { timezone: 'Asia/Tokyo' },
        { timezone: '' }, // Should be filtered out
        { timezone: null }, // Should be filtered out
      ];

      const result = extractParticipantTimezones(participants);
      expect(result).toEqual(['America/New_York', 'Europe/London', 'Asia/Tokyo']);
    });

    test('should handle Firestore document objects', () => {
      const participants = [
        { data: () => ({ timezone: 'America/New_York' }) },
        { data: () => ({ timezone: 'Europe/London' }) },
        { data: () => ({ timezone: null }) }, // Should be filtered out
      ];

      const result = extractParticipantTimezones(participants);
      expect(result).toEqual(['America/New_York', 'Europe/London']);
    });

    test('should remove duplicates', () => {
      const participants = [
        { timezone: 'America/New_York' },
        { timezone: 'America/New_York' }, // Duplicate
        { timezone: 'Europe/London' },
      ];

      const result = extractParticipantTimezones(participants);
      expect(result).toEqual(['America/New_York', 'Europe/London']);
    });

    test('should handle empty array', () => {
      const result = extractParticipantTimezones([]);
      expect(result).toEqual([]);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle malformed date strings', () => {
      expect(() => getMidnightInTimezone('invalid-date', 'UTC')).not.toThrow();
    });

    test('should handle extreme dates', () => {
      const farFuture = '2099-12-31';
      const farPast = '1900-01-01';
      
      expect(() => getMidnightInTimezone(farFuture, 'UTC')).not.toThrow();
      expect(() => getMidnightInTimezone(farPast, 'UTC')).not.toThrow();
    });

    test('should handle unusual timezone offsets', () => {
      // Test timezones with unusual offsets
      const chatham = getCurrentDateInTimezone('Pacific/Chatham'); // UTC+12:45
      const kathmandu = getCurrentDateInTimezone('Asia/Kathmandu'); // UTC+5:45
      
      expect(chatham).toBeInstanceOf(Date);
      expect(kathmandu).toBeInstanceOf(Date);
    });
  });
});
