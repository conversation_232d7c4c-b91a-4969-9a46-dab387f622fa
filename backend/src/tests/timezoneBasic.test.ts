/**
 * Basic timezone functionality tests
 * Tests core timezone functions to verify they work correctly
 */

import {
  isValidTimezone,
  getSafeTimezone,
  getCurrentDateInTimezone,
  convertDateToTimezone,
  extractParticipantTimezones
} from '../utils/timezoneUtils';

describe('Basic Timezone Functionality', () => {
  
  describe('isValidTimezone', () => {
    test('should validate common timezones', () => {
      expect(isValidTimezone('America/New_York')).toBe(true);
      expect(isValidTimezone('Europe/London')).toBe(true);
      expect(isValidTimezone('UTC')).toBe(true);
    });

    test('should reject invalid timezones', () => {
      expect(isValidTimezone('Invalid/Timezone')).toBe(false);
      expect(isValidTimezone('')).toBe(false);
      expect(isValidTimezone(null as any)).toBe(false);
    });
  });

  describe('getSafeTimezone', () => {
    test('should return valid timezone as-is', () => {
      expect(getSafeTimezone('America/New_York')).toBe('America/New_York');
      expect(getSafeTimezone('UTC')).toBe('UTC');
    });

    test('should fallback to UTC for invalid timezones', () => {
      expect(getSafeTimezone('Invalid/Timezone')).toBe('UTC');
      expect(getSafeTimezone('')).toBe('UTC');
    });
  });

  describe('getCurrentDateInTimezone', () => {
    test('should return a Date object', () => {
      const result = getCurrentDateInTimezone('America/New_York');
      expect(result).toBeInstanceOf(Date);
      expect(result.getHours()).toBe(0); // Should be date-only
      expect(result.getMinutes()).toBe(0);
      expect(result.getSeconds()).toBe(0);
    });

    test('should handle invalid timezones gracefully', () => {
      const result = getCurrentDateInTimezone('Invalid/Timezone');
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe('convertDateToTimezone', () => {
    test('should convert date to timezone', () => {
      const testDate = new Date('2024-01-15T12:00:00Z');
      const result = convertDateToTimezone(testDate, 'America/New_York');
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getHours()).toBe(0); // Should be date-only
      expect(result.getMinutes()).toBe(0);
      expect(result.getSeconds()).toBe(0);
    });

    test('should handle invalid timezones', () => {
      const testDate = new Date('2024-01-15T12:00:00Z');
      const result = convertDateToTimezone(testDate, 'Invalid/Timezone');
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe('extractParticipantTimezones', () => {
    test('should extract valid timezones', () => {
      const participants = [
        { timezone: 'America/New_York' },
        { timezone: 'Europe/London' },
        { timezone: 'Asia/Tokyo' },
      ];

      const result = extractParticipantTimezones(participants);
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(3);
      expect(result).toContain('America/New_York');
      expect(result).toContain('Europe/London');
      expect(result).toContain('Asia/Tokyo');
    });

    test('should filter out invalid timezones', () => {
      const participants = [
        { timezone: 'America/New_York' },
        { timezone: 'Invalid/Timezone' },
        { timezone: '' },
        { timezone: null },
      ];

      const result = extractParticipantTimezones(participants);
      expect(result).toEqual(['America/New_York']);
    });

    test('should remove duplicates', () => {
      const participants = [
        { timezone: 'America/New_York' },
        { timezone: 'America/New_York' }, // Duplicate
        { timezone: 'Europe/London' },
      ];

      const result = extractParticipantTimezones(participants);
      expect(result.length).toBe(2);
      expect(result).toContain('America/New_York');
      expect(result).toContain('Europe/London');
    });

    test('should handle empty array', () => {
      const result = extractParticipantTimezones([]);
      expect(result).toEqual([]);
    });
  });

  describe('Integration Tests', () => {
    test('should handle timezone operations consistently', () => {
      const timezone = 'America/New_York';
      
      // Test that all functions handle the same timezone consistently
      expect(isValidTimezone(timezone)).toBe(true);
      expect(getSafeTimezone(timezone)).toBe(timezone);
      
      const currentDate = getCurrentDateInTimezone(timezone);
      expect(currentDate).toBeInstanceOf(Date);
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const convertedDate = convertDateToTimezone(testDate, timezone);
      expect(convertedDate).toBeInstanceOf(Date);
    });

    test('should handle invalid timezone consistently across functions', () => {
      const invalidTimezone = 'Invalid/Timezone';
      
      expect(isValidTimezone(invalidTimezone)).toBe(false);
      expect(getSafeTimezone(invalidTimezone)).toBe('UTC');
      
      // These should not throw errors
      expect(() => getCurrentDateInTimezone(invalidTimezone)).not.toThrow();
      expect(() => convertDateToTimezone(new Date(), invalidTimezone)).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    test('should not throw errors with malformed inputs', () => {
      expect(() => isValidTimezone(undefined as any)).not.toThrow();
      expect(() => getSafeTimezone(undefined as any)).not.toThrow();
      expect(() => getCurrentDateInTimezone(undefined as any)).not.toThrow();
      expect(() => convertDateToTimezone(new Date(), undefined as any)).not.toThrow();
      expect(() => extractParticipantTimezones(undefined as any)).not.toThrow();
    });

    test('should handle edge cases gracefully', () => {
      // Test with various edge case inputs
      const edgeCases = ['', ' ', 'UTC+5', 'GMT', 'EST', '123', 'null', 'undefined'];
      
      edgeCases.forEach(timezone => {
        expect(() => {
          isValidTimezone(timezone);
          getSafeTimezone(timezone);
          getCurrentDateInTimezone(timezone);
          convertDateToTimezone(new Date(), timezone);
        }).not.toThrow();
      });
    });
  });
});
