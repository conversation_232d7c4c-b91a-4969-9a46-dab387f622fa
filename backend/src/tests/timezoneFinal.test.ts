/**
 * Final timezone compliance verification
 * Simple tests to verify the timezone fixes work
 */

import {
  isValidTimezone,
  getSafeTimezone,
  getCurrentDateInTimezone,
  convertDateToTimezone,
  extractParticipantTimezones
} from '../utils/timezoneUtils';

describe('Final Timezone Verification', () => {
  
  test('Core timezone functions work correctly', () => {
    // Test basic validation
    expect(isValidTimezone('America/New_York')).toBe(true);
    expect(isValidTimezone('Europe/London')).toBe(true);
    expect(isValidTimezone('UTC')).toBe(true);
    expect(isValidTimezone('Invalid/Timezone')).toBe(false);
    
    // Test safe fallback
    expect(getSafeTimezone('America/New_York')).toBe('America/New_York');
    expect(getSafeTimezone('Invalid/Timezone')).toBe('UTC');
    
    // Test date conversion
    const currentDate = getCurrentDateInTimezone('America/New_York');
    expect(currentDate).toBeInstanceOf(Date);
    expect(currentDate.getHours()).toBe(0); // Should be date-only
    
    const testDate = new Date('2024-01-15T12:00:00Z');
    const convertedDate = convertDateToTimezone(testDate, 'Europe/London');
    expect(convertedDate).toBeInstanceOf(Date);
    expect(convertedDate.getHours()).toBe(0); // Should be date-only
  });

  test('Participant timezone extraction works', () => {
    const participants = [
      { timezone: 'America/New_York' },
      { timezone: 'Europe/London' },
      { timezone: 'Asia/Tokyo' },
      { timezone: 'America/New_York' }, // Duplicate
      { timezone: 'Invalid/Timezone' }, // Invalid
      { timezone: '' }, // Empty
    ];

    const result = extractParticipantTimezones(participants);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(3); // Only valid, unique timezones
    expect(result).toContain('America/New_York');
    expect(result).toContain('Europe/London');
    expect(result).toContain('Asia/Tokyo');
  });

  test('Error handling works correctly', () => {
    // Should not throw errors with invalid inputs
    expect(() => isValidTimezone(null as any)).not.toThrow();
    expect(() => getSafeTimezone(undefined as any)).not.toThrow();
    expect(() => getCurrentDateInTimezone('Invalid/Timezone')).not.toThrow();
    expect(() => convertDateToTimezone(new Date(), 'Invalid/Timezone')).not.toThrow();
    expect(() => extractParticipantTimezones(null as any)).not.toThrow();
    
    // Should return valid fallback values
    expect(getSafeTimezone('Invalid/Timezone')).toBe('UTC');
    expect(extractParticipantTimezones(null as any)).toEqual([]);
    
    const invalidResult = getCurrentDateInTimezone('Invalid/Timezone');
    expect(invalidResult).toBeInstanceOf(Date);
  });

  test('Multiple timezone scenarios work', () => {
    const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'UTC'];
    const testDate = new Date('2024-01-15T12:00:00Z');
    
    timezones.forEach(timezone => {
      expect(isValidTimezone(timezone)).toBe(true);
      expect(getSafeTimezone(timezone)).toBe(timezone);
      
      const currentDate = getCurrentDateInTimezone(timezone);
      expect(currentDate).toBeInstanceOf(Date);
      expect(currentDate.getHours()).toBe(0);
      
      const convertedDate = convertDateToTimezone(testDate, timezone);
      expect(convertedDate).toBeInstanceOf(Date);
      expect(convertedDate.getHours()).toBe(0);
    });
  });

  test('Date boundaries are handled correctly', () => {
    // Test with UTC midnight
    const utcMidnight = new Date('2024-01-15T00:00:00Z');
    
    const nyDate = convertDateToTimezone(utcMidnight, 'America/New_York');
    const londonDate = convertDateToTimezone(utcMidnight, 'Europe/London');
    const tokyoDate = convertDateToTimezone(utcMidnight, 'Asia/Tokyo');
    
    // All should be valid dates
    expect(nyDate).toBeInstanceOf(Date);
    expect(londonDate).toBeInstanceOf(Date);
    expect(tokyoDate).toBeInstanceOf(Date);
    
    // All should be date-only (midnight)
    expect(nyDate.getHours()).toBe(0);
    expect(londonDate.getHours()).toBe(0);
    expect(tokyoDate.getHours()).toBe(0);
    
    // Dates might be different due to timezone offset, but should be within 1 day
    const dayDiff1 = Math.abs(nyDate.getTime() - londonDate.getTime()) / (1000 * 60 * 60 * 24);
    const dayDiff2 = Math.abs(londonDate.getTime() - tokyoDate.getTime()) / (1000 * 60 * 60 * 24);
    
    expect(dayDiff1).toBeLessThanOrEqual(1);
    expect(dayDiff2).toBeLessThanOrEqual(1);
  });

  test('Performance is acceptable', () => {
    const timezones = ['America/New_York', 'Europe/London', 'Asia/Tokyo', 'UTC'];
    const testDate = new Date('2024-01-15T12:00:00Z');
    const startTime = Date.now();
    
    // Perform many timezone operations
    for (let i = 0; i < 100; i++) {
      timezones.forEach(timezone => {
        isValidTimezone(timezone);
        getCurrentDateInTimezone(timezone);
        convertDateToTimezone(testDate, timezone);
      });
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (less than 1 second)
    expect(duration).toBeLessThan(1000);
  });

  test('Unusual timezone offsets work', () => {
    // Test timezones with unusual offsets
    const unusualTimezones = [
      'Pacific/Chatham', // UTC+12:45
      'Asia/Kathmandu',  // UTC+5:45
    ];
    
    const testDate = new Date('2024-01-15T12:00:00Z');
    
    unusualTimezones.forEach(timezone => {
      if (isValidTimezone(timezone)) {
        expect(() => {
          getCurrentDateInTimezone(timezone);
          convertDateToTimezone(testDate, timezone);
        }).not.toThrow();
        
        const currentDate = getCurrentDateInTimezone(timezone);
        const convertedDate = convertDateToTimezone(testDate, timezone);
        
        expect(currentDate).toBeInstanceOf(Date);
        expect(convertedDate).toBeInstanceOf(Date);
      }
    });
  });

  test('Extreme dates are handled', () => {
    const farFuture = new Date('2099-12-31T12:00:00Z');
    const farPast = new Date('1900-01-01T12:00:00Z');
    
    expect(() => {
      convertDateToTimezone(farFuture, 'America/New_York');
      convertDateToTimezone(farPast, 'America/New_York');
    }).not.toThrow();
    
    const futureResult = convertDateToTimezone(farFuture, 'America/New_York');
    const pastResult = convertDateToTimezone(farPast, 'America/New_York');
    
    expect(futureResult).toBeInstanceOf(Date);
    expect(pastResult).toBeInstanceOf(Date);
  });
});
