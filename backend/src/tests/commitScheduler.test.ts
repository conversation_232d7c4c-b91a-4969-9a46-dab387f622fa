/**
 * Comprehensive tests for Commit Scheduling System
 */

import { CommitSchedulerService, CommitSchedulerConfig } from '../services/commitSchedulerService';
import { CommitVerificationService } from '../services/commitVerificationService';
import { CommitNotificationService } from '../services/commitNotificationService';
import { CommitService } from '../services/commitService';

// Mock dependencies
jest.mock('../services/schedulerService');
jest.mock('../services/databaseService');
jest.mock('../fcm');

describe('CommitSchedulerService', () => {
  let commitSchedulerService: CommitSchedulerService;
  let mockConfig: CommitSchedulerConfig;

  beforeEach(() => {
    commitSchedulerService = new CommitSchedulerService();
    mockConfig = {
      commitId: 'test-commit-123',
      userId: 'test-user-456',
      timezone: 'America/New_York',
      startDate: new Date('2024-01-01T00:00:00Z'),
      endDate: new Date('2024-01-31T23:59:59Z'),
      frequency: 'daily',
      deadlineType: 'midnight',
      autoVerificationEnabled: false,
      verificationType: 'manual'
    };
  });

  describe('setupCommitScheduling', () => {
    it('should create all necessary scheduler jobs for immediate start', async () => {
      const pastStartDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
      const config = { ...mockConfig, startDate: pastStartDate };

      const result = await commitSchedulerService.setupCommitScheduling(config);

      expect(result.success).toBe(true);
      expect(result.jobsCreated).toBeGreaterThan(0);
      expect(result.errors).toHaveLength(0);
    });

    it('should create start job for future start date', async () => {
      const futureStartDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
      const config = { ...mockConfig, startDate: futureStartDate };

      const result = await commitSchedulerService.setupCommitScheduling(config);

      expect(result.success).toBe(true);
      expect(result.jobsCreated).toBeGreaterThan(0);
    });

    it('should handle auto-verification enabled commits', async () => {
      const config = {
        ...mockConfig,
        autoVerificationEnabled: true,
        verificationType: 'github'
      };

      const result = await commitSchedulerService.setupCommitScheduling(config);

      expect(result.success).toBe(true);
      expect(result.jobsCreated).toBeGreaterThan(2); // Should include auto-verification job
    });

    it('should handle errors gracefully', async () => {
      // Mock scheduler service to throw error
      jest.spyOn(commitSchedulerService as any, 'createCommitStartJob')
        .mockRejectedValue(new Error('Scheduler error'));

      const result = await commitSchedulerService.setupCommitScheduling(mockConfig);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('calculateCheckerSchedule', () => {
    it('should calculate correct schedule for before deadline', () => {
      const config = {
        ...mockConfig,
        deadlineType: 'before' as const,
        deadlineTime: '09:00'
      };

      const schedule = (commitSchedulerService as any).calculateCheckerSchedule(config);

      expect(schedule.cronExpression).toBe('0 10 * * *'); // 1 hour after 9 AM
      expect(schedule.description).toContain('after 09:00 deadline');
    });

    it('should calculate correct schedule for between deadline', () => {
      const config = {
        ...mockConfig,
        deadlineType: 'between' as const,
        deadlineEndTime: '18:00'
      };

      const schedule = (commitSchedulerService as any).calculateCheckerSchedule(config);

      expect(schedule.cronExpression).toBe('0 19 * * *'); // 1 hour after 6 PM
      expect(schedule.description).toContain('after 18:00 end time');
    });

    it('should use fallback for midnight deadline', () => {
      const config = {
        ...mockConfig,
        deadlineType: 'midnight' as const
      };

      const schedule = (commitSchedulerService as any).calculateCheckerSchedule(config);

      expect(schedule.cronExpression).toBe('0 1 * * *'); // 1 AM
      expect(schedule.description).toContain('after midnight deadline');
    });
  });

  describe('deleteCommitScheduling', () => {
    it('should delete all associated jobs', async () => {
      const result = await commitSchedulerService.deleteCommitScheduling(
        mockConfig.commitId,
        mockConfig.userId
      );

      expect(result.success).toBe(true);
      expect(result.jobsDeleted).toBeGreaterThan(0);
    });

    it('should handle non-existent jobs gracefully', async () => {
      // Mock scheduler service to return NOT_FOUND errors
      jest.spyOn(commitSchedulerService as any, 'schedulerService')
        .mockImplementation(() => ({
          deleteJob: jest.fn().mockRejectedValue({ code: 5 }) // NOT_FOUND
        }));

      const result = await commitSchedulerService.deleteCommitScheduling(
        'non-existent-commit',
        'non-existent-user'
      );

      expect(result.success).toBe(true); // Should still succeed
    });
  });

  describe('updateCommitScheduling', () => {
    it('should delete old jobs and create new ones', async () => {
      const newConfig = {
        ...mockConfig,
        deadlineType: 'before' as const,
        deadlineTime: '10:00'
      };

      const result = await commitSchedulerService.updateCommitScheduling(
        mockConfig.commitId,
        mockConfig.userId,
        newConfig
      );

      expect(result.success).toBe(true);
      expect(result.jobsUpdated).toBeGreaterThan(0);
    });
  });
});

describe('CommitVerificationService', () => {
  let verificationService: CommitVerificationService;

  beforeEach(() => {
    verificationService = new CommitVerificationService();
  });

  describe('verifyGitHubCommit', () => {
    it('should verify GitHub commits successfully', async () => {
      // Mock GitHub service to return commits
      jest.spyOn(verificationService as any, 'getGitHubService')
        .mockResolvedValue({
          getRecentCommits: jest.fn().mockResolvedValue([
            { sha: 'abc123', message: 'Test commit' },
            { sha: 'def456', message: 'Another commit' }
          ])
        });

      const result = await verificationService.verifyGitHubCommit('test-commit', {
        repositoryName: 'user/repo',
        minCommits: 1,
        verificationWindow: 24,
        userId: 'test-user'
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.details?.commitCount).toBe(2);
    });

    it('should fail verification with insufficient commits', async () => {
      jest.spyOn(verificationService as any, 'getGitHubService')
        .mockResolvedValue({
          getRecentCommits: jest.fn().mockResolvedValue([])
        });

      const result = await verificationService.verifyGitHubCommit('test-commit', {
        repositoryName: 'user/repo',
        minCommits: 1,
        verificationWindow: 24,
        userId: 'test-user'
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(false);
      expect(result.details?.commitCount).toBe(0);
    });

    it('should handle GitHub API errors', async () => {
      jest.spyOn(verificationService as any, 'getGitHubService')
        .mockRejectedValue(new Error('GitHub API error'));

      const result = await verificationService.verifyGitHubCommit('test-commit', {
        repositoryName: 'user/repo',
        minCommits: 1,
        verificationWindow: 24,
        userId: 'test-user'
      });

      expect(result.success).toBe(false);
      expect(result.verified).toBe(false);
      expect(result.error).toContain('GitHub API error');
    });
  });

  describe('verifyStravaActivity', () => {
    it('should verify Strava activities successfully', async () => {
      jest.spyOn(verificationService as any, 'getStravaService')
        .mockResolvedValue({
          getRecentActivities: jest.fn().mockResolvedValue([
            { id: 123, type: 'Run', moving_time: 1800 } // 30 minutes
          ])
        });

      const result = await verificationService.verifyStravaActivity('test-commit', {
        activityType: 'Run',
        minDuration: 20,
        verificationWindow: 24,
        userId: 'test-user'
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
    });

    it('should fail verification with insufficient duration', async () => {
      jest.spyOn(verificationService as any, 'getStravaService')
        .mockResolvedValue({
          getRecentActivities: jest.fn().mockResolvedValue([
            { id: 123, type: 'Run', moving_time: 600 } // 10 minutes
          ])
        });

      const result = await verificationService.verifyStravaActivity('test-commit', {
        activityType: 'Run',
        minDuration: 20,
        verificationWindow: 24,
        userId: 'test-user'
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(false);
    });
  });

  describe('verifyPhotoSubmission', () => {
    it('should verify photo submissions successfully', async () => {
      const result = await verificationService.verifyPhotoSubmission('test-commit', {
        allowMultiple: true,
        userId: 'test-user'
      }, {
        photos: ['photo1.jpg', 'photo2.jpg']
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(true);
      expect(result.details?.submissionData.photoCount).toBe(2);
    });

    it('should fail verification with no photos', async () => {
      const result = await verificationService.verifyPhotoSubmission('test-commit', {
        allowMultiple: false,
        userId: 'test-user'
      }, {
        photos: []
      });

      expect(result.success).toBe(true);
      expect(result.verified).toBe(false);
    });
  });

  describe('handleVerificationFailure', () => {
    it('should implement exponential backoff for retries', async () => {
      const mockUpdateDocument = jest.fn();
      jest.spyOn(verificationService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue({
            verification: { consecutiveFailures: 1 }
          }),
          updateDocument: mockUpdateDocument
        });

      await verificationService.handleVerificationFailure('test-commit', new Error('Test error'));

      expect(mockUpdateDocument).toHaveBeenCalledWith(
        'commits',
        'test-commit',
        expect.objectContaining({
          'verification.consecutiveFailures': 2,
          'verification.nextRetryTime': expect.any(String)
        })
      );
    });

    it('should mark commit as failed after max retries', async () => {
      const mockUpdateDocument = jest.fn();
      jest.spyOn(verificationService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue({
            verification: { consecutiveFailures: 2 } // Will become 3, which is max
          }),
          updateDocument: mockUpdateDocument
        });

      await verificationService.handleVerificationFailure('test-commit', new Error('Test error'));

      expect(mockUpdateDocument).toHaveBeenCalledWith(
        'commits',
        'test-commit',
        expect.objectContaining({
          status: 'failed'
        })
      );
    });
  });
});

describe('CommitNotificationService', () => {
  let notificationService: CommitNotificationService;

  beforeEach(() => {
    notificationService = new CommitNotificationService();
  });

  describe('sendDailyCommitNotifications', () => {
    it('should send notifications for active commits', async () => {
      const mockCommit = {
        id: 'test-commit',
        title: 'Test Commitment',
        status: 'active',
        userId: 'test-user',
        schedule: { frequency: 'daily' }
      };

      const mockUser = {
        id: 'test-user',
        timezone: 'America/New_York'
      };

      jest.spyOn(notificationService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn()
            .mockResolvedValueOnce(mockCommit)
            .mockResolvedValueOnce(mockUser)
        });

      jest.spyOn(notificationService as any, 'hasUserSubmittedToday')
        .mockResolvedValue(false);

      const result = await notificationService.sendDailyCommitNotifications(
        'test-commit',
        'test-user',
        { isRecurring: true }
      );

      expect(result.success).toBe(true);
      expect(result.notificationsSent).toBeGreaterThan(0);
    });

    it('should skip notifications for inactive commits', async () => {
      const mockCommit = {
        id: 'test-commit',
        status: 'completed',
        userId: 'test-user'
      };

      jest.spyOn(notificationService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue(mockCommit)
        });

      const result = await notificationService.sendDailyCommitNotifications(
        'test-commit',
        'test-user'
      );

      expect(result.success).toBe(true);
      expect(result.action).toBe('skipped_inactive');
      expect(result.notificationsSent).toBe(0);
    });
  });

  describe('calculateTimeUntilDeadline', () => {
    it('should calculate time until before deadline correctly', () => {
      const mockCommit = {
        schedule: {
          deadline: {
            type: 'before',
            time: '09:00'
          }
        }
      };

      const timeUntilDeadline = (notificationService as any).calculateTimeUntilDeadline(
        mockCommit,
        'UTC'
      );

      expect(timeUntilDeadline).toBeGreaterThan(0);
    });

    it('should handle midnight deadline', () => {
      const mockCommit = {
        schedule: {
          deadline: {
            type: 'midnight'
          }
        }
      };

      const timeUntilDeadline = (notificationService as any).calculateTimeUntilDeadline(
        mockCommit,
        'UTC'
      );

      expect(timeUntilDeadline).toBeGreaterThan(0);
    });
  });
});

describe('CommitService', () => {
  let commitService: CommitService;

  beforeEach(() => {
    commitService = new CommitService();
  });

  describe('startCommit', () => {
    it('should start commit and activate scheduling', async () => {
      const mockCommit = {
        id: 'test-commit',
        userId: 'test-user',
        status: 'pending'
      };

      jest.spyOn(commitService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue(mockCommit),
          updateDocument: jest.fn().mockResolvedValue(undefined)
        });

      jest.spyOn(commitService as any, 'schedulerService')
        .mockReturnValue({
          activateCommitScheduling: jest.fn().mockResolvedValue({ success: true })
        });

      const result = await commitService.startCommit('test-commit', 'test-user', {});

      expect(result.success).toBe(true);
      expect(result.action).toBe('started_with_scheduling');
    });

    it('should handle commit not found', async () => {
      jest.spyOn(commitService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue(null)
        });

      const result = await commitService.startCommit('non-existent', 'test-user');

      expect(result.success).toBe(false);
      expect(result.action).toBe('error');
    });
  });

  describe('performDailyCommitCheck', () => {
    it('should process deadline violations', async () => {
      const mockCommit = {
        id: 'test-commit',
        status: 'active',
        timezone: 'UTC'
      };

      jest.spyOn(commitService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue(mockCommit)
        });

      jest.spyOn(commitService as any, 'hasDeadlinePassed')
        .mockResolvedValue(true);

      jest.spyOn(commitService as any, 'hasUserSubmittedToday')
        .mockResolvedValue(false);

      const result = await commitService.performDailyCommitCheck(
        'test-commit',
        'test-user',
        { isRecurring: true }
      );

      expect(result.success).toBe(true);
      expect(result.action).toBe('deadline_violation');
    });

    it('should skip checks for inactive commits', async () => {
      const mockCommit = {
        id: 'test-commit',
        status: 'completed'
      };

      jest.spyOn(commitService as any, 'dbService', 'get')
        .mockReturnValue({
          getDocument: jest.fn().mockResolvedValue(mockCommit)
        });

      const result = await commitService.performDailyCommitCheck(
        'test-commit',
        'test-user',
        {}
      );

      expect(result.success).toBe(true);
      expect(result.action).toBe('skipped_inactive');
    });
  });
});

// Integration tests
describe('Commit Scheduling Integration', () => {
  it('should handle complete commit lifecycle', async () => {
    const commitScheduler = new CommitSchedulerService();
    const commitService = new CommitService();

    // Setup scheduling
    const config: CommitSchedulerConfig = {
      commitId: 'integration-test',
      userId: 'test-user',
      timezone: 'UTC',
      startDate: new Date(),
      frequency: 'daily',
      deadlineType: 'midnight',
      autoVerificationEnabled: false,
      verificationType: 'manual'
    };

    const setupResult = await commitScheduler.setupCommitScheduling(config);
    expect(setupResult.success).toBe(true);

    // Start commit
    const startResult = await commitService.startCommit(
      config.commitId,
      config.userId,
      config
    );
    expect(startResult.success).toBe(true);

    // End commit
    const endResult = await commitService.endCommit(
      config.commitId,
      config.userId,
      'duration_completed'
    );
    expect(endResult.success).toBe(true);

    // Cleanup scheduling
    const cleanupResult = await commitScheduler.deleteCommitScheduling(
      config.commitId,
      config.userId
    );
    expect(cleanupResult.success).toBe(true);
  });
});
