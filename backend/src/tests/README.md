# Timezone Compliance Testing

This directory contains comprehensive tests to verify timezone compliance across all scheduled jobs in the Accustom backend.

## Test Files

### `timezoneUtils.test.ts`
Tests the core timezone utility functions including:
- Timezone validation (`isValidTimezone`, `getSafeTimezone`)
- Timezone conversion utilities (`getCurrentDateInTimezone`, `convertDateToTimezone`)
- Timezone comparison functions (`findEarliestTimezone`, `findLatestTimezone`)
- DST handling (`isDSTTransitionPeriod`, `getSafeMidnightInTimezone`)
- Participant timezone extraction (`extractParticipantTimezones`)

### `timezoneCompliance.test.ts`
Tests timezone compliance across all scheduled job services:
- **NotificationService**: Deadline calculations, date conversions
- **CommitNotificationService**: Deadline timing, day calculations
- **CommitService**: Check scheduling, deadline validation
- **ProgramService**: Program day calculations, date handling
- **Cross-service consistency**: Ensures all services handle timezones consistently

## Running Tests

### Run all timezone tests:
```bash
npm run test:timezone
```

### Run timezone tests in watch mode:
```bash
npm run test:timezone:watch
```

### Run all tests:
```bash
npm run test:all
```

### Run specific test file:
```bash
npx jest timezoneUtils.test.ts
npx jest timezoneCompliance.test.ts
```

## Test Coverage Areas

### 1. Basic Timezone Validation
- Valid IANA timezone identifiers
- Invalid timezone handling
- Edge cases (unusual offsets, deprecated formats)

### 2. Date Conversion Accuracy
- Current date in different timezones
- Date-only conversions (no time component)
- Handling of date boundaries across timezones

### 3. DST Transition Handling
- Spring forward transitions
- Fall back transitions
- Non-DST timezone behavior
- Safety buffers for DST periods

### 4. Scheduled Job Compliance
- Notification timing calculations
- Commit deadline evaluations
- Program day determinations
- Cross-timezone consistency

### 5. Performance and Edge Cases
- Large-scale timezone operations
- Extreme date ranges (far past/future)
- Invalid input handling
- Error recovery mechanisms

## Key Test Scenarios

### Multi-Timezone Program Scenarios
Tests programs with participants across multiple timezones:
- New York (UTC-5/-4)
- London (UTC+0/+1)
- Tokyo (UTC+9)
- Sydney (UTC+10/+11)

### DST Transition Dates
Tests around known DST transition periods:
- US Spring Forward: Second Sunday in March
- US Fall Back: First Sunday in November
- EU transitions: Last Sunday in March/October

### Edge Cases
- Timezones with unusual offsets (e.g., UTC+5:45, UTC+12:45)
- Historical timezone changes
- Leap year handling
- Month boundary crossings

## Expected Behavior

### Timezone Handling Principles
1. **User-Centric**: All times should be calculated in the user's timezone
2. **Date-Only Operations**: Most operations should work with date-only (no time)
3. **Graceful Degradation**: Invalid timezones should fallback to UTC
4. **Consistency**: Same inputs should produce same outputs across services
5. **DST Safety**: DST transitions should be handled safely with buffers

### Notification Timing
- Notifications should be sent at the correct local time for each user
- Deadline calculations should account for user's local midnight
- Urgent reminders should be based on user's local time until deadline

### Commit Scheduling
- Daily checks should run at appropriate times in user's timezone
- Deadline violations should be detected accurately in user's local time
- Verification windows should respect user's timezone

### Program Lifecycle
- Program start/end should coordinate across all participant timezones
- Individual checkups should run based on user's local program day
- Status calculations should be timezone-aware

## Troubleshooting

### Common Issues
1. **Date Boundary Problems**: Ensure date-only operations don't include time
2. **DST Edge Cases**: Use safe midnight functions during DST transitions
3. **Invalid Timezones**: Always validate and provide fallbacks
4. **Performance**: Cache timezone calculations when possible

### Debugging Tips
1. Log timezone conversions with input/output timestamps
2. Test around DST transition dates specifically
3. Verify date-only operations return midnight times
4. Check cross-service consistency with same inputs

## Future Enhancements

### Additional Test Coverage
- Integration tests with real Firebase scheduler
- Load testing with many concurrent timezone operations
- Historical timezone data accuracy
- Mobile app timezone synchronization

### Monitoring
- Add timezone-related metrics to production monitoring
- Alert on timezone conversion errors
- Track DST transition handling success rates
- Monitor cross-timezone program coordination accuracy
