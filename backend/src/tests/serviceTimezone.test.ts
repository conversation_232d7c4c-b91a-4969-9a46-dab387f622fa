/**
 * Service-level timezone compliance tests
 * Tests that the timezone fixes in services work correctly
 */

// Mock dependencies
jest.mock('../services/dbService');
jest.mock('../services/schedulerService');
jest.mock('../services/fcmService');

describe('Service Timezone Compliance', () => {
  
  describe('NotificationService Timezone Methods', () => {
    test('should have timezone helper methods', () => {
      // Import the service
      const { NotificationService } = require('../services/notificationService');
      const service = new NotificationService();
      
      // Check that the service has the timezone helper method
      expect(typeof (service as any).getDateInTimezone).toBe('function');
      expect(typeof (service as any).calculateHoursUntilDeadline).toBe('function');
    });

    test('should handle timezone conversion without errors', () => {
      const { NotificationService } = require('../services/notificationService');
      const service = new NotificationService();
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const timezone = 'America/New_York';
      
      expect(() => {
        (service as any).getDateInTimezone(testDate, timezone);
      }).not.toThrow();
    });

    test('should handle invalid timezone gracefully', () => {
      const { NotificationService } = require('../services/notificationService');
      const service = new NotificationService();
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const invalidTimezone = 'Invalid/Timezone';
      
      expect(() => {
        (service as any).getDateInTimezone(testDate, invalidTimezone);
      }).not.toThrow();
      
      const result = (service as any).getDateInTimezone(testDate, invalidTimezone);
      expect(result).toBeInstanceOf(Date);
    });

    test('should calculate hours until deadline without errors', () => {
      const { NotificationService } = require('../services/notificationService');
      const service = new NotificationService();
      
      expect(() => {
        const hours = (service as any).calculateHoursUntilDeadline('America/New_York');
        expect(typeof hours).toBe('number');
        expect(hours).toBeGreaterThanOrEqual(0);
      }).not.toThrow();
    });
  });

  describe('ProgramService Timezone Methods', () => {
    test('should have timezone helper methods', () => {
      const { ProgramService } = require('../services/programService');
      const service = new ProgramService();
      
      expect(typeof (service as any).getDateInTimezone).toBe('function');
      expect(typeof (service as any).calculateParticipantProgramDay).toBe('function');
    });

    test('should handle timezone conversion without errors', () => {
      const { ProgramService } = require('../services/programService');
      const service = new ProgramService();
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const timezone = 'America/New_York';
      
      expect(() => {
        (service as any).getDateInTimezone(testDate, timezone);
      }).not.toThrow();
      
      const result = (service as any).getDateInTimezone(testDate, timezone);
      expect(result).toBeInstanceOf(Date);
      expect(result.getHours()).toBe(0); // Should be date-only
    });

    test('should calculate program day correctly', () => {
      const { ProgramService } = require('../services/programService');
      const service = new ProgramService();
      
      expect(() => {
        const result = (service as any).calculateParticipantProgramDay(
          '2024-01-01',
          'America/New_York',
          30
        );
        expect(result).toHaveProperty('currentDay');
        expect(result).toHaveProperty('status');
        expect(typeof result.currentDay).toBe('number');
        expect(result.currentDay).toBeGreaterThan(0);
      }).not.toThrow();
    });
  });

  describe('CommitService Timezone Methods', () => {
    test('should have timezone-aware methods', () => {
      const { CommitService } = require('../services/commitService');
      const service = new CommitService();
      
      // Check that private methods exist (they should be accessible for testing)
      expect(typeof (service as any).shouldPerformCheckToday).toBe('function');
      expect(typeof (service as any).hasDeadlinePassed).toBe('function');
    });

    test('should handle timezone operations without errors', async () => {
      const { CommitService } = require('../services/commitService');
      const service = new CommitService();
      
      const mockCommit = {
        timezone: 'America/New_York',
        schedule: {
          frequency: 'daily',
          startDate: '2024-01-01'
        }
      };
      
      expect(async () => {
        await (service as any).shouldPerformCheckToday(mockCommit, {});
      }).not.toThrow();
    });
  });

  describe('CommitNotificationService Timezone Methods', () => {
    test('should have timezone-aware methods', () => {
      const { CommitNotificationService } = require('../services/commitNotificationService');
      const service = new CommitNotificationService();
      
      expect(typeof (service as any).calculateTimeUntilDeadline).toBe('function');
      expect(typeof (service as any).getCurrentDayForCommit).toBe('function');
    });

    test('should calculate time until deadline without errors', () => {
      const { CommitNotificationService } = require('../services/commitNotificationService');
      const service = new CommitNotificationService();
      
      const mockCommit = {
        schedule: {
          deadline: {
            type: 'midnight'
          }
        }
      };
      
      expect(() => {
        const time = (service as any).calculateTimeUntilDeadline(mockCommit, 'America/New_York');
        expect(typeof time).toBe('number');
        expect(time).toBeGreaterThanOrEqual(0);
      }).not.toThrow();
    });

    test('should calculate current day without errors', () => {
      const { CommitNotificationService } = require('../services/commitNotificationService');
      const service = new CommitNotificationService();
      
      const mockCommit = {
        schedule: {
          startDate: '2024-01-01'
        }
      };
      
      expect(() => {
        const day = (service as any).getCurrentDayForCommit(mockCommit, 'America/New_York');
        expect(typeof day).toBe('number');
        expect(day).toBeGreaterThan(0);
      }).not.toThrow();
    });
  });

  describe('Cross-Service Consistency', () => {
    test('should handle same timezone consistently', () => {
      const { NotificationService } = require('../services/notificationService');
      const { ProgramService } = require('../services/programService');
      
      const notificationService = new NotificationService();
      const programService = new ProgramService();
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const timezone = 'America/New_York';
      
      const notificationResult = (notificationService as any).getDateInTimezone(testDate, timezone);
      const programResult = (programService as any).getDateInTimezone(testDate, timezone);
      
      // Both services should return the same date for the same input
      expect(notificationResult.getTime()).toBe(programResult.getTime());
    });

    test('should handle invalid timezones consistently', () => {
      const { NotificationService } = require('../services/notificationService');
      const { ProgramService } = require('../services/programService');
      const { CommitNotificationService } = require('../services/commitNotificationService');
      
      const notificationService = new NotificationService();
      const programService = new ProgramService();
      const commitNotificationService = new CommitNotificationService();
      
      const invalidTimezone = 'Invalid/Timezone';
      
      // All services should handle invalid timezones gracefully
      expect(() => {
        (notificationService as any).calculateHoursUntilDeadline(invalidTimezone);
      }).not.toThrow();
      
      expect(() => {
        (programService as any).getDateInTimezone(new Date(), invalidTimezone);
      }).not.toThrow();
      
      expect(() => {
        const mockCommit = { schedule: { deadline: { type: 'midnight' } } };
        (commitNotificationService as any).calculateTimeUntilDeadline(mockCommit, invalidTimezone);
      }).not.toThrow();
    });
  });

  describe('Error Recovery', () => {
    test('should not crash with malformed inputs', () => {
      const { NotificationService } = require('../services/notificationService');
      const service = new NotificationService();
      
      // Test with various malformed inputs
      expect(() => {
        (service as any).getDateInTimezone(null, 'America/New_York');
      }).not.toThrow();
      
      expect(() => {
        (service as any).getDateInTimezone(new Date(), null);
      }).not.toThrow();
      
      expect(() => {
        (service as any).calculateHoursUntilDeadline(null);
      }).not.toThrow();
    });

    test('should provide fallback values', () => {
      const { NotificationService } = require('../services/notificationService');
      const service = new NotificationService();
      
      // Should return a valid number even with invalid timezone
      const hours = (service as any).calculateHoursUntilDeadline('Invalid/Timezone');
      expect(typeof hours).toBe('number');
      expect(hours).toBeGreaterThanOrEqual(0);
      
      // Should return a valid date even with invalid inputs
      const date = (service as any).getDateInTimezone(new Date(), 'Invalid/Timezone');
      expect(date).toBeInstanceOf(Date);
    });
  });
});
