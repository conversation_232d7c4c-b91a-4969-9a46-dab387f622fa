/**
 * Timezone compliance tests for scheduled jobs
 * Tests that all scheduled jobs properly handle user timezones
 */

import { NotificationService } from '../services/notificationService';
import { CommitNotificationService } from '../services/commitNotificationService';
import { CommitService } from '../services/commitService';
import { ProgramService } from '../services/programService';

// Mock dependencies
jest.mock('../services/dbService');
jest.mock('../services/schedulerService');
jest.mock('../services/fcmService');

describe('Timezone Compliance in Scheduled Jobs', () => {
  
  describe('NotificationService Timezone Compliance', () => {
    let notificationService: NotificationService;
    
    beforeEach(() => {
      notificationService = new NotificationService();
    });

    test('should calculate hours until deadline correctly across timezones', () => {
      // Test the private method through reflection for testing purposes
      const calculateHoursUntilDeadline = (notificationService as any).calculateHoursUntilDeadline;
      
      // Mock current time to a specific moment
      const mockDate = new Date('2024-01-15T20:00:00Z'); // 8 PM UTC
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      // Test different timezones
      const nyHours = calculateHoursUntilDeadline.call(notificationService, 'America/New_York');
      const londonHours = calculateHoursUntilDeadline.call(notificationService, 'Europe/London');
      const tokyoHours = calculateHoursUntilDeadline.call(notificationService, 'Asia/Tokyo');
      
      expect(typeof nyHours).toBe('number');
      expect(typeof londonHours).toBe('number');
      expect(typeof tokyoHours).toBe('number');
      
      expect(nyHours).toBeGreaterThanOrEqual(0);
      expect(londonHours).toBeGreaterThanOrEqual(0);
      expect(tokyoHours).toBeGreaterThanOrEqual(0);
      
      // Hours should be different across timezones
      expect(nyHours).not.toBe(londonHours);
      expect(londonHours).not.toBe(tokyoHours);
      
      jest.restoreAllMocks();
    });

    test('should handle invalid timezones gracefully', () => {
      const calculateHoursUntilDeadline = (notificationService as any).calculateHoursUntilDeadline;
      
      expect(() => {
        calculateHoursUntilDeadline.call(notificationService, 'Invalid/Timezone');
      }).not.toThrow();
      
      const result = calculateHoursUntilDeadline.call(notificationService, 'Invalid/Timezone');
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThanOrEqual(0);
    });

    test('should properly convert dates to user timezone', () => {
      const getDateInTimezone = (notificationService as any).getDateInTimezone;
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      
      const nyDate = getDateInTimezone.call(notificationService, testDate, 'America/New_York');
      const londonDate = getDateInTimezone.call(notificationService, testDate, 'Europe/London');
      const utcDate = getDateInTimezone.call(notificationService, testDate, 'UTC');
      
      expect(nyDate).toBeInstanceOf(Date);
      expect(londonDate).toBeInstanceOf(Date);
      expect(utcDate).toBeInstanceOf(Date);
      
      // All should be date-only (time = 00:00:00)
      expect(nyDate.getHours()).toBe(0);
      expect(londonDate.getHours()).toBe(0);
      expect(utcDate.getHours()).toBe(0);
    });
  });

  describe('CommitNotificationService Timezone Compliance', () => {
    let commitNotificationService: CommitNotificationService;
    
    beforeEach(() => {
      commitNotificationService = new CommitNotificationService();
    });

    test('should calculate time until deadline correctly', () => {
      const calculateTimeUntilDeadline = (commitNotificationService as any).calculateTimeUntilDeadline;
      
      const mockCommit = {
        schedule: {
          deadline: {
            type: 'before',
            time: '23:00'
          }
        }
      };
      
      const mockDate = new Date('2024-01-15T20:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      const nyTime = calculateTimeUntilDeadline.call(commitNotificationService, mockCommit, 'America/New_York');
      const londonTime = calculateTimeUntilDeadline.call(commitNotificationService, mockCommit, 'Europe/London');
      
      expect(typeof nyTime).toBe('number');
      expect(typeof londonTime).toBe('number');
      expect(nyTime).toBeGreaterThanOrEqual(0);
      expect(londonTime).toBeGreaterThanOrEqual(0);
      
      jest.restoreAllMocks();
    });

    test('should calculate current day for commit correctly across timezones', () => {
      const getCurrentDayForCommit = (commitNotificationService as any).getCurrentDayForCommit;
      
      const mockCommit = {
        schedule: {
          startDate: '2024-01-01'
        }
      };
      
      const mockDate = new Date('2024-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      const nyDay = getCurrentDayForCommit.call(commitNotificationService, mockCommit, 'America/New_York');
      const londonDay = getCurrentDayForCommit.call(commitNotificationService, mockCommit, 'Europe/London');
      const tokyoDay = getCurrentDayForCommit.call(commitNotificationService, mockCommit, 'Asia/Tokyo');
      
      expect(typeof nyDay).toBe('number');
      expect(typeof londonDay).toBe('number');
      expect(typeof tokyoDay).toBe('number');
      
      expect(nyDay).toBeGreaterThan(0);
      expect(londonDay).toBeGreaterThan(0);
      expect(tokyoDay).toBeGreaterThan(0);
      
      // Days might be different due to timezone differences
      // Tokyo is ahead, so might be one day more
      expect(Math.abs(nyDay - tokyoDay)).toBeLessThanOrEqual(1);
      
      jest.restoreAllMocks();
    });

    test('should handle DST transitions in deadline calculations', () => {
      const calculateTimeUntilDeadline = (commitNotificationService as any).calculateTimeUntilDeadline;
      
      const mockCommit = {
        schedule: {
          deadline: {
            type: 'midnight'
          }
        }
      };
      
      // Test around DST transition dates
      const springTransition = new Date('2024-03-10T12:00:00Z');
      const fallTransition = new Date('2024-11-03T12:00:00Z');
      
      jest.spyOn(global, 'Date').mockImplementation(() => springTransition as any);
      const springResult = calculateTimeUntilDeadline.call(commitNotificationService, mockCommit, 'America/New_York');
      
      jest.spyOn(global, 'Date').mockImplementation(() => fallTransition as any);
      const fallResult = calculateTimeUntilDeadline.call(commitNotificationService, mockCommit, 'America/New_York');
      
      expect(typeof springResult).toBe('number');
      expect(typeof fallResult).toBe('number');
      expect(springResult).toBeGreaterThanOrEqual(0);
      expect(fallResult).toBeGreaterThanOrEqual(0);
      
      jest.restoreAllMocks();
    });
  });

  describe('CommitService Timezone Compliance', () => {
    let commitService: CommitService;
    
    beforeEach(() => {
      commitService = new CommitService();
    });

    test('should determine if check should run today correctly across timezones', async () => {
      const shouldPerformCheckToday = (commitService as any).shouldPerformCheckToday;
      
      const mockCommit = {
        timezone: 'America/New_York',
        schedule: {
          frequency: 'weekly',
          startDate: '2024-01-01',
          duration: 4
        }
      };
      
      const mockSchedulerData = {};
      
      const mockDate = new Date('2024-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      const result = await shouldPerformCheckToday.call(commitService, mockCommit, mockSchedulerData);
      expect(typeof result).toBe('boolean');
      
      jest.restoreAllMocks();
    });

    test('should check deadline correctly across timezones', async () => {
      const hasDeadlinePassed = (commitService as any).hasDeadlinePassed;
      
      const mockCommit = {
        timezone: 'America/New_York',
        schedule: {
          deadline: {
            type: 'before',
            time: '23:00'
          }
        }
      };
      
      const mockSchedulerData = {
        deadlineType: 'before',
        deadlineTime: '23:00'
      };
      
      const mockDate = new Date('2024-01-15T20:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      const result = await hasDeadlinePassed.call(commitService, mockCommit, mockSchedulerData);
      expect(typeof result).toBe('boolean');
      
      jest.restoreAllMocks();
    });
  });

  describe('ProgramService Timezone Compliance', () => {
    let programService: ProgramService;
    
    beforeEach(() => {
      programService = new ProgramService();
    });

    test('should calculate participant program day correctly across timezones', () => {
      const calculateParticipantProgramDay = (programService as any).calculateParticipantProgramDay;
      
      const mockDate = new Date('2024-01-15T12:00:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      
      const nyResult = calculateParticipantProgramDay.call(
        programService, 
        '2024-01-01', 
        'America/New_York', 
        30
      );
      
      const tokyoResult = calculateParticipantProgramDay.call(
        programService, 
        '2024-01-01', 
        'Asia/Tokyo', 
        30
      );
      
      expect(nyResult).toHaveProperty('currentDay');
      expect(nyResult).toHaveProperty('status');
      expect(tokyoResult).toHaveProperty('currentDay');
      expect(tokyoResult).toHaveProperty('status');
      
      expect(typeof nyResult.currentDay).toBe('number');
      expect(typeof tokyoResult.currentDay).toBe('number');
      expect(nyResult.currentDay).toBeGreaterThan(0);
      expect(tokyoResult.currentDay).toBeGreaterThan(0);
      
      // Days might be different due to timezone differences
      expect(Math.abs(nyResult.currentDay - tokyoResult.currentDay)).toBeLessThanOrEqual(1);
      
      jest.restoreAllMocks();
    });

    test('should convert dates to timezone correctly', () => {
      const getDateInTimezone = (programService as any).getDateInTimezone;
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      
      const nyDate = getDateInTimezone.call(programService, testDate, 'America/New_York');
      const londonDate = getDateInTimezone.call(programService, testDate, 'Europe/London');
      
      expect(nyDate).toBeInstanceOf(Date);
      expect(londonDate).toBeInstanceOf(Date);
      
      // Should be date-only
      expect(nyDate.getHours()).toBe(0);
      expect(londonDate.getHours()).toBe(0);
    });
  });

  describe('Cross-Service Timezone Consistency', () => {
    test('should handle same timezone consistently across services', () => {
      const notificationService = new NotificationService();
      const programService = new ProgramService();
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const timezone = 'America/New_York';
      
      const notificationDate = (notificationService as any).getDateInTimezone(testDate, timezone);
      const programDate = (programService as any).getDateInTimezone(testDate, timezone);
      
      // Both services should return the same date for the same input
      expect(notificationDate.getTime()).toBe(programDate.getTime());
    });

    test('should handle invalid timezones consistently across services', () => {
      const notificationService = new NotificationService();
      const commitNotificationService = new CommitNotificationService();
      
      const invalidTimezone = 'Invalid/Timezone';
      
      // Both services should handle invalid timezones gracefully
      expect(() => {
        (notificationService as any).calculateHoursUntilDeadline(invalidTimezone);
      }).not.toThrow();
      
      expect(() => {
        const mockCommit = { schedule: { deadline: { type: 'midnight' } } };
        (commitNotificationService as any).calculateTimeUntilDeadline(mockCommit, invalidTimezone);
      }).not.toThrow();
    });
  });

  describe('Performance and Edge Cases', () => {
    test('should handle large number of timezone calculations efficiently', () => {
      const notificationService = new NotificationService();
      const getDateInTimezone = (notificationService as any).getDateInTimezone;
      
      const testDate = new Date('2024-01-15T12:00:00Z');
      const timezones = [
        'America/New_York', 'Europe/London', 'Asia/Tokyo', 'Australia/Sydney',
        'America/Los_Angeles', 'Europe/Paris', 'Asia/Shanghai', 'America/Chicago'
      ];
      
      const startTime = Date.now();
      
      // Perform many timezone conversions
      for (let i = 0; i < 100; i++) {
        timezones.forEach(tz => {
          getDateInTimezone.call(notificationService, testDate, tz);
        });
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000);
    });

    test('should handle extreme dates without errors', () => {
      const notificationService = new NotificationService();
      const getDateInTimezone = (notificationService as any).getDateInTimezone;
      
      const farFuture = new Date('2099-12-31T12:00:00Z');
      const farPast = new Date('1900-01-01T12:00:00Z');
      
      expect(() => {
        getDateInTimezone.call(notificationService, farFuture, 'America/New_York');
        getDateInTimezone.call(notificationService, farPast, 'America/New_York');
      }).not.toThrow();
    });
  });
});
