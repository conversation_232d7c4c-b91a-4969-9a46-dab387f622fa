/**
 * Email Notification Cloud Functions
 * Provides HTTP endpoints for sending email notifications
 */

import { https } from 'firebase-functions/v2';
import { EmailService } from './services/emailService';
import { NotificationDispatcher } from './services/notificationDispatcher';
import { DatabaseService } from './services/databaseService';

// Initialize services lazily to avoid module loading issues
let emailService: EmailService | null = null;
let notificationDispatcher: NotificationDispatcher | null = null;
let databaseService: DatabaseService | null = null;

function getEmailService(): EmailService {
  if (!emailService) {
    emailService = new EmailService();
  }
  return emailService;
}

function getNotificationDispatcher(): NotificationDispatcher {
  if (!notificationDispatcher) {
    notificationDispatcher = new NotificationDispatcher();
  }
  return notificationDispatcher;
}

function getDatabaseService(): DatabaseService {
  if (!databaseService) {
    databaseService = new DatabaseService();
  }
  return databaseService;
}

/**
 * Send email notification to a user
 *
 * HTTP endpoint for sending email notifications
 * POST /sendEmailNotification
 *
 * Body:
 * {
 *   userId: string,
 *   notification: {
 *     title: string,
 *     message: string,
 *     type: 'account' | 'program' | 'points' | 'reminder',
 *     priority: 'low' | 'medium' | 'high',
 *     data?: Record<string, string>
 *   }
 * }
 */
export const sendEmailNotification = https.onCall(async (request) => {
  const { userId, notification } = request.data;

  // Validate input
  if (!userId || !notification) {
    throw new https.HttpsError('invalid-argument', 'userId and notification are required');
  }

  if (!notification.title || !notification.message || !notification.type || !notification.priority) {
    throw new https.HttpsError('invalid-argument', 'notification must include title, message, type, and priority');
  }

  try {
    // Send email notification
    const result = await getEmailService().sendEmailNotification(userId, notification);

    if (result.success) {
      return {
        success: true,
        messageId: result.messageId,
        timestamp: result.timestamp
      };
    } else {
      throw new https.HttpsError('internal', `Failed to send email: ${result.error}`);
    }
  } catch (error) {
    console.error('Error in sendEmailNotification function:', error);
    throw new https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error occurred');
  }
});



/**
 * Send multi-channel notification (app + email + SMS + WhatsApp)
 *
 * HTTP endpoint for sending notifications to all enabled channels
 * POST /sendMultiChannelNotification
 *
 * Body:
 * {
 *   userId: string,
 *   notification: {
 *     title: string,
 *     message: string,
 *     type: 'account' | 'program' | 'points' | 'reminder',
 *     priority: 'low' | 'medium' | 'high',
 *     data?: Record<string, string>
 *   },
 *   channels?: {
 *     app?: boolean,
 *     email?: boolean,
 *     sms?: boolean,
 *     whatsapp?: boolean
 *   }
 * }
 */
export const sendMultiChannelNotification = https.onCall(async (request) => {
  const { userId, notification, channels } = request.data;

  // Validate input
  if (!userId || !notification) {
    throw new https.HttpsError('invalid-argument', 'userId and notification are required');
  }

  if (!notification.title || !notification.message || !notification.type || !notification.priority) {
    throw new https.HttpsError('invalid-argument', 'notification must include title, message, type, and priority');
  }

  try {
    // Send notification to all enabled channels
    const result = await getNotificationDispatcher().dispatchNotification(userId, notification, channels);

    return {
      success: result.success,
      channels: result.channels,
      timestamp: result.timestamp,
      message: result.success
        ? 'Multi-channel notification sent successfully'
        : 'Some channels failed to send notification'
    };
  } catch (error) {
    console.error('Error in sendMultiChannelNotification function:', error);
    throw new https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error occurred');
  }
});

/**
 * Send welcome notification to all channels
 *
 * HTTP endpoint for sending welcome notifications
 * POST /sendWelcomeNotification
 *
 * Body:
 * {
 *   userId: string,
 *   userName: string
 * }
 */
export const sendWelcomeNotification = https.onCall(async (request) => {
  const { userId, userName } = request.data;

  // Validate input
  if (!userId || !userName) {
    throw new https.HttpsError('invalid-argument', 'userId and userName are required');
  }

  try {
    // Send welcome notification to all enabled channels
    const result = await getNotificationDispatcher().sendWelcomeNotification(userId, userName);

    return {
      success: result.success,
      channels: result.channels,
      timestamp: result.timestamp,
      message: result.success
        ? 'Welcome notification sent successfully'
        : 'Some channels failed to send welcome notification'
    };
  } catch (error) {
    console.error('Error in sendWelcomeNotification function:', error);
    throw new https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error occurred');
  }
});

/**
 * Update user notification preferences
 *
 * HTTP endpoint for updating user notification preferences
 * POST /updateNotificationPreferences
 *
 * Body:
 * {
 *   userId: string,
 *   preferences: {
 *     channels: ('app' | 'email' | 'sms' | 'whatsapp')[],
 *     emailNotifications?: {
 *       reminders?: boolean,
 *       programAlerts?: boolean,
 *       marketingNotifications?: boolean,
 *       chatRoomNotifications?: boolean
 *     },
 *     smsNotifications?: {
 *       reminders?: boolean,
 *       urgentOnly?: boolean
 *     },
 *     whatsappNotifications?: {
 *       reminders?: boolean,
 *       programUpdates?: boolean
 *     }
 *   }
 * }
 */
export const updateNotificationPreferences = https.onCall(async (request) => {
  const { userId, preferences } = request.data;

  // Validate input
  if (!userId || !preferences) {
    throw new https.HttpsError('invalid-argument', 'userId and preferences are required');
  }

  try {
    // Update user notification preferences
    const db = getDatabaseService().getDatabase();
    await db.collection('users').doc(userId).update({
      notificationPreferences: {
        ...preferences,
        updatedAt: new Date().toISOString()
      }
    });

    return {
      success: true,
      message: 'Notification preferences updated successfully',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in updateNotificationPreferences function:', error);
    throw new https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error occurred');
  }
});

/**
 * Send reminder notification (used by scheduler)
 *
 * HTTP endpoint for sending reminder notifications
 * POST /sendReminderNotification
 *
 * Body:
 * {
 *   userId: string,
 *   programName: string,
 *   programId: string,
 *   dayNumber?: number,
 *   streakCount?: number
 * }
 */
export const sendReminderNotification = https.onCall(async (request) => {
  const { userId, programName, programId, dayNumber, streakCount } = request.data;

  // Validate input
  if (!userId || !programName || !programId) {
    throw new https.HttpsError('invalid-argument', 'userId, programName, and programId are required');
  }

  try {
    const notification = {
      title: `Progress Check-in`,
      message: `Time to log your progress for "${programName}"${dayNumber ? ` - Day ${dayNumber}` : ''}.`,
      type: 'reminder' as const,
      priority: 'medium' as const,
      data: {
        action: 'submit_progress',
        programId: programId,
        programName: programName,
        dayNumber: dayNumber?.toString(),
        streakCount: streakCount?.toString()
      }
    };

    // Send to all enabled channels
    const result = await getNotificationDispatcher().dispatchNotification(userId, notification);

    return {
      success: result.success,
      channels: result.channels,
      timestamp: result.timestamp,
      message: 'Reminder notification sent'
    };
  } catch (error) {
    console.error('Error in sendReminderNotification function:', error);
    throw new https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error occurred');
  }
});

/**
 * Send test email to verify branding
 *
 * HTTP endpoint for testing email branding
 * POST /sendTestEmailBranding
 */
export const sendTestEmailBranding = https.onCall(async (request) => {
  const { adminEmail } = request.data;

  // Validate input
  if (!adminEmail) {
    throw new https.HttpsError('invalid-argument', 'adminEmail is required');
  }

  try {
    // Send test email directly
    const { EmailTemplates } = await import('./services/emailTemplates');
    const { getEmailConfig } = await import('./utils/config');
    const sgMail = (await import('@sendgrid/mail')).default;

    // Initialize SendGrid
    const emailConfig = getEmailConfig();
    sgMail.setApiKey(emailConfig.sendgridApiKey);

    // Generate test email template with new branding
    const templateData = {
      userName: 'Admin',
      title: 'ACCUSTOM Branding Test',
      message: 'This test email showcases the new luxury gold branding that perfectly matches your ACCUSTOM app!',
      actionUrl: 'https://accustom.app'
    };

    const template = EmailTemplates.getTestTemplate(templateData);

    // Send email directly
    const msg = {
      to: adminEmail,
      from: {
        email: emailConfig.fromEmail,
        name: emailConfig.fromName
      },
      subject: template.subject,
      text: template.text,
      html: template.html
    };

    const response = await sgMail.send(msg);

    return {
      success: true,
      messageId: response[0].headers['x-message-id'] || 'unknown',
      timestamp: new Date().toISOString(),
      message: `Test email with new ACCUSTOM branding sent successfully to ${adminEmail}`,
      branding: {
        colors: ['#D4AF37', '#FFD700'],
        typography: 'Montserrat',
        styling: 'Luxury Gold Theme'
      }
    };
  } catch (error) {
    console.error('Error in sendTestEmailBranding function:', error);
    throw new https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error occurred');
  }
});


