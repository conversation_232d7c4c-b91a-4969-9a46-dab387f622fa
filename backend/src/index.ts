import * as admin from 'firebase-admin';

// Initialize Firebase Admin with proper configuration
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      // Use default credentials in Cloud Functions environment
      // The service account will be configured via IAM roles
      projectId: process.env.GCLOUD_PROJECT || process.env.GCP_PROJECT || 'betonself',
    });

    // Configure Firestore settings to ignore undefined properties
    const firestore = admin.firestore();
    firestore.settings({
      ignoreUndefinedProperties: true
    });

  } catch (error) {
    console.error('Failed to initialize Firebase Admin:', error);
    throw error;
  }
}

// Import utilities
import { validateEnvironment } from './utils/config';
import { createProgramFunction, createParticipantFunction, createSchedulerHttpsFunction, createMaintenanceFunction } from './utils/functionFactory';
import { ServiceContainer } from './services/serviceContainer';

// Validate environment on startup
validateEnvironment();

// Import and export GitHub OAuth functions
export { githubOAuthCallback, getGitHubToken } from './githubOAuth';

// Import and export FCM notification functions
export { registerFcmToken, unregisterFcmToken, sendPushNotification } from './fcm';

// Import and export email notification functions
export {
  sendEmailNotification,
  sendMultiChannelNotification,
  sendWelcomeNotification,
  updateNotificationPreferences,
  sendReminderNotification,
  sendTestEmailBranding
} from './emailNotifications';



// Program lifecycle management functions
// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const programStarter = createSchedulerHttpsFunction(
  'programStarter',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().startProgram(data.programId);
  },
  ['programId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const programEnder = createSchedulerHttpsFunction(
  'programEnder',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().endProgramWithTimezoneCheck(data.programId);
  },
  ['programId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const individualDailyCheckup = createSchedulerHttpsFunction(
  'individualDailyCheckup',
  async (data: { programId: string; userId: string; [key: string]: any }, services) => {
    return await services.getProgramService().performIndividualCheckup(data.programId, data.userId, data);
  },
  ['programId', 'userId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const dailyNotificationScheduler = createSchedulerHttpsFunction(
  'dailyNotificationScheduler',
  async (data: { programId: string; userId: string; [key: string]: any }, services) => {
    const result = await services.getNotificationService().sendDailyNotifications(data.programId, data.userId, data);
    return result;
  },
  ['programId', 'userId']
);



// Program Scheduler Management Functions
// These functions handle two-stage program scheduling system

/**
 * Set up program start and initial end schedulers
 * Start: UTC timezone, End: Initial UTC with timezone check system
 */
export const setupProgramStartScheduler = createProgramFunction(
  'setupProgramStartScheduler',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().setupProgramStartScheduler(data.programId);
  }
);

/**
 * Set up initial program end scheduler (UTC-based, two-stage system)
 * Will check for ongoing participants and reschedule if needed
 */
export const setupProgramEndScheduler = createProgramFunction(
  'setupProgramEndScheduler',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().setupProgramEndScheduler(data.programId);
  }
);

/**
 * Setup individual scheduling for a new participant enrollment
 */
export const setupIndividualSchedulingOnEnrollment = createParticipantFunction(
  'setupIndividualSchedulingOnEnrollment',
  async (data: { programId: string; userId: string }, services) => {
    return await services.getProgramService().setupIndividualScheduling(data.programId, data.userId);
  }
);

// Commit lifecycle management functions
// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const commitStarter = createSchedulerHttpsFunction(
  'commitStarter',
  async (data: { commitId: string; userId: string; schedulingConfig?: any }, services) => {
    return await services.getCommitService().startCommit(data.commitId, data.userId, data.schedulingConfig);
  },
  ['commitId', 'userId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const commitEnder = createSchedulerHttpsFunction(
  'commitEnder',
  async (data: { commitId: string; userId: string; reason: string }, services) => {
    return await services.getCommitService().endCommit(data.commitId, data.userId, data.reason);
  },
  ['commitId', 'userId', 'reason']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const commitDailyChecker = createSchedulerHttpsFunction(
  'commitDailyChecker',
  async (data: { commitId: string; userId: string; [key: string]: any }, services) => {
    return await services.getCommitService().performDailyCommitCheck(data.commitId, data.userId, data);
  },
  ['commitId', 'userId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const commitNotificationScheduler = createSchedulerHttpsFunction(
  'commitNotificationScheduler',
  async (data: { commitId: string; userId: string; [key: string]: any }, services) => {
    const result = await services.getCommitNotificationService().sendDailyCommitNotifications(data.commitId, data.userId, data);
    return result;
  },
  ['commitId', 'userId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const commitAutoVerifier = createSchedulerHttpsFunction(
  'commitAutoVerifier',
  async (data: { commitId: string; userId: string; verificationType: string; [key: string]: any }, services) => {
    const result = await services.getCommitVerificationService().performAutoVerification(data.commitId, data.userId, data);
    return result;
  },
  ['commitId', 'userId', 'verificationType']
);

// Commit Scheduler Management Functions
// These functions handle commit scheduling system

/**
 * Set up commit scheduling when a commit is created
 */
export const setupCommitScheduling = createMaintenanceFunction(
  'setupCommitScheduling',
  async (data: { commitId: string; userId: string; schedulingConfig: any }, services: ServiceContainer) => {
    // Convert ISO string dates back to Date objects
    const config = {
      ...data.schedulingConfig,
      startDate: new Date(data.schedulingConfig.startDate),
      endDate: data.schedulingConfig.endDate ? new Date(data.schedulingConfig.endDate) : undefined
    };


    return await services.getCommitSchedulerService().setupCommitScheduling(config);
  },
  ['commitId', 'userId', 'schedulingConfig']
);

/**
 * Delete commit scheduling when a commit is cancelled or completed
 */
export const deleteCommitScheduling = createMaintenanceFunction(
  'deleteCommitScheduling',
  async (data: { commitId: string; userId: string }, services: ServiceContainer) => {
    return await services.getCommitSchedulerService().deleteCommitScheduling(data.commitId, data.userId);
  },
  ['commitId', 'userId']
);

/**
 * Update commit scheduling configuration
 */
export const updateCommitScheduling = createMaintenanceFunction(
  'updateCommitScheduling',
  async (data: { commitId: string; userId: string; newConfig: any }, services: ServiceContainer) => {
    // Convert ISO string dates back to Date objects
    const config = {
      ...data.newConfig,
      startDate: data.newConfig.startDate ? new Date(data.newConfig.startDate) : undefined,
      endDate: data.newConfig.endDate ? new Date(data.newConfig.endDate) : undefined
    };

    return await services.getCommitSchedulerService().updateCommitScheduling(data.commitId, data.userId, config);
  },
  ['commitId', 'userId', 'newConfig']
);


