import * as admin from 'firebase-admin';
import { https } from 'firebase-functions/v2';
import fetch from 'node-fetch';

// Collection paths
const USERS_COLLECTION = 'users';
const FCM_TOKENS_SUBCOLLECTION = 'fcmTokens';
const NOTIFICATIONS_SUBCOLLECTION = 'notifications';

// Notification types
export enum NotificationType {
  ACCOUNT = 'account',
  PROGRAM = 'program',
  POINTS = 'points',
  REMINDER = 'reminder'
}

// Notification priority
export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// FCM message types
export enum FcmMessageType {
  NOTIFICATION = 'notification', // Shows even when app is in background
  DATA = 'data'                  // Only processed when app is in foreground
}

/**
 * Register an FCM token for a user
 *
 * This function stores the FCM token in a subcollection under the user's document
 * Each token is stored with device information and a timestamp
 *
 * @param userId - The user's ID
 * @param token - The FCM token to register
 * @param deviceInfo - Information about the user's device
 */
export const registerFcmToken = https.onCall(
  async (request) => {
    // Access data from the request
    const userId = request.data.userId;
    const token = request.data.token;
    const deviceInfo = request.data.deviceInfo || {};

    if (!userId || !token) {
      throw new https.HttpsError(
        'invalid-argument',
        'The function must be called with userId and token arguments.'
      );
    }

    try {
      // Create a unique ID for the token based on its value
      const tokenId = Buffer.from(token).toString('base64');

      // Store the token with device info and timestamp
      await admin.firestore()
        .collection(USERS_COLLECTION)
        .doc(userId)
        .collection(FCM_TOKENS_SUBCOLLECTION)
        .doc(tokenId)
        .set({
          token,
          deviceInfo,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        }, { merge: true });

      return { success: true };
    } catch (error) {
      console.error('Error registering FCM token:', error);
      throw new https.HttpsError(
        'internal',
        'An error occurred while registering the FCM token.'
      );
    }
  });

/**
 * Unregister an FCM token for a user
 *
 * This function removes the FCM token from the user's tokens subcollection
 *
 * @param userId - The user's ID
 * @param token - The FCM token to unregister
 */
export const unregisterFcmToken = https.onCall(
  async (request) => {
    const userId = request.data.userId;
    const token = request.data.token;

    if (!userId || !token) {
      throw new https.HttpsError(
        'invalid-argument',
        'The function must be called with userId and token arguments.'
      );
    }

    try {
      // Create a unique ID for the token based on its value
      const tokenId = Buffer.from(token).toString('base64');

      // Remove the token document
      await admin.firestore()
        .collection(USERS_COLLECTION)
        .doc(userId)
        .collection(FCM_TOKENS_SUBCOLLECTION)
        .doc(tokenId)
        .delete();

      return { success: true };
    } catch (error) {
      console.error('Error unregistering FCM token:', error);
      throw new https.HttpsError(
        'internal',
        'An error occurred while unregistering the FCM token.'
      );
    }
  });

/**
 * Send a push notification to a user via FCM
 *
 * This function sends a notification to all FCM tokens registered for a user
 * It also stores the notification in the user's notifications subcollection
 *
 * @param userId - The user's ID
 * @param notification - The notification data to send
 */
export const sendPushNotification = https.onCall(
  async (request) => {
    const userId = request.data.userId;
    const notification = request.data.notification;
    const messageType = request.data.messageType || FcmMessageType.NOTIFICATION;

    if (!userId || !notification || !notification.title || !notification.message) {
      throw new https.HttpsError(
        'invalid-argument',
        'The function must be called with userId and notification (title, message) arguments.'
      );
    }

    try {
      // First, store the notification in Firestore (existing in-app notification system)
      const notificationRef = admin.firestore()
        .collection(USERS_COLLECTION)
        .doc(userId)
        .collection(NOTIFICATIONS_SUBCOLLECTION)
        .doc();

      const notificationData = {
        id: notificationRef.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        time: admin.firestore.FieldValue.serverTimestamp(),
        read: false,
        data: notification.data || {},
      };

      await notificationRef.set(notificationData);

      // Then, get all FCM tokens for the user
      const tokensSnapshot = await admin.firestore()
        .collection(USERS_COLLECTION)
        .doc(userId)
        .collection(FCM_TOKENS_SUBCOLLECTION)
        .get();

      if (tokensSnapshot.empty) {
        return { success: true, inAppOnly: true };
      }

      // Extract tokens
      const tokens = tokensSnapshot.docs.map(doc => doc.data().token);

      // Separate Expo tokens from FCM tokens
      const expoTokens = tokens.filter(token => token.startsWith('ExponentPushToken['));
      const fcmTokens = tokens.filter(token => !token.startsWith('ExponentPushToken['));

      let totalSuccessCount = 0;
      let totalFailureCount = 0;
      const allFailedTokens: string[] = [];

      // Send to Expo tokens using Expo's push service
      if (expoTokens.length > 0) {
        const expoResult = await sendExpoNotifications(expoTokens, notification, notificationRef.id);
        totalSuccessCount += expoResult.successCount;
        totalFailureCount += expoResult.failureCount;
        allFailedTokens.push(...expoResult.failedTokens);
      }

      // Send to FCM tokens using Firebase messaging
      if (fcmTokens.length > 0) {
        const fcmResult = await sendFCMNotifications(fcmTokens, notification, notificationRef.id, messageType);
        totalSuccessCount += fcmResult.successCount;
        totalFailureCount += fcmResult.failureCount;
        allFailedTokens.push(...fcmResult.failedTokens);
      }


      // Clean up failed tokens
      if (allFailedTokens.length > 0) {
        await Promise.all(allFailedTokens.map(async token => {
          const tokenId = Buffer.from(token).toString('base64');
          const tokenDoc = await admin.firestore()
            .collection(USERS_COLLECTION)
            .doc(userId)
            .collection(FCM_TOKENS_SUBCOLLECTION)
            .doc(tokenId)
            .get();

          if (tokenDoc.exists) {
            await tokenDoc.ref.delete();
          }
        }));
      }

      return {
        success: true,
        notificationId: notificationRef.id,
        sentCount: totalSuccessCount,
        failureCount: totalFailureCount
      };
    } catch (error) {
      console.error('Error sending push notification:', error);
      throw new https.HttpsError(
        'internal',
        'An error occurred while sending the push notification.'
      );
    }
  });

/**
 * Send notifications to Expo tokens using Expo's push service
 */
async function sendExpoNotifications(
  tokens: string[],
  notification: any,
  notificationId: string
): Promise<{ successCount: number; failureCount: number; failedTokens: string[] }> {
  try {
    const messages = tokens.map(token => ({
      to: token,
      title: notification.title,
      body: notification.message,
      data: {
        notificationId,
        type: notification.type,
        priority: notification.priority,
        ...notification.data,
      },
      sound: 'default',
      badge: 1,
    }));

    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(messages),
    });

    const result = await response.json() as any;

    let successCount = 0;
    let failureCount = 0;
    const failedTokens: string[] = [];

    if (Array.isArray(result.data)) {
      result.data.forEach((item: any, index: number) => {
        if (item.status === 'ok') {
          successCount++;
        } else {
          failureCount++;
          failedTokens.push(tokens[index]);
          console.error('Expo push error:', item.message);
        }
      });
    } else {
      // Single message response
      if (result.data?.status === 'ok') {
        successCount = 1;
      } else {
        failureCount = 1;
        failedTokens.push(...tokens);
        console.error('Expo push error:', result.data?.message);
      }
    }

    return { successCount, failureCount, failedTokens };
  } catch (error) {
    console.error('Error sending Expo notifications:', error);
    return { successCount: 0, failureCount: tokens.length, failedTokens: tokens };
  }
}

/**
 * Send notifications to FCM tokens using Firebase messaging
 */
async function sendFCMNotifications(
  tokens: string[],
  notification: any,
  notificationId: string,
  messageType: string
): Promise<{ successCount: number; failureCount: number; failedTokens: string[] }> {
  try {
    // Prepare the FCM message
    let fcmMessage: admin.messaging.MulticastMessage;

    if (messageType === FcmMessageType.NOTIFICATION) {
      // Notification message (shows even when app is in background)
      fcmMessage = {
        tokens,
        notification: {
          title: notification.title,
          body: notification.message,
          imageUrl: notification.imageUrl,
        },
        data: {
          ...notification.data,
          notificationId,
          type: notification.type,
          priority: notification.priority,
        },
        android: {
          priority: notification.priority === NotificationPriority.HIGH ? 'high' : 'normal',
          notification: {
            channelId: `accustom-${notification.type}`,
            clickAction: 'FLUTTER_NOTIFICATION_CLICK',
          },
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
            },
          },
        },
      };
    } else {
      // Data-only message (only processed when app is in foreground)
      fcmMessage = {
        tokens,
        data: {
          title: notification.title,
          body: notification.message,
          notificationId,
          type: notification.type,
          priority: notification.priority,
          ...notification.data,
        },
      };
    }

    // Send the FCM message
    const response = await admin.messaging().sendMulticast(fcmMessage);

    const failedTokens: string[] = [];
    if (response.failureCount > 0) {
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          failedTokens.push(tokens[idx]);
          console.error('FCM error:', resp.error);
        }
      });
    }

    return {
      successCount: response.successCount,
      failureCount: response.failureCount,
      failedTokens
    };
  } catch (error) {
    console.error('Error sending FCM notifications:', error);
    return { successCount: 0, failureCount: tokens.length, failedTokens: tokens };
  }
}
