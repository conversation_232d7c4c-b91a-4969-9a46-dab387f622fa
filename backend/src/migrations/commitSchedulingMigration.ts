/**
 * Migration Script for Commit Scheduling System
 * Handles migration of existing commits to new scheduling system
 */

import * as admin from 'firebase-admin';
import { CommitSchedulerService } from '../services/commitSchedulerService';
import { commitMonitoringService } from '../utils/commitMonitoring';

export interface MigrationResult {
  success: boolean;
  totalCommits: number;
  migratedCommits: number;
  skippedCommits: number;
  failedCommits: number;
  errors: string[];
  duration: number;
}

export interface MigrationOptions {
  dryRun: boolean;
  batchSize: number;
  maxConcurrency: number;
  skipInactiveCommits: boolean;
  enableScheduling: boolean;
}

export class CommitSchedulingMigration {
  private db: admin.firestore.Firestore;
  private schedulerService: CommitSchedulerService;

  constructor() {
    this.db = admin.firestore();
    this.schedulerService = new CommitSchedulerService();
  }

  /**
   * Main migration function
   */
  async migrateCommitsToScheduling(options: MigrationOptions): Promise<MigrationResult> {
    const startTime = Date.now();

    const result: MigrationResult = {
      success: false,
      totalCommits: 0,
      migratedCommits: 0,
      skippedCommits: 0,
      failedCommits: 0,
      errors: [],
      duration: 0
    };

    try {
      // Get all commits that need migration
      const commits = await this.getCommitsForMigration(options);
      result.totalCommits = commits.length;


      if (options.dryRun) {
        return await this.performDryRun(commits, result);
      }

      // Process commits in batches
      const batches = this.createBatches(commits, options.batchSize);
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];

        const batchResults = await this.processBatch(batch, options);
        
        result.migratedCommits += batchResults.migrated;
        result.skippedCommits += batchResults.skipped;
        result.failedCommits += batchResults.failed;
        result.errors.push(...batchResults.errors);

        // Progress update
        const progress = ((i + 1) / batches.length * 100).toFixed(1);

        // Small delay between batches to avoid overwhelming the system
        if (i < batches.length - 1) {
          await this.sleep(1000);
        }
      }

      result.success = result.failedCommits === 0;
      result.duration = Date.now() - startTime;


      // Log migration completion
      await commitMonitoringService.createAlert({
        type: 'info',
        severity: 'low',
        title: 'Commit Scheduling Migration Completed',
        message: `Migration completed: ${result.migratedCommits} migrated, ${result.failedCommits} failed`,
        metadata: result
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Migration failed:', errorMessage);
      
      result.success = false;
      result.errors.push(errorMessage);
      result.duration = Date.now() - startTime;

      await commitMonitoringService.createAlert({
        type: 'error',
        severity: 'critical',
        title: 'Commit Scheduling Migration Failed',
        message: `Migration failed: ${errorMessage}`,
        metadata: { error: errorMessage, result }
      });

      return result;
    }
  }

  /**
   * Get commits that need migration
   */
  private async getCommitsForMigration(options: MigrationOptions): Promise<any[]> {
    let query = this.db.collection('commits');

    // Filter by status if specified
    if (options.skipInactiveCommits) {
      query = query.where('status', '==', 'active');
    }

    // Get commits that don't have scheduling enabled
    const snapshot = await query.get();
    
    return snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(commit => !commit.scheduling?.enabled); // Only migrate commits without scheduling
  }

  /**
   * Perform dry run to analyze what would be migrated
   */
  private async performDryRun(commits: any[], result: MigrationResult): Promise<MigrationResult> {

    for (const commit of commits) {
      try {
        const analysis = await this.analyzeCommitForMigration(commit);
        
        if (analysis.canMigrate) {
          result.migratedCommits++;
        } else {
          result.skippedCommits++;
        }
      } catch (error) {
        result.failedCommits++;
        const errorMsg = `${commit.id}: ${error}`;
        result.errors.push(errorMsg);
      }
    }

    result.success = true;
    result.duration = 0;
    return result;
  }

  /**
   * Analyze if a commit can be migrated
   */
  private async analyzeCommitForMigration(commit: any): Promise<{
    canMigrate: boolean;
    reason: string;
  }> {
    // Check if commit is active
    if (commit.status !== 'active') {
      return {
        canMigrate: false,
        reason: `Status is ${commit.status}, not active`
      };
    }

    // Check if commit has required fields
    if (!commit.userId) {
      return {
        canMigrate: false,
        reason: 'Missing userId field'
      };
    }

    // Check if user exists
    const userDoc = await this.db.collection('users').doc(commit.userId).get();
    if (!userDoc.exists) {
      return {
        canMigrate: false,
        reason: 'User document not found'
      };
    }

    // Check if commit has schedule information
    if (!commit.schedule) {
      return {
        canMigrate: true,
        reason: 'Will create default schedule (daily, midnight deadline)'
      };
    }

    return {
      canMigrate: true,
      reason: 'Has existing schedule, will migrate to new system'
    };
  }

  /**
   * Process a batch of commits
   */
  private async processBatch(
    commits: any[], 
    options: MigrationOptions
  ): Promise<{
    migrated: number;
    skipped: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      migrated: 0,
      skipped: 0,
      failed: 0,
      errors: []
    };

    // Process commits with limited concurrency
    const semaphore = new Array(options.maxConcurrency).fill(null);
    const promises = commits.map(async (commit, index) => {
      // Wait for available slot
      await semaphore[index % options.maxConcurrency];
      
      try {
        const migrationResult = await this.migrateCommit(commit, options);
        
        if (migrationResult.success) {
          results.migrated++;
        } else {
          results.skipped++;
        }
      } catch (error) {
        results.failed++;
        const errorMsg = `Failed to migrate commit ${commit.id}: ${error}`;
        results.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    });

    await Promise.all(promises);
    return results;
  }

  /**
   * Migrate a single commit
   */
  private async migrateCommit(
    commit: any, 
    options: MigrationOptions
  ): Promise<{
    success: boolean;
    reason?: string;
  }> {
    // Analyze if commit can be migrated
    const analysis = await this.analyzeCommitForMigration(commit);
    if (!analysis.canMigrate) {
      return {
        success: false,
        reason: analysis.reason
      };
    }

    // Get user data for timezone
    const userDoc = await this.db.collection('users').doc(commit.userId).get();
    const userData = userDoc.data();
    const timezone = userData?.timezone || 'UTC';

    // Create scheduling configuration
    const schedulingConfig = {
      commitId: commit.id,
      userId: commit.userId,
      timezone,
      startDate: commit.schedule?.startDate ? new Date(commit.schedule.startDate) : new Date(),
      endDate: commit.schedule?.endDate ? new Date(commit.schedule.endDate) : undefined,
      frequency: commit.schedule?.frequency || 'daily',
      deadlineType: commit.schedule?.deadline?.type || 'midnight',
      deadlineTime: commit.schedule?.deadline?.time,
      deadlineEndTime: commit.schedule?.deadline?.endTime,
      autoVerificationEnabled: commit.verification?.autoVerificationEnabled || false,
      verificationType: commit.verification?.type || 'manual',
      notificationPreferences: {
        reminderTime: '19:00',
        urgentRemindersEnabled: true,
        weeklyDigestEnabled: false,
        weeklyDigestDay: 'sunday' as const,
        autoVerificationNotifications: true
      }
    };

    // Set up scheduling if enabled
    if (options.enableScheduling) {
      const setupResult = await this.schedulerService.setupCommitScheduling(schedulingConfig);
      
      if (!setupResult.success) {
        throw new Error(`Scheduling setup failed: ${setupResult.errors.join(', ')}`);
      }
    }

    // Update commit document with scheduling fields
    await this.db.collection('commits').doc(commit.id).update({
      'scheduling.enabled': options.enableScheduling,
      'scheduling.migratedAt': admin.firestore.FieldValue.serverTimestamp(),
      'scheduling.migrationVersion': '1.0.0'
    });

    return { success: true };
  }

  /**
   * Create batches from array
   */
  private createBatches<T>(array: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Rollback migration for a specific commit
   */
  async rollbackCommitMigration(commitId: string, userId: string): Promise<boolean> {
    try {

      // Delete scheduler jobs
      const deleteResult = await this.schedulerService.deleteCommitScheduling(commitId, userId);
      
      // Remove scheduling fields from commit document
      await this.db.collection('commits').doc(commitId).update({
        'scheduling.enabled': false,
        'scheduling.rolledBackAt': admin.firestore.FieldValue.serverTimestamp()
      });

      return true;

    } catch (error) {
      console.error(`❌ Rollback failed for commit ${commitId}:`, error);
      return false;
    }
  }

  /**
   * Validate migration results
   */
  async validateMigration(commitIds: string[]): Promise<{
    valid: number;
    invalid: number;
    issues: string[];
  }> {
    const results = {
      valid: 0,
      invalid: 0,
      issues: []
    };

    for (const commitId of commitIds) {
      try {
        const commit = await this.db.collection('commits').doc(commitId).get();
        const commitData = commit.data();

        if (!commitData) {
          results.invalid++;
          results.issues.push(`Commit ${commitId} not found`);
          continue;
        }

        // Check if scheduling is properly configured
        if (!commitData.scheduling?.enabled) {
          results.invalid++;
          results.issues.push(`Commit ${commitId} scheduling not enabled`);
          continue;
        }

        // Check if scheduler jobs exist (would need to query Cloud Scheduler API)
        // For now, assume valid if scheduling.enabled is true
        results.valid++;

      } catch (error) {
        results.invalid++;
        results.issues.push(`Validation failed for commit ${commitId}: ${error}`);
      }
    }

    return results;
  }
}

// Export migration instance
export const commitSchedulingMigration = new CommitSchedulingMigration();

// Default migration options
export const DEFAULT_MIGRATION_OPTIONS: MigrationOptions = {
  dryRun: false,
  batchSize: 50,
  maxConcurrency: 5,
  skipInactiveCommits: true,
  enableScheduling: true
};
