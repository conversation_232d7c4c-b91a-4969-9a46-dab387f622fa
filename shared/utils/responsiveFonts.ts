/**
 * Responsive font sizing utilities
 * Provides consistent font scaling across different devices and screen sizes
 */

import React from 'react';
import { Dimensions, Platform } from 'react-native';

// Base screen width for scaling calculations (iPhone 6/7/8 width)
const BASE_WIDTH = 375;

// Web container max width (matches _layout.tsx)
const WEB_MAX_WIDTH = 480;

/**
 * Calculate responsive font size based on screen dimensions
 * @param baseFontSize - The base font size for the reference device
 * @param minScale - Minimum scale factor (default: 0.8)
 * @param maxScale - Maximum scale factor (default: 1.3)
 * @returns Responsive font size
 */
export const getResponsiveFontSize = (
  baseFontSize: number,
  minScale: number = 0.8,
  maxScale: number = 1.3
): number => {
  const { width } = Dimensions.get('window');
  
  if (Platform.OS === 'web') {
    // Web: Use fixed size based on container max width
    return baseFontSize;
  }
  
  // Mobile: Scale based on screen width
  const scaleFactor = width / BASE_WIDTH;
  
  // Clamp the scale factor to prevent too small or too large text
  const clampedScale = Math.max(minScale, Math.min(scaleFactor, maxScale));
  
  return Math.round(baseFontSize * clampedScale);
};

/**
 * Predefined responsive font sizes for common use cases
 */
export const ResponsiveFontSizes = {
  // Header sizes - much more conservative scaling
  headerLarge: () => getResponsiveFontSize(24, 0.9, 1.1),
  headerMedium: () => getResponsiveFontSize(20, 0.9, 1.1),
  headerSmall: () => getResponsiveFontSize(18, 0.9, 1.1),

  // Page title sizes
  pageTitle: () => getResponsiveFontSize(18, 0.9, 1.1),
  sectionTitle: () => getResponsiveFontSize(16, 0.9, 1.05),
  
  // Body text sizes
  bodyLarge: () => getResponsiveFontSize(16, 0.9, 1.05),
  bodyMedium: () => getResponsiveFontSize(14, 0.9, 1.05),
  bodySmall: () => getResponsiveFontSize(12, 0.9, 1.05),

  // Button text sizes
  buttonLarge: () => getResponsiveFontSize(18, 0.9, 1.05),
  buttonMedium: () => getResponsiveFontSize(16, 0.9, 1.05),
  buttonSmall: () => getResponsiveFontSize(14, 0.9, 1.05),
};

/**
 * Hook for responsive font sizing with dimension change listener
 * @param getFontSize - Function that returns the desired font size
 * @returns Current responsive font size
 */
export const useResponsiveFontSize = (getFontSize: () => number) => {
  const [fontSize, setFontSize] = React.useState(getFontSize());

  React.useEffect(() => {
    const subscription = Dimensions.addEventListener('change', () => {
      setFontSize(getFontSize());
    });

    return () => subscription?.remove();
  }, [getFontSize]);

  return fontSize;
};

/**
 * Get responsive spacing based on screen dimensions
 * @param baseSpacing - The base spacing value
 * @param minScale - Minimum scale factor (default: 0.7)
 * @param maxScale - Maximum scale factor (default: 1.2)
 * @returns Responsive spacing value
 */
export const getResponsiveSpacing = (
  baseSpacing: number,
  minScale: number = 0.7,
  maxScale: number = 1.2
): number => {
  const { width, height } = Dimensions.get('window');

  if (Platform.OS === 'web') {
    // Web: Use fixed spacing based on container max width
    return baseSpacing;
  }

  // Mobile: Scale based on screen dimensions
  const screenArea = width * height;
  const baseArea = BASE_WIDTH * 667; // iPhone 6/7/8 area
  const scaleFactor = Math.sqrt(screenArea / baseArea);

  // Clamp the scale factor
  const clampedScale = Math.max(minScale, Math.min(scaleFactor, maxScale));

  return Math.round(baseSpacing * clampedScale);
};

/**
 * Get responsive dimensions for sign-in page layout
 * @returns Object with responsive dimensions
 */
export const getSignInResponsiveDimensions = () => {
  const { width, height } = Dimensions.get('window');

  // Determine if it's a small screen (phones), medium (tablets), or large (desktop)
  const isSmallScreen = width < 480;
  const isMediumScreen = width >= 480 && width < 768;
  const isLargeScreen = width >= 768;
  const isVeryTallScreen = height > 800;
  const isShortScreen = height < 600;

  return {
    // Screen size flags
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isVeryTallScreen,
    isShortScreen,

    // Header dimensions - more compact for better centering
    headerPaddingTop: Platform.OS === 'ios'
      ? (isShortScreen ? 40 : isVeryTallScreen ? 80 : 60)
      : (isShortScreen ? 30 : isVeryTallScreen ? 60 : 40),
    headerPaddingBottom: isShortScreen ? 20 : isVeryTallScreen ? 40 : 30,
    headerPaddingHorizontal: getResponsiveSpacing(24, 0.8, 1.1),

    // Logo dimensions
    logoWidth: isSmallScreen ? 100 : isMediumScreen ? 120 : 140,
    logoHeight: isSmallScreen ? 67 : isMediumScreen ? 80 : 93,

    // Form dimensions
    formMarginHorizontal: getResponsiveSpacing(24, 0.8, 1.1),
    formMarginBottom: getResponsiveSpacing(30, 0.8, 1.1),

    // Font sizes
    appTitleSize: getResponsiveFontSize(24, 0.85, 1.15),
    welcomeTextSize: getResponsiveFontSize(18, 0.85, 1.1),
    subtitleSize: getResponsiveFontSize(14, 0.85, 1.1),
    formTitleSize: getResponsiveFontSize(18, 0.85, 1.1),
    linkTextSize: getResponsiveFontSize(14, 0.85, 1.1),

    // Spacing
    contentGap: getResponsiveSpacing(12, 0.8, 1.1),
    inputGap: getResponsiveSpacing(10, 0.8, 1.1),
    buttonMarginTop: getResponsiveSpacing(20, 0.8, 1.1),
  };
};
