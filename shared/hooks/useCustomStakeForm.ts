import { useState, useEffect, useCallback, useRef } from 'react';
import { CustomStakeData, LocationData, ProgramPreferences, ReminderChannel, StrictnessLevel, NotificationTiming } from "../types/customStake";

const initialFormData: CustomStakeData = {
  commitment: '',
  evidenceType: 'photo',
  reportingFrequency: 'daily',
  timingType: 'mid-night',
  recipient: 'accustom',
  amountPerReport: 10,
  referee: 'honor',
  programPreferences: {
    motivationalNote: '',
    reminderChannels: [],
    strictnessLevel: 'reasonable',
    notificationTiming: {
      type: 'hours-before',
      hoursBeforeDeadline: 2,
    },
    prioritySupport: false,
    advancedAnalytics: false,
    addToCalendar: false,
  },
};

export const useCustomStakeForm = () => {
  const [formData, setFormData] = useState<CustomStakeData>(() => initialFormData);

  // All recipients now require money at stake, so no special handling needed

  // Reset timing when switching to weekly/monthly frequencies
  // Use a ref to prevent unnecessary re-renders during initial load
  const lastFrequency = useRef(formData.reportingFrequency);
  useEffect(() => {
    if (lastFrequency.current !== formData.reportingFrequency) {
      lastFrequency.current = formData.reportingFrequency;

      if (formData.reportingFrequency === 'weekly' || formData.reportingFrequency === 'monthly') {
        setFormData(prev => ({
          ...prev,
          timingType: 'mid-night', // Reset to default
          beforeTime: undefined,
          afterTime: undefined,
          startTime: undefined,
          endTime: undefined
        }));
      }
    }
  }, [formData.reportingFrequency]);

  const updateFormData = useCallback((updates: Partial<CustomStakeData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const updateCommitment = useCallback((commitment: string) => {
    setFormData(prev => ({ ...prev, commitment }));
  }, []);

  const updateEvidenceType = useCallback((evidenceType: CustomStakeData['evidenceType']) => {
    setFormData(prev => ({ ...prev, evidenceType }));
  }, []);

  const updateReportingFrequency = useCallback((reportingFrequency: CustomStakeData['reportingFrequency']) => {
    setFormData(prev => ({ ...prev, reportingFrequency }));
  }, []);

  const updateRecipient = useCallback((recipient: CustomStakeData['recipient']) => {
    setFormData(prev => ({ ...prev, recipient }));
  }, []);

  const updateTimingType = useCallback((timingType: CustomStakeData['timingType']) => {
    setFormData(prev => ({ ...prev, timingType }));
  }, []);

  const updateAmount = useCallback((amountPerReport: number) => {
    setFormData(prev => ({ ...prev, amountPerReport }));
  }, []);

  const updateStartDate = useCallback((startDate: Date) => {
    setFormData(prev => ({ ...prev, startDate }));
  }, []);

  const updateEndDate = useCallback((endDate: Date) => {
    setFormData(prev => ({ ...prev, endDate }));
  }, []);

  const updateWeekLength = useCallback((weekLength: number) => {
    setFormData(prev => ({ ...prev, weekLength }));
  }, []);

  const updateTimesPerWeek = useCallback((timesPerWeek: number) => {
    setFormData(prev => ({ ...prev, timesPerWeek }));
  }, []);

  const updateTimesPerMonth = useCallback((timesPerMonth: number) => {
    setFormData(prev => ({ ...prev, timesPerMonth }));
  }, []);

  const updateLocationData = useCallback((locationData: LocationData) => {
    setFormData(prev => ({
      ...prev,
      locationData,
      location: locationData.title // Keep legacy field for backward compatibility
    }));
  }, []);

  const updateAppName = useCallback((appName: string) => {
    setFormData(prev => ({ ...prev, appName }));
  }, []);

  const updateStravaActivity = useCallback((stravaActivity: string) => {
    setFormData(prev => ({ ...prev, stravaActivity }));
  }, []);

  const updateTiming = useCallback((timeType: 'before' | 'after' | 'start' | 'end', time: string) => {
    const updates: Partial<CustomStakeData> = {};
    switch (timeType) {
      case 'before':
        updates.beforeTime = time;
        break;
      case 'after':
        updates.afterTime = time;
        break;
      case 'start':
        updates.startTime = time;
        break;
      case 'end':
        updates.endTime = time;
        break;
    }
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const updateProgramPreferences = useCallback((updates: Partial<ProgramPreferences>) => {
    setFormData(prev => ({
      ...prev,
      programPreferences: {
        ...prev.programPreferences!,
        ...updates,
      }
    }));
  }, []);

  const updateMotivationalNote = useCallback((motivationalNote: string) => {
    updateProgramPreferences({ motivationalNote });
  }, [updateProgramPreferences]);

  const updateReminderChannels = useCallback((reminderChannels: ReminderChannel[]) => {
    updateProgramPreferences({ reminderChannels });
  }, [updateProgramPreferences]);

  const updateStrictnessLevel = useCallback((strictnessLevel: StrictnessLevel) => {
    updateProgramPreferences({ strictnessLevel });
  }, [updateProgramPreferences]);

  const updateNotificationTiming = useCallback((notificationTiming: NotificationTiming) => {
    updateProgramPreferences({ notificationTiming });
  }, [updateProgramPreferences]);

  const resetForm = useCallback(() => {
    setFormData(initialFormData);
  }, []);

  return {
    formData,
    updateFormData,
    updateCommitment,
    updateEvidenceType,
    updateReportingFrequency,
    updateRecipient,
    updateTimingType,
    updateAmount,
    updateStartDate,
    updateEndDate,
    updateWeekLength,
    updateTimesPerWeek,
    updateTimesPerMonth,
    updateLocationData,
    updateAppName,
    updateStravaActivity,
    updateTiming,
    updateProgramPreferences,
    updateMotivationalNote,
    updateReminderChannels,
    updateStrictnessLevel,
    updateNotificationTiming,
    resetForm,
  };
};
