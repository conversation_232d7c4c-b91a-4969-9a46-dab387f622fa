/**
 * Responsive hook specifically for the Sign-In page
 * Provides responsive dimensions, spacing, and font sizes
 */

import { useState, useEffect } from 'react';
import { Dimensions, Platform } from 'react-native';
import { getSignInResponsiveDimensions } from '../utils/responsiveFonts';

export interface SignInResponsiveDimensions {
  // Screen size flags
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
  isVeryTallScreen: boolean;
  isShortScreen: boolean;
  
  // Header dimensions
  headerPaddingTop: number;
  headerPaddingBottom: number;
  headerPaddingHorizontal: number;
  
  // Logo dimensions
  logoWidth: number;
  logoHeight: number;
  
  // Form dimensions
  formMarginHorizontal: number;
  formMarginBottom: number;
  
  // Font sizes
  appTitleSize: number;
  welcomeTextSize: number;
  subtitleSize: number;
  formTitleSize: number;
  linkTextSize: number;
  
  // Spacing
  contentGap: number;
  inputGap: number;
  buttonMarginTop: number;
}

/**
 * Hook that provides responsive dimensions for the Sign-In page
 * Automatically updates when screen dimensions change
 */
export const useSignInResponsive = (): SignInResponsiveDimensions => {
  const [dimensions, setDimensions] = useState<SignInResponsiveDimensions>(() => 
    getSignInResponsiveDimensions()
  );

  useEffect(() => {
    if (Platform.OS === 'web') {
      // Handle window resize on web with debouncing for performance
      let timeoutId: ReturnType<typeof setTimeout>;

      const handleResize = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setDimensions(getSignInResponsiveDimensions());
        }, 100); // Debounce resize events
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
        clearTimeout(timeoutId);
      };
    } else {
      // Handle orientation changes on mobile
      const subscription = Dimensions.addEventListener('change', () => {
        setDimensions(getSignInResponsiveDimensions());
      });

      return () => subscription?.remove();
    }
  }, []);

  return dimensions;
};

/**
 * Get responsive styles for the Sign-In page components
 */
export const getSignInResponsiveStyles = (dimensions: SignInResponsiveDimensions) => {
  return {
    // Header section styles
    headerSection: {
      alignItems: 'center' as const,
      paddingTop: dimensions.headerPaddingTop,
      paddingBottom: dimensions.headerPaddingBottom,
      paddingHorizontal: dimensions.headerPaddingHorizontal,
      // Don't take up too much space on short screens
      flexShrink: dimensions.isShortScreen ? 1 : 0,
    },
    
    // Logo styles
    logoImage: {
      width: dimensions.logoWidth,
      height: dimensions.logoHeight,
    },
    
    // App title styles
    appTitle: {
      fontSize: dimensions.appTitleSize,
      fontFamily: 'MontserratBold',
      textAlign: 'center' as const,
      letterSpacing: 2,
      marginTop: 8,
    },
    
    // Welcome text styles
    welcomeText: {
      fontSize: dimensions.welcomeTextSize,
      fontFamily: 'MontserratMedium',
      textAlign: 'center' as const,
      letterSpacing: 0.5,
    },
    
    // Subtitle styles
    subtitle: {
      fontSize: dimensions.subtitleSize,
      fontFamily: 'MontserratRegular',
      textAlign: 'center' as const,
      letterSpacing: 0.3,
      lineHeight: dimensions.subtitleSize * 1.4,
      opacity: 0.8,
    },
    
    // Form container styles
    formContainer: {
      marginHorizontal: dimensions.formMarginHorizontal,
      marginVertical: dimensions.isShortScreen ? 10 : 20,
      // Remove bottom margin since we're centering
    },
    
    // Form content styles
    formContent: {
      gap: dimensions.contentGap,
    },
    
    // Form title styles
    formTitle: {
      fontSize: dimensions.formTitleSize,
      fontFamily: 'MontserratBold',
      textAlign: 'center' as const,
      marginBottom: 6,
      letterSpacing: 0.5,
    },
    
    // Input container styles
    inputContainer: {
      gap: dimensions.inputGap,
    },
    
    // Primary button styles
    primaryButton: {
      marginTop: dimensions.buttonMarginTop,
      shadowColor: '#FFD700',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    
    // Link text styles
    linkText: {
      fontSize: dimensions.linkTextSize,
      fontFamily: 'MontserratMedium',
    },
    
    // Navigation container styles
    navigationContainer: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
      marginTop: dimensions.contentGap,
      paddingHorizontal: 0,
      // Adjust layout for small screens
      ...(dimensions.isSmallScreen && {
        flexDirection: 'column' as const,
        gap: dimensions.contentGap / 2,
        alignItems: 'stretch' as const,
      }),
    },
    
    // Link button styles for small screens
    linkButton: {
      paddingVertical: 8,
      paddingHorizontal: 12,
      ...(dimensions.isSmallScreen && {
        alignItems: 'center' as const,
        alignSelf: 'stretch' as const,
      }),
    },
    
    // Spacer styles - reduced to center the form better
    spacer: {
      flex: dimensions.isShortScreen ? 0.1 : dimensions.isVeryTallScreen ? 0.3 : 0.2,
      minHeight: dimensions.isShortScreen ? 10 : 20,
    },

    // Container styles for centering
    mainContainer: {
      flex: 1,
      justifyContent: 'center' as const,
    },

    // Content wrapper for better centering
    contentWrapper: {
      flex: 1,
      justifyContent: dimensions.isShortScreen ? 'flex-start' as const : 'center' as const,
      paddingVertical: dimensions.isShortScreen ? 5 : 10,
    },
  };
};
