import { useMemo } from 'react';
import { CustomStakeData, ValidationError, FormValidationResult } from "../types/customStake";

export const useCustomStakeValidation = (formData: CustomStakeData): FormValidationResult => {
  return useMemo(() => {
    // Early return for empty/initial form data to improve performance
    if (!formData || (!formData.commitment && !formData.evidenceType)) {
      return { isValid: false, errors: [] };
    }

    const errors: ValidationError[] = [];

    // Basic validation
    if (!formData.evidenceType) {
      errors.push({ field: 'evidenceType', message: 'Please select an evidence type' });
    }

    if (!formData.commitment.trim()) {
      errors.push({ field: 'commitment', message: 'Please enter your commitment' });
    }

    if (formData.commitment.length > 500) {
      errors.push({ field: 'commitment', message: 'Commitment must be 500 characters or less' });
    }

    // Date validation based on reporting frequency
    if (formData.reportingFrequency === 'once' && !formData.endDate) {
      errors.push({ field: 'endDate', message: 'Please select an end date for your commitment' });
    }

    if (formData.reportingFrequency === 'daily') {
      if (!formData.startDate || !formData.endDate) {
        errors.push({ field: 'dates', message: 'Please select both start and end dates' });
      }
    }

    if (formData.reportingFrequency === 'weekly' || formData.reportingFrequency === 'monthly') {
      if (!formData.startDate) {
        errors.push({ field: 'startDate', message: 'Please select a start date' });
      }
      if (!formData.weekLength || formData.weekLength <= 0) {
        errors.push({ field: 'weekLength', message: 'Please enter a valid length for your commitment' });
      }
      if (formData.reportingFrequency === 'weekly' && (!formData.timesPerWeek || formData.timesPerWeek <= 0)) {
        errors.push({ field: 'timesPerWeek', message: 'Please select how many times per week you want to report' });
      }
      if (formData.reportingFrequency === 'monthly' && (!formData.timesPerMonth || formData.timesPerMonth <= 0)) {
        errors.push({ field: 'timesPerMonth', message: 'Please select how many times per month you want to report' });
      }
    }

    // Evidence-specific validation
    if (formData.evidenceType === 'gps-checkin' &&
        !formData.locationData && !formData.location?.trim()) {
      errors.push({ field: 'location', message: 'Please select a location for GPS evidence' });
    }

    if (formData.evidenceType === 'screen-time' && !formData.appName?.trim()) {
      errors.push({ field: 'appName', message: 'Please enter an app name for screen time evidence' });
    }

    // Note: 'strava' is not in the current EvidenceType union, so this check is removed

    // Timing validation - only for daily and once frequencies
    if (formData.reportingFrequency === 'daily' || formData.reportingFrequency === 'once') {
      if (formData.timingType === 'before' && !formData.beforeTime?.trim()) {
        errors.push({ field: 'beforeTime', message: 'Please enter a time for "before" timing' });
      }

      if (formData.timingType === 'after' && !formData.afterTime?.trim()) {
        errors.push({ field: 'afterTime', message: 'Please enter a time for "after" timing' });
      }

      if (formData.timingType === 'between' && (!formData.startTime?.trim() || !formData.endTime?.trim())) {
        errors.push({ field: 'timingBetween', message: 'Please enter both start and end times for "between" timing' });
      }

      // Validate time format (HH:MM)
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (formData.timingType === 'before' && formData.beforeTime && !timeRegex.test(formData.beforeTime)) {
        errors.push({ field: 'beforeTime', message: 'Please enter time in HH:MM format (e.g., 14:30)' });
      }

      if (formData.timingType === 'after' && formData.afterTime && !timeRegex.test(formData.afterTime)) {
        errors.push({ field: 'afterTime', message: 'Please enter time in HH:MM format (e.g., 14:30)' });
      }

      if (formData.timingType === 'between') {
        if (formData.startTime && !timeRegex.test(formData.startTime)) {
          errors.push({ field: 'startTime', message: 'Please enter start time in HH:MM format (e.g., 14:30)' });
        }
        if (formData.endTime && !timeRegex.test(formData.endTime)) {
          errors.push({ field: 'endTime', message: 'Please enter end time in HH:MM format (e.g., 14:30)' });
        }
      }
    }

    // Amount validation - all recipients now require money at stake
    if (formData.amountPerReport <= 0) {
      errors.push({ field: 'amountPerReport', message: 'Please enter a valid amount at stake' });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, [formData]);
};

export const useStakeCalculation = (formData: CustomStakeData): number => {
  return useMemo(() => {
    // Early return for empty/initial form data to improve performance
    if (!formData || !formData.recipient) return 0;

    // All recipients now require money at stake

    if (formData.reportingFrequency === 'once') return formData.amountPerReport;

    let multiplier = 1;

    if (formData.reportingFrequency === 'daily') {
      // Calculate actual days between start and end date
      if (formData.startDate && formData.endDate) {
        const timeDiff = formData.endDate.getTime() - formData.startDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end days
        multiplier = daysDiff;
      } else {
        multiplier = 28; // Default fallback
      }
    } else if (formData.reportingFrequency === 'weekly') {
      const weeks = formData.weekLength || 4;
      const timesPerWeek = formData.timesPerWeek || 1;
      multiplier = weeks * timesPerWeek;
    } else if (formData.reportingFrequency === 'monthly') {
      const months = formData.weekLength || 1; // weekLength represents months for monthly frequency
      const timesPerMonth = formData.timesPerMonth || 1;
      multiplier = months * timesPerMonth;
    }

    return formData.amountPerReport * multiplier;
  }, [formData]);
};
