// Enhanced Payment System Types for Dual Payment Streams
// Supports Stripe for commits/pools and platform-specific payments for premium/grace days

// Import payment types and providers from config to avoid circular dependencies
export { PaymentType, PaymentProvider, IN_APP_PRODUCTS } from '../../lib/config/payments';

// Base Payment Data Interface
export interface BasePaymentData {
  amount: number; // Amount in cents
  currency: string;
  description: string;
  userId: string;
  paymentType: PaymentType;
}

// Stripe-specific Payment Data (for commits and pools)
export interface StripePaymentData extends BasePaymentData {
  programId: string;
  paymentType: PaymentType.COMMIT | PaymentType.POOL;
}

// In-App Purchase Data (for premium subscriptions and grace days)
export interface InAppPurchaseData extends BasePaymentData {
  productId: string;
  paymentType: PaymentType.PREMIUM_SUBSCRIPTION | PaymentType.GRACE_DAY;
  programId?: string; // Optional for grace days
  quantity?: number; // For grace day purchases
}

// Union type for all payment data
export type PaymentData = StripePaymentData | InAppPurchaseData;

// Legacy interface for backward compatibility
export interface LegacyPaymentData {
  amount: number;
  currency: string;
  description: string;
  programId: string;
  userId: string;
}

// Payment Result Interfaces
export interface BasePaymentResult {
  success: boolean;
  error?: string;
  provider: PaymentProvider;
}

export interface StripePaymentResult extends BasePaymentResult {
  provider: PaymentProvider.STRIPE;
  paymentIntentId?: string;
}

export interface InAppPurchaseResult extends BasePaymentResult {
  provider: PaymentProvider.GOOGLE_PLAY | PaymentProvider.APPLE_APP_STORE;
  transactionId?: string;
  purchaseToken?: string; // Google Play
  receipt?: string; // Apple App Store
  originalTransactionId?: string; // For subscriptions
}

// Union type for all payment results
export type PaymentResult = StripePaymentResult | InAppPurchaseResult;

// Legacy interface for backward compatibility
export interface LegacyPaymentResult {
  success: boolean;
  paymentIntentId?: string;
  error?: string;
}

// Subscription-specific interfaces
export interface SubscriptionPurchaseData extends InAppPurchaseData {
  paymentType: PaymentType.PREMIUM_SUBSCRIPTION;
  subscriptionPlan: 'monthly' | 'yearly';
}

export interface GraceDayPurchaseData extends InAppPurchaseData {
  paymentType: PaymentType.GRACE_DAY;
  programId: string;
  quantity: number;
}

// Product Configuration for In-App Purchases
export interface InAppProduct {
  id: string;
  type: 'subscription' | 'consumable';
  price: number;
  currency: string;
  title: string;
  description: string;
  platform: 'ios' | 'android' | 'both';
}







// Type guards for payment data
export function isStripePaymentData(data: PaymentData): data is StripePaymentData {
  return data.paymentType === PaymentType.COMMIT || data.paymentType === PaymentType.POOL;
}

export function isInAppPurchaseData(data: PaymentData): data is InAppPurchaseData {
  return data.paymentType === PaymentType.PREMIUM_SUBSCRIPTION || data.paymentType === PaymentType.GRACE_DAY;
}

export function isSubscriptionPurchaseData(data: PaymentData): data is SubscriptionPurchaseData {
  return data.paymentType === PaymentType.PREMIUM_SUBSCRIPTION;
}

export function isGraceDayPurchaseData(data: PaymentData): data is GraceDayPurchaseData {
  return data.paymentType === PaymentType.GRACE_DAY;
}

// Type guards for payment results
export function isStripePaymentResult(result: PaymentResult): result is StripePaymentResult {
  return result.provider === PaymentProvider.STRIPE;
}

export function isInAppPurchaseResult(result: PaymentResult): result is InAppPurchaseResult {
  return result.provider === PaymentProvider.GOOGLE_PLAY || result.provider === PaymentProvider.APPLE_APP_STORE;
}

// Helper function to convert legacy payment data to new format
export function convertLegacyPaymentData(
  legacyData: LegacyPaymentData,
  paymentType: PaymentType.COMMIT | PaymentType.POOL
): StripePaymentData {
  return {
    ...legacyData,
    paymentType,
  };
}

// Helper function to convert new payment result to legacy format
export function convertToLegacyPaymentResult(result: PaymentResult): LegacyPaymentResult {
  if (isStripePaymentResult(result)) {
    return {
      success: result.success,
      paymentIntentId: result.paymentIntentId,
      error: result.error,
    };
  } else {
    return {
      success: result.success,
      paymentIntentId: result.transactionId, // Map transaction ID to payment intent ID for compatibility
      error: result.error,
    };
  }
}
