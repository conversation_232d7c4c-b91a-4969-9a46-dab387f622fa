import React from 'react';
import { View, ViewStyle, StyleSheet } from 'react-native';
import { BlurView } from 'expo-blur';
import { useTheme } from "../../contexts/ThemeContext";

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: number;
  borderRadius?: number;
  padding?: number;
  variant?: 'primary' | 'secondary';
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  intensity = 40,
  borderRadius,
  padding,
  variant = 'primary'
}) => {
  const { colors, isDark, designSystem } = useTheme();

  // Use design system defaults if not provided
  const cardBorderRadius = borderRadius ?? designSystem.borderRadius.xl;
  const cardPadding = padding ?? designSystem.spacing.xxl;

  const cardBackground = variant === 'primary'
    ? colors.glassBackground
    : colors.glassBackgroundSecondary;

  return (
    <View style={[
      styles.container,
      designSystem.shadows.md,
      {
        borderRadius: cardBorderRadius,
        backgroundColor: cardBackground,
        borderColor: colors.glassBorder,
        shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
      },
      style
    ]}>
      <BlurView
        intensity={intensity}
        tint="dark"
        style={[
          styles.blurContainer,
          {
            borderRadius: cardBorderRadius,
            padding: cardPadding,
          }
        ]}
      >
        {children}
      </BlurView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    // Shadow styles are now applied via designSystem.shadows.md in component
  },
  blurContainer: {
    overflow: 'hidden',
    backgroundColor: 'rgba(0, 0, 0, 0.20)', // Black tinted background for better contrast
  },
});
