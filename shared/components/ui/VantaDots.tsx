import React, { useRef, useEffect, useState, useCallback } from 'react';
import { StyleSheet, View, Animated, AppState, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import { useFocusEffect } from 'expo-router';

interface VantaDotsBackgroundProps {
  screenKey?: string;
}

export const VantaDotsBackground: React.FC<VantaDotsBackgroundProps> = ({ screenKey = 'default' }) => {
  const webViewRef = useRef<WebView>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [webViewKey, setWebViewKey] = useState(`vanta-dots-${screenKey}-${Date.now()}`);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const retryTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const loadTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const mountTimeRef = useRef(Date.now());
  const isUpdatingRef = useRef(false); // Prevent concurrent updates

  // Handle app state changes for iOS
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active' && shouldRender) {
        // Restart animation when app becomes active
        setTimeout(() => {
          if (webViewRef.current) {
            webViewRef.current.injectJavaScript(`
              if (typeof restartAnimation === 'function') {
                restartAnimation();
              }
              true;
            `);
          }
        }, 100);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [shouldRender]);

  // Handle screen focus for navigation - avoid resetting state unnecessarily
  useFocusEffect(
    useCallback(() => {
      setIsVisible(true);

      // Only reset if we're not already rendering and loaded
      if (!shouldRender) {
        setShouldRender(true);
      }

      // Don't reset WebView key or loaded state on every focus to avoid swap
      // Only generate new key if there was an actual error or first load

      return () => {
        setIsVisible(false);

        // Clear any pending timeouts
        if (retryTimeoutRef.current) {
          clearTimeout(retryTimeoutRef.current);
          retryTimeoutRef.current = null;
        }
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current);
          loadTimeoutRef.current = null;
        }
      };
    }, [screenKey, shouldRender])
  );

  // Initial render setup - start rendering immediately but wait for load
  useEffect(() => {
    setShouldRender(true);
    // Don't set isLoaded immediately - let the WebView load process handle it
  }, []); // Empty dependency array - only run once

  // Retry mechanism for failed loads
  const retryLoad = useCallback(() => {
    if (isUpdatingRef.current) return; // Prevent concurrent updates

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }

    retryTimeoutRef.current = setTimeout(() => {
      if (!isLoaded && isVisible && !isUpdatingRef.current) {
        isUpdatingRef.current = true;
        // Generate new key to force WebView recreation
        setWebViewKey(`vanta-dots-${screenKey}-${Date.now()}`);
        setIsLoaded(false);
        // Reset flag after state update
        setTimeout(() => {
          isUpdatingRef.current = false;
        }, 100);
      }
    }, 1500);
  }, [isLoaded, screenKey, isVisible]);

  // Fade in animation when loaded
  useEffect(() => {
    if (isLoaded) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    } else {
      // Reset opacity when not loaded to prepare for fade-in
      fadeAnim.setValue(0);
    }
  }, [isLoaded, fadeAnim]);

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, []);

  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            background: transparent !important;
            background-color: transparent !important;
        }
        #vanta-bg {
            width: 100%;
            height: 100vh;
        }
        canvas {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
        }
    </style>
</head>
<body>
    <div id="vanta-bg"></div>

    <script>
    // Simple dots animation without external dependencies
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    document.getElementById('vanta-bg').appendChild(canvas);

    let width = window.innerWidth;
    let height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;

    const dots = [];
    const numDots = 100;
    let mouseX = width / 2;
    let mouseY = height / 2;

    // Create dots
    for (let i = 0; i < numDots; i++) {
      dots.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1
      });
    }

    function animate() {
      ctx.clearRect(0, 0, width, height);

      dots.forEach(dot => {
        // Move towards mouse
        const dx = mouseX - dot.x;
        const dy = mouseY - dot.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          dot.vx += dx * 0.0001;
          dot.vy += dy * 0.0001;
        }

        dot.x += dot.vx;
        dot.y += dot.vy;

        // Bounce off edges
        if (dot.x < 0 || dot.x > width) dot.vx *= -1;
        if (dot.y < 0 || dot.y > height) dot.vy *= -1;

        // Draw dot
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(dot.x, dot.y, dot.size, 0, Math.PI * 2);
        ctx.fill();
      });

      requestAnimationFrame(animate);
    }

    // Mouse tracking
    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    // Touch tracking
    document.addEventListener('touchmove', (e) => {
      if (e.touches.length > 0) {
        mouseX = e.touches[0].clientX;
        mouseY = e.touches[0].clientY;
      }
    });

    // Resize handler
    window.addEventListener('resize', () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
    });

    let animationId;
    let isAnimating = true;

    function startAnimation() {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      isAnimating = true;
      animate();
    }

    function stopAnimation() {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      isAnimating = false;
    }

    // Global restart function for React Native to call
    window.restartAnimation = function() {
      startAnimation();
    };

    // Modified animate function to use animationId
    function animate() {
      if (!isAnimating) return;

      ctx.clearRect(0, 0, width, height);

      dots.forEach(dot => {
        // Move towards mouse
        const dx = mouseX - dot.x;
        const dy = mouseY - dot.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          dot.vx += dx * 0.0001;
          dot.vy += dy * 0.0001;
        }

        dot.x += dot.vx;
        dot.y += dot.vy;

        // Bounce off edges
        if (dot.x < 0 || dot.x > width) dot.vx *= -1;
        if (dot.y < 0 || dot.y > height) dot.vy *= -1;

        // Draw dot
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(dot.x, dot.y, dot.size, 0, Math.PI * 2);
        ctx.fill();
      });

      animationId = requestAnimationFrame(animate);
    }

    // Start initial animation
    startAnimation();

    // Handle visibility changes for iOS
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        stopAnimation();
      } else {
        setTimeout(() => startAnimation(), 100);
      }
    });

    // Handle page focus/blur
    window.addEventListener('focus', () => {
      setTimeout(() => startAnimation(), 100);
    });

    window.addEventListener('blur', () => {
      // Don't stop immediately, iOS might just be switching apps briefly
      setTimeout(() => {
        if (document.hidden) {
          stopAnimation();
        }
      }, 1000);
    });

    // Send a message to React Native to confirm animation started
    setTimeout(() => {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage('VantaDots animation initialized');
      }
    }, 500);
    </script>
</body>
</html>
  `;

  return (
    <View style={styles.container}>
      {shouldRender && (
        <Animated.View style={[styles.webviewContainer, { opacity: fadeAnim }]}>
          <WebView
            key={webViewKey}
            ref={webViewRef}
            source={{ html: htmlContent }}
            style={styles.webview}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={false}
            pointerEvents="none"
            originWhitelist={['*']}
            mixedContentMode="compatibility"
            allowsInlineMediaPlayback={true}
            mediaPlaybackRequiresUserAction={false}
            androidLayerType="hardware"
            cacheEnabled={false}
            incognito={true}
            // iOS specific optimizations
            bounces={false}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            overScrollMode="never"
            // Performance optimizations
            renderToHardwareTextureAndroid={true}
            onMessage={(event) => {
              if (event.nativeEvent.data === 'VantaDots animation initialized') {
                if (loadTimeoutRef.current) {
                  clearTimeout(loadTimeoutRef.current);
                }
                setIsLoaded(true);
              }
            }}
            onError={(syntheticEvent) => {
              console.warn(`VantaDots WebView error for ${screenKey}:`, syntheticEvent.nativeEvent);
              retryLoad();
            }}
            onLoad={() => {
              // Set a timeout to show animation even if message doesn't arrive
              loadTimeoutRef.current = setTimeout(() => {
                if (!isLoaded && isVisible) {
                  setIsLoaded(true);
                }
              }, Platform.OS === 'ios' ? 800 : 600); // Reduced timeout for faster appearance
            }}
            onLoadStart={() => {
              // Only reset loaded state if this is a fresh load, not a navigation
              if (!isLoaded) {
                setIsLoaded(false);
              }
            }}
            onLoadEnd={() => {
              // Additional restart for both platforms
              setTimeout(() => {
                if (webViewRef.current && isVisible) {
                  webViewRef.current.injectJavaScript(`
                    if (typeof restartAnimation === 'function') {
                      restartAnimation();
                    }
                    true;
                  `);
                }
              }, Platform.OS === 'ios' ? 300 : 200);
            }}
            renderError={() => <View style={styles.errorContainer} />}
            // Inject JavaScript to handle iOS-specific issues
            injectedJavaScript={`
              // iOS-specific fixes
              if (typeof window !== 'undefined') {
                // Prevent iOS from pausing animations
                let lastTime = Date.now();
                const keepAlive = setInterval(() => {
                  const now = Date.now();
                  if (now - lastTime > 2000) {
                    // Animation might have paused, restart it
                    if (typeof restartAnimation === 'function') {
                      restartAnimation();
                    }
                  }
                  lastTime = now;
                }, 1000);

                // Handle iOS app lifecycle
                document.addEventListener('webkitvisibilitychange', () => {
                  if (!document.webkitHidden && typeof restartAnimation === 'function') {
                    setTimeout(() => restartAnimation(), 100);
                  }
                });
              }
              true;
            `}
          />
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1, // Behind content
  },
  webviewContainer: {
    flex: 1,
  },
  webview: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});
