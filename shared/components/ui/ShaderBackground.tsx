import React, { useRef, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { useTheme } from "../../contexts/ThemeContext";

export const ShaderBackground: React.FC = () => {
  const { colors, isDark } = useTheme();
  const webViewRef = useRef<WebView>(null);
  const isUpdatingRef = useRef(false); // Prevent concurrent updates

  // Aggressive animation persistence
  useEffect(() => {
    const keepAlive = setInterval(() => {
      // Aggressively restart animation if stopped
      if (webViewRef.current && !isUpdatingRef.current) {
        webViewRef.current.injectJavaScript(`
          // Force animation restart
          if (typeof isRunning !== 'undefined') {
            if (!isRunning) {
              isRunning = true;
              requestAnimationFrame(render);
            }
          }
          true;
        `);
      }
    }, 5000); // Check every 5 seconds

    // Additional backup timer
    const forceRestart = setInterval(() => {
      if (webViewRef.current && !isUpdatingRef.current) {
        webViewRef.current.injectJavaScript(`
          // Force restart regardless of state
          if (typeof render === 'function') {
            isRunning = true;
            requestAnimationFrame(render);
          }
          true;
        `);
      }
    }, 15000); // Force restart every 15 seconds

    return () => {
      clearInterval(keepAlive);
      clearInterval(forceRestart);
    };
  }, []);

  // Adjust shader colors based on theme - Casino gold theme with better contrast
  const shaderColors = isDark
    ? {
        color1: '#FFEB3B', // Bright gold for dark theme
        color2: '#FFD700', // Classic gold
        color3: '#333333', // Medium dark for contrast
        color4: '#1A1A1A'  // Dark background
      }
    : {
        color1: '#D4AF37', // Classic gold primary
        color2: '#F4D03F', // Light gold
        color3: '#FFFFFF', // Pure white
        color4: '#FFF8DC'  // Cornsilk/cream
      };

  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shader Background</title>
    <style>
        body {
            display: flex;
            margin: 0;
            height: 100vh;
            overflow: hidden;
        }

        shader-art {
            display: block;
            width: 100%;
            height: 100%;
        }

        #shaderCanvas {
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
        }
    </style>
</head>
<body>
    <canvas id="shaderCanvas"></canvas>

    <script>
        // Pure WebGL implementation without external dependencies
        const canvas = document.getElementById('shaderCanvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!gl) {
            console.error('WebGL not supported');
        } else {
            // Resize canvas to fill container
            function resizeCanvas() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                gl.viewport(0, 0, canvas.width, canvas.height);
            }

            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();

            // Vertex shader source
            const vertexShaderSource = \`
                attribute vec4 a_position;
                attribute vec2 a_texCoord;
                varying vec2 v_texCoord;
                void main() {
                    gl_Position = a_position;
                    v_texCoord = a_texCoord;
                }
            \`;

            // Fragment shader source with casino colors
            const fragmentShaderSource = \`
                precision mediump float;
                uniform float u_time;
                uniform vec2 u_resolution;
                varying vec2 v_texCoord;

                // Casino theme colors (converted from hex to RGB 0-1 range)
                vec3 color1 = vec3(${parseInt(shaderColors.color1.slice(1, 3), 16) / 255}, ${parseInt(shaderColors.color1.slice(3, 5), 16) / 255}, ${parseInt(shaderColors.color1.slice(5, 7), 16) / 255});
                vec3 color2 = vec3(${parseInt(shaderColors.color2.slice(1, 3), 16) / 255}, ${parseInt(shaderColors.color2.slice(3, 5), 16) / 255}, ${parseInt(shaderColors.color2.slice(5, 7), 16) / 255});
                vec3 color3 = vec3(${parseInt(shaderColors.color3.slice(1, 3), 16) / 255}, ${parseInt(shaderColors.color3.slice(3, 5), 16) / 255}, ${parseInt(shaderColors.color3.slice(5, 7), 16) / 255});
                vec3 color4 = vec3(${parseInt(shaderColors.color4.slice(1, 3), 16) / 255}, ${parseInt(shaderColors.color4.slice(3, 5), 16) / 255}, ${parseInt(shaderColors.color4.slice(5, 7), 16) / 255});

                float noise(vec3 p) {
                    return sin(p.x * 5.0 + cos(p.y * 7.0 + p.z)) *
                           cos(p.y * 9.0 + sin(p.x * 13.0 - p.z));
                }

                void main() {
                    vec2 st = v_texCoord * 0.4;
                    float time = u_time * 0.0001; // Much slower animation

                    vec2 v1 = vec2(noise(vec3(st, 2.0)), noise(vec3(st, 1.0)));
                    vec2 v2 = vec2(
                        noise(vec3(st + v1 * 0.5 + vec2(cos(time) * 0.3, sin(time) * 0.5), time * 0.02)),
                        noise(vec3(st + v1 * 0.5 + vec2(sin(time) * 0.4, cos(time) * 0.3), time * 0.015))
                    );

                    float n = 0.5 + 0.5 * noise(vec3(st + v2 * 0.3, 0.0));

                    vec3 color = mix(color1, color2, clamp((n*n)*4.0, 0.0, 1.0));
                    color = mix(color, color3, clamp(length(v1) * 0.5, 0.0, 1.0));
                    color = mix(color, color4, clamp(length(v2.x) * 0.7, 0.0, 1.0));

                    color /= n*n + n * 3.0 + 0.5; // Better brightness control
                    gl_FragColor = vec4(color, 1.0);
                }
            \`;

            // Create and compile shader
            function createShader(gl, type, source) {
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);
                if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                    console.error('Shader compile error:', gl.getShaderInfoLog(shader));
                    gl.deleteShader(shader);
                    return null;
                }
                return shader;
            }

            // Create program
            const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
            const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);

            if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                console.error('Program link error:', gl.getProgramInfoLog(program));
            }

            // Set up geometry
            const positions = new Float32Array([
                -1, -1, 1, -1, -1, 1,
                -1, 1, 1, -1, 1, 1
            ]);

            const texCoords = new Float32Array([
                0, 0, 1, 0, 0, 1,
                0, 1, 1, 0, 1, 1
            ]);

            const positionBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

            const texCoordBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, texCoords, gl.STATIC_DRAW);

            // Get attribute and uniform locations
            const positionLocation = gl.getAttribLocation(program, 'a_position');
            const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');
            const timeLocation = gl.getUniformLocation(program, 'u_time');
            const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');

            // Animation loop with no FPS limiting for maximum smoothness
            let animationId;
            let isRunning = true;

            function render(currentTime) {
                if (!isRunning) return;

                try {
                    // Check if WebGL context is still valid
                    if (gl.isContextLost()) {
                        setTimeout(() => {
                            if (isRunning) {
                                requestAnimationFrame(render);
                            }
                        }, 100);
                        return;
                    }

                    gl.useProgram(program);

                    // Set uniforms
                    gl.uniform1f(timeLocation, currentTime);
                    gl.uniform2f(resolutionLocation, canvas.width, canvas.height);

                    // Set up attributes
                    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
                    gl.enableVertexAttribArray(positionLocation);
                    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

                    gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
                    gl.enableVertexAttribArray(texCoordLocation);
                    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 0, 0);

                    // Clear and draw
                    gl.clearColor(0.0, 0.0, 0.0, 0.0);
                    gl.clear(gl.COLOR_BUFFER_BIT);
                    gl.drawArrays(gl.TRIANGLES, 0, 6);

                } catch (error) {
                    console.error('WebGL render error:', error);
                }

                // Run at full FPS - no limiting
                if (isRunning) {
                    animationId = requestAnimationFrame(render);
                }
            }

            // Keep animation running even when page visibility changes
            document.addEventListener('visibilitychange', () => {
                // Don't pause animation - keep it running always
                if (!isRunning) {
                    isRunning = true;
                    requestAnimationFrame(render);
                }
            });

            // Handle WebGL context loss
            canvas.addEventListener('webglcontextlost', (event) => {
                event.preventDefault();
                isRunning = false;
            });

            canvas.addEventListener('webglcontextrestored', () => {
                isRunning = true;
                requestAnimationFrame(render);
            });

            // Start animation with multiple fallback mechanisms
            requestAnimationFrame(render);

            // Backup animation restart mechanism
            setInterval(() => {
                if (!isRunning) {
                    isRunning = true;
                    requestAnimationFrame(render);
                }
            }, 1000); // Check every second

            // Additional fallback using setTimeout
            function ensureAnimation() {
                if (isRunning) {
                    setTimeout(ensureAnimation, 16); // ~60fps fallback
                } else {
                    isRunning = true;
                    requestAnimationFrame(render);
                    setTimeout(ensureAnimation, 16);
                }
            }
            ensureAnimation();
        }
    </script>
</body>
</html>
  `;

  return (
    <View style={styles.container}>
      <WebView
        ref={webViewRef}
        source={{ html: htmlContent }}
        style={styles.webview}
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        overScrollMode="never"
        scalesPageToFit={false}
        startInLoadingState={false}
        javaScriptEnabled={true}
        domStorageEnabled={false}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        // Disable user interaction to prevent interfering with UI
        pointerEvents="none"
        // Performance optimizations
        renderToHardwareTextureAndroid={true}
        mixedContentMode="compatibility"
        // Prevent context menu and selection
        onShouldStartLoadWithRequest={() => true}
        // Suppress console logs and errors from shader
        onMessage={() => {}}
        // Prevent WebView from being destroyed
        cacheEnabled={true}
        incognito={false}
        injectedJavaScript={`
          // Suppress console logs for cleaner output
          console.warn = () => {};
          console.error = () => {};

          // Force animation to keep running
          document.addEventListener('visibilitychange', () => {
            // Always restart animation on visibility change
            setTimeout(() => {
              if (typeof isRunning !== 'undefined' && typeof render === 'function') {
                isRunning = true;
                requestAnimationFrame(render);
              }
            }, 100);
          });

          // Window focus events
          window.addEventListener('focus', () => {
            if (typeof isRunning !== 'undefined' && typeof render === 'function') {
              isRunning = true;
              requestAnimationFrame(render);
            }
          });

          window.addEventListener('blur', () => {
            // Don't stop on blur - keep running
            setTimeout(() => {
              if (typeof isRunning !== 'undefined' && typeof render === 'function') {
                isRunning = true;
                requestAnimationFrame(render);
              }
            }, 100);
          });

          true;
        `}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1, // Ensure it stays behind other content
  },
  webview: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});
