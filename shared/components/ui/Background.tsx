import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useTheme } from "../../contexts/ThemeContext";
import { ShaderBackground } from './ShaderBackground';

interface BackgroundProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: number;
  variant?: 'gradient' | 'solid' | 'mesh' | 'shader';
}

export const Background: React.FC<BackgroundProps> = ({
  children,
  style,
  intensity = 15,
  variant = 'gradient'
}) => {
  const { colors, isDark } = useTheme();

  const getBackgroundGradient = (): readonly [string, string, ...string[]] => {
    switch (variant) {
      case 'gradient':
        return isDark
          ? ['#000000', '#0A0A0A', '#000000'] as const
          : ['#F8F9FA', '#FFFFFF', '#F0F2F5'] as const;
      case 'solid':
        return [colors.background, colors.background] as const;
      case 'mesh':
        return isDark
          ? ['#000000', '#0A0A0A', '#050505', '#000000'] as const
          : ['#F8F9FA', '#E8F4FD', '#FFF8E1', '#F8F9FA'] as const;
      case 'shader':
        return ['transparent', 'transparent'] as const; // Transparent for shader background
      default:
        return isDark
          ? ['#000000', '#0A0A0A', '#000000'] as const
          : ['#F8F9FA', '#FFFFFF', '#F0F2F5'] as const;
    }
  };

  if (variant === 'shader') {
    return (
      <View style={[styles.container, style]}>
        <ShaderBackground />
        {/* Overlay for better content readability */}
        <View style={[StyleSheet.absoluteFillObject, { backgroundColor: 'rgba(0, 0, 0, 0.25)' }]} />
        {children}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={getBackgroundGradient()}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* Subtle overlay blur for depth */}
      <BlurView
        intensity={intensity}
        tint={isDark ? 'dark' : 'light'}
        style={StyleSheet.absoluteFillObject}
      />

      {/* Floating orbs for visual effect */}
      <View style={[styles.orb, styles.orb1, {
        backgroundColor: isDark ? 'rgba(255, 235, 59, 0.08)' : colors.primary + '10'
      }]} />
      <View style={[styles.orb, styles.orb2, {
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.03)' : colors.info + '08'
      }]} />
      <View style={[styles.orb, styles.orb3, {
        backgroundColor: isDark ? 'rgba(255, 235, 59, 0.05)' : colors.success + '06'
      }]} />

      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  orb: {
    position: 'absolute',
    borderRadius: 1000,
    opacity: 0.6,
  },
  orb1: {
    width: 180,
    height: 180,
    top: -90,
    right: -45,
  },
  orb2: {
    width: 140,
    height: 140,
    bottom: -70,
    left: -35,
  },
  orb3: {
    width: 100,
    height: 100,
    top: '40%',
    right: -50,
  },
});
