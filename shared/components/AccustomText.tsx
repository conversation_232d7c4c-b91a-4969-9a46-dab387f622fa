import React from 'react';
import { Text, TextStyle } from 'react-native';
import { getResponsiveFontSize } from '../utils/responsiveFonts';

interface AccustomTextProps {
  /**
   * The text content that may contain "Accustom" mentions
   */
  children: string;
  
  /**
   * Base style to apply to the text
   */
  style?: TextStyle | TextStyle[];
  
  /**
   * Size variant for the Accustom branding
   * - 'large': Login screen size (24px base)
   * - 'medium': Header size (20px base)
   * - 'small': Inline text size (16px base)
   * - 'tiny': Small inline text (14px base)
   * - 'inherit': Match the surrounding text size
   */
  accustomSize?: 'large' | 'medium' | 'small' | 'tiny' | 'inherit';

  /**
   * Whether to render "ACCUSTOM" in all caps (default: true)
   */
  uppercase?: boolean;

  /**
   * Custom letter spacing for Accustom text (default: varies by size)
   */
  letterSpacing?: number;

  /**
   * Whether to use bold font for Accustom text (default: true)
   */
  bold?: boolean;

  /**
   * Custom font size override (takes precedence over accustomSize)
   */
  fontSize?: number;
}

/**
 * AccustomText component provides consistent styling for any text that contains
 * mentions of "Accustom" or "Accustom Team". It automatically detects and styles
 * these mentions according to the app's branding guidelines.
 * 
 * Usage:
 * ```tsx
 * <AccustomText accustomSize="large" style={styles.title}>
 *   Welcome to Accustom
 * </AccustomText>
 *
 * <AccustomText accustomSize="inherit" style={styles.description}>
 *   The Accustom Team will review your progress.
 * </AccustomText>
 *
 * <AccustomText fontSize={18} letterSpacing={1.2} style={styles.custom}>
 *   Custom sized Accustom text
 * </AccustomText>
 * ```
 */
export const AccustomText: React.FC<AccustomTextProps> = ({
  children,
  style,
  accustomSize = 'inherit',
  uppercase = true,
  letterSpacing,
  bold = true,
  fontSize,
}) => {
  // Get responsive font size based on variant
  const getAccustomFontSize = () => {
    // If custom fontSize is provided, use it
    if (fontSize !== undefined) {
      return fontSize;
    }

    // If inherit, try to extract font size from style
    if (accustomSize === 'inherit') {
      const styleArray = Array.isArray(style) ? style : [style];
      for (const s of styleArray) {
        if (s && typeof s === 'object' && 'fontSize' in s && s.fontSize) {
          return s.fontSize as number;
        }
      }
      // Fallback to medium size if can't extract from style
      return getResponsiveFontSize(16, 0.85, 1.15);
    }

    switch (accustomSize) {
      case 'large':
        return getResponsiveFontSize(24, 0.85, 1.15); // Login screen size
      case 'medium':
        return getResponsiveFontSize(20, 0.85, 1.15); // Header size
      case 'small':
        return getResponsiveFontSize(16, 0.85, 1.15); // Regular inline text
      case 'tiny':
        return getResponsiveFontSize(14, 0.85, 1.15); // Small inline text
      default:
        return getResponsiveFontSize(16, 0.85, 1.15);
    }
  };

  // Get letter spacing based on size if not provided
  const getLetterSpacing = () => {
    if (letterSpacing !== undefined) return letterSpacing;

    switch (accustomSize) {
      case 'large':
        return 2; // Login screen spacing
      case 'medium':
        return 2; // Header spacing
      case 'small':
        return 1.5; // Inline text spacing
      case 'tiny':
        return 1; // Small text spacing
      case 'inherit':
        return 1; // Subtle spacing for inherited size
      default:
        return 1.5;
    }
  };

  // Split text and render with styled Accustom mentions
  const renderStyledText = () => {
    // Match "Accustom", "ACCUSTOM", "Accustom Team", "ACCUSTOM TEAM"
    const parts = children.split(/(ACCUSTOM TEAM|Accustom Team|ACCUSTOM|Accustom)/g);
    const accustomFontSize = getAccustomFontSize();
    const accustomLetterSpacing = getLetterSpacing();
    
    return (
      <Text style={style}>
        {parts.map((part, index) => {
          const isAccustomMention = /^(ACCUSTOM TEAM|Accustom Team|ACCUSTOM|Accustom)$/.test(part);
          
          if (isAccustomMention) {
            // Determine the styled text based on the original and uppercase setting
            let styledText = part;
            if (uppercase) {
              if (part.toLowerCase().includes('team')) {
                styledText = 'ACCUSTOM TEAM';
              } else {
                styledText = 'ACCUSTOM';
              }
            }
            
            return (
              <Text
                key={index}
                style={[
                  style,
                  {
                    fontFamily: bold ? 'MontserratBold' : 'MontserratMedium',
                    letterSpacing: accustomLetterSpacing,
                    fontSize: accustomFontSize,
                  }
                ]}
              >
                {styledText}
              </Text>
            );
          }
          
          return part;
        })}
      </Text>
    );
  };

  return renderStyledText();
};

/**
 * Convenience component for large Accustom branding (like login screen)
 */
export const AccustomTitle: React.FC<Omit<AccustomTextProps, 'accustomSize'>> = (props) => (
  <AccustomText {...props} accustomSize="large" />
);

/**
 * Convenience component for header-sized Accustom branding
 */
export const AccustomHeader: React.FC<Omit<AccustomTextProps, 'accustomSize'>> = (props) => (
  <AccustomText {...props} accustomSize="medium" />
);

/**
 * Convenience component for inline Accustom mentions in body text
 */
export const AccustomInline: React.FC<Omit<AccustomTextProps, 'accustomSize'>> = (props) => (
  <AccustomText {...props} accustomSize="small" />
);

/**
 * Convenience component for small Accustom mentions
 */
export const AccustomSmall: React.FC<Omit<AccustomTextProps, 'accustomSize'>> = (props) => (
  <AccustomText {...props} accustomSize="tiny" />
);

export default AccustomText;
