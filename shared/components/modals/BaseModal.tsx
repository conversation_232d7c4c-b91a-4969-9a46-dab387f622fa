import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../contexts/ThemeContext";

interface BaseModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  animationType?: 'none' | 'slide' | 'fade';
  transparent?: boolean;
  noPadding?: boolean;
}

export const BaseModal: React.FC<BaseModalProps> = ({
  visible,
  onClose,
  title,
  children,
  showCloseButton = true,
  animationType = 'fade',
  transparent = true,
  noPadding = false,
}) => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem);

  return (
    <Modal
      visible={visible}
      transparent={transparent}
      animationType={animationType}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {(title || showCloseButton) && (
            <View style={styles.modalHeader}>
              {title && (
                <Text style={styles.modalTitle}>{title}</Text>
              )}
              {showCloseButton && (
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={onClose}
                >
                  <MaterialIcons name="close" size={24} color={colors.text} />
                </TouchableOpacity>
              )}
            </View>
          )}
          
          <View style={[styles.modalContent, noPadding && styles.modalContentNoPadding]}>
            {children}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: `rgba(0, 0, 0, ${designSystem.opacity.overlay})`,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: designSystem.spacing.lg,
  },
  modalContainer: {
    backgroundColor: colors.background,
    borderRadius: designSystem.borderRadius.xxl,
    width: '100%',
    maxWidth: 400, // Limit width on larger screens
    maxHeight: '80%', // Use maxHeight instead of fixed height
    overflow: 'hidden',
    flexDirection: 'column',
    // Add shadow for better visual separation
    shadowColor: colors.text,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: designSystem.spacing.xl,
    paddingTop: designSystem.spacing.xl,
    paddingBottom: designSystem.spacing.lg,
    backgroundColor: 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: designSystem.typography.fontSize.xl,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    marginRight: designSystem.spacing.lg,
  },
  closeButton: {
    padding: designSystem.spacing.sm,
    borderRadius: designSystem.borderRadius.xl,
    backgroundColor: colors.surface,
  },
  modalContent: {
    paddingHorizontal: designSystem.spacing.xl,
    paddingVertical: designSystem.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120, // Ensure minimum content height
  },
  modalContentNoPadding: {
    padding: 0,
  },
});
