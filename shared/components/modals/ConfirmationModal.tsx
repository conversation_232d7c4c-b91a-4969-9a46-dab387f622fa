import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../contexts/ThemeContext";
import { BaseModal } from './BaseModal';

interface ConfirmationModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonStyle?: 'primary' | 'danger' | 'success';
  isLoading?: boolean;
  icon?: string;
  iconColor?: string;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  visible,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmButtonStyle = 'primary',
  isLoading = false,
  icon,
  iconColor,
}) => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem);

  const getConfirmButtonColor = () => {
    switch (confirmButtonStyle) {
      case 'danger':
        return colors.error;
      case 'success':
        return colors.success;
      default:
        return colors.primary;
    }
  };

  return (
    <BaseModal
      visible={visible}
      onClose={onClose}
      title={title}
      showCloseButton={false}
    >
      <View style={styles.content}>
        {icon && (
          <View style={styles.iconContainer}>
            <MaterialIcons 
              name={icon as any} 
              size={48} 
              color={iconColor || colors.primary} 
            />
          </View>
        )}
        
        <Text style={styles.message}>{message}</Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onClose}
            disabled={isLoading}
          >
            <Text style={styles.cancelButtonText}>{cancelText}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.button,
              styles.confirmButton,
              { backgroundColor: getConfirmButtonColor() },
              isLoading && styles.disabledButton,
            ]}
            onPress={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.confirmButtonText}>{confirmText}</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </BaseModal>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  content: {
    alignItems: 'center',
    width: '100%',
  },
  iconContainer: {
    marginBottom: designSystem.spacing.xl,
    padding: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.round,
    backgroundColor: colors.primary + '20', // Add subtle background
  },
  message: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    textAlign: 'center',
    marginBottom: designSystem.spacing.xxxl,
    lineHeight: designSystem.typography.fontSize.lg * designSystem.typography.lineHeight.relaxed,
    paddingHorizontal: designSystem.spacing.md,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: designSystem.spacing.md,
    width: '100%',
  },
  button: {
    flex: 1,
    paddingVertical: designSystem.spacing.md,
    paddingHorizontal: designSystem.spacing.lg,
    borderRadius: designSystem.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
    // Add shadow for better visual hierarchy
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  cancelButton: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    shadowColor: colors.text,
  },
  confirmButton: {
    backgroundColor: colors.primary,
    shadowColor: colors.primary,
  },
  disabledButton: {
    opacity: designSystem.opacity.disabled,
  },
  cancelButtonText: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: 'MontserratBold',
    color: colors.text,
    letterSpacing: 0.5,
  },
  confirmButtonText: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: 'MontserratBold',
    color: colors.background,
    letterSpacing: 0.5,
  },
});
