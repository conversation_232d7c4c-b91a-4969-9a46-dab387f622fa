import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../contexts/ThemeContext";
import { BaseModal } from './BaseModal';

interface SuccessModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  buttonText?: string;
  onButtonPress?: () => void;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  visible,
  onClose,
  title = 'Success',
  message,
  buttonText = 'OK',
  onButtonPress,
}) => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem);

  const handleButtonPress = () => {
    if (onButtonPress) {
      onButtonPress();
    } else {
      onClose();
    }
  };

  return (
    <BaseModal
      visible={visible}
      onClose={onClose}
      title={title}
      showCloseButton={false}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <MaterialIcons 
            name="check-circle" 
            size={48} 
            color={colors.success} 
          />
        </View>
        
        <Text style={styles.message}>{message}</Text>
        
        <TouchableOpacity
          style={styles.button}
          onPress={handleButtonPress}
        >
          <Text style={styles.buttonText}>{buttonText}</Text>
        </TouchableOpacity>
      </View>
    </BaseModal>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  content: {
    alignItems: 'center',
    width: '100%',
  },
  iconContainer: {
    marginBottom: designSystem.spacing.xl,
    padding: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.round,
    backgroundColor: colors.success + '20', // Add subtle background
  },
  message: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    textAlign: 'center',
    marginBottom: designSystem.spacing.xxxl,
    lineHeight: designSystem.typography.fontSize.lg * designSystem.typography.lineHeight.relaxed,
    paddingHorizontal: designSystem.spacing.md,
  },
  button: {
    backgroundColor: colors.success,
    paddingVertical: designSystem.spacing.md,
    paddingHorizontal: designSystem.spacing.xxxl,
    borderRadius: designSystem.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
    minWidth: 120,
    width: '100%',
    maxWidth: 200,
    // Add shadow for better visual hierarchy
    shadowColor: colors.success,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  buttonText: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: 'MontserratBold',
    color: colors.background,
    letterSpacing: 0.5,
  },
});
