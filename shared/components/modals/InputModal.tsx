import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../contexts/ThemeContext";
import { BaseModal } from './BaseModal';

interface InputModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (value: string) => void;
  title: string;
  message?: string;
  placeholder?: string;
  initialValue?: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  icon?: string;
  iconColor?: string;
  multiline?: boolean;
  maxLength?: number;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
}

export const InputModal: React.FC<InputModalProps> = ({
  visible,
  onClose,
  onConfirm,
  title,
  message,
  placeholder,
  initialValue = '',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  isLoading = false,
  icon,
  iconColor,
  multiline = false,
  maxLength,
  keyboardType = 'default',
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [inputValue, setInputValue] = useState(initialValue);

  useEffect(() => {
    if (visible) {
      setInputValue(initialValue);
    }
  }, [visible, initialValue]);

  const handleConfirm = () => {
    onConfirm(inputValue);
  };

  return (
    <BaseModal
      visible={visible}
      onClose={onClose}
      title={title}
      showCloseButton={false}
    >
      <View style={styles.content}>
        {icon && (
          <View style={styles.iconContainer}>
            <MaterialIcons 
              name={icon as any} 
              size={48} 
              color={iconColor || colors.primary} 
            />
          </View>
        )}
        
        {message && (
          <Text style={styles.message}>{message}</Text>
        )}
        
        <TextInput
          style={[
            styles.input,
            multiline && styles.multilineInput,
          ]}
          value={inputValue}
          onChangeText={setInputValue}
          placeholder={placeholder}
          placeholderTextColor={colors.textMuted}
          multiline={multiline}
          maxLength={maxLength}
          keyboardType={keyboardType}
          autoFocus={true}
        />
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onClose}
            disabled={isLoading}
          >
            <Text style={styles.cancelButtonText}>{cancelText}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.button,
              styles.confirmButton,
              isLoading && styles.disabledButton,
            ]}
            onPress={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.confirmButtonText}>{confirmText}</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </BaseModal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  content: {
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  message: {
    fontSize: 16,
    fontFamily: 'Montserrat',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  input: {
    width: '100%',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    fontFamily: 'Montserrat',
    color: colors.text,
    backgroundColor: colors.background,
    marginBottom: 24,
    minHeight: 44,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  cancelButton: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  confirmButton: {
    backgroundColor: colors.primary,
  },
  disabledButton: {
    opacity: 0.6,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
  },
  confirmButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: 'black',
  },
});
