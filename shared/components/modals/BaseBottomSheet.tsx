import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from "../../contexts/ThemeContext";

interface BaseBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  showHandle?: boolean;
  animationType?: 'none' | 'slide' | 'fade';
  maxHeight?: number; // Percentage of screen height (0-100)
  minHeight?: number; // Percentage of screen height (0-100)
  scrollable?: boolean;
  noPadding?: boolean;
}

export const BaseBottomSheet: React.FC<BaseBottomSheetProps> = ({
  visible,
  onClose,
  title,
  children,
  showCloseButton = true,
  showHandle = true,
  animationType = 'slide',
  maxHeight = 80,
  minHeight,
  scrollable = true,
  noPadding = false,
}) => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem, maxHeight, minHeight);

  const ContentWrapper = scrollable ? ScrollView : View;
  const contentProps = scrollable 
    ? { 
        showsVerticalScrollIndicator: false,
        contentContainerStyle: styles.scrollContent 
      } 
    : { style: styles.staticContent };

  return (
    <Modal
      visible={visible}
      transparent
      animationType={animationType}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        />

        <View style={styles.bottomSheet}>
          {/* Handle */}
          {showHandle && <View style={styles.handle} />}

          {/* Header */}
          {(title || showCloseButton) && (
            <View style={styles.header}>
              <View style={styles.headerLeft}>
                {title && (
                  <Text style={styles.title}>{title}</Text>
                )}
              </View>
              {showCloseButton && (
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <MaterialIcons name="close" size={24} color={colors.textMuted} />
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* Content */}
          <ContentWrapper
            style={[styles.content, noPadding && styles.contentNoPadding]}
            {...contentProps}
          >
            {children}
          </ContentWrapper>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any, designSystem: any, maxHeight: number, minHeight?: number) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: `rgba(0, 0, 0, ${designSystem.opacity.overlay})`,
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  bottomSheet: {
    backgroundColor: colors.background,
    borderTopLeftRadius: designSystem.borderRadius.xxl,
    borderTopRightRadius: designSystem.borderRadius.xxl,
    minHeight: minHeight
      ? Dimensions.get('window').height * (minHeight / 100)
      : Dimensions.get('window').height * 0.3,
    maxHeight: Dimensions.get('window').height * (maxHeight / 100),
    height: minHeight
      ? Dimensions.get('window').height * (minHeight / 100)
      : undefined,
    paddingBottom: 34, // Safe area padding
    // Add shadow for better visual separation
    shadowColor: colors.text,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: colors.textMuted + '40',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: designSystem.spacing.md,
    marginBottom: designSystem.spacing.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: designSystem.spacing.xl,
    paddingBottom: designSystem.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    marginBottom: designSystem.spacing.lg,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: designSystem.typography.fontSize.xl,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  closeButton: {
    padding: designSystem.spacing.sm,
    borderRadius: designSystem.borderRadius.lg,
    backgroundColor: colors.surface,
  },
  content: {
    flex: 1,
    paddingHorizontal: designSystem.spacing.xl,
  },
  contentNoPadding: {
    paddingHorizontal: 0,
  },
  scrollContent: {
    paddingBottom: designSystem.spacing.xl,
  },
  staticContent: {
    flex: 1,
  },
});
