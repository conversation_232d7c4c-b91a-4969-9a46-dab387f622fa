import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from "../../contexts/ThemeContext";

interface DisqualifiedMessageProps {
  disqualificationReason?: string | null;
  containerStyle?: any;
}

export const DisqualifiedMessage: React.FC<DisqualifiedMessageProps> = ({
  disqualificationReason,
  containerStyle,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const router = useRouter();

  return (
    <View style={[styles.messageContainer, containerStyle]}>
      <Text style={styles.messageTitle}>You're disqualified</Text>
      {disqualificationReason === "1a" ? (
        <>
          <Text style={styles.messageSubtitle}>
            You've been disqualified because you were inactive and ran out of grace days.
          </Text>
          <Text style={styles.messageInfo}>
            Our system detected that you didn't submit your daily check-ins for multiple days.
            After using all your available grace days, you were automatically disqualified from the program.
            Remember that consistent participation is key to success in these challenges.
          </Text>
        </>
      ) : disqualificationReason === "1b" ? (
        <>
          <Text style={styles.messageSubtitle}>
            You've been disqualified because you had too many failed submissions and ran out of grace days.
          </Text>
          <Text style={styles.messageInfo}>
            Your submissions did not meet the program requirements on multiple occasions.
            After using all your available grace days to cover these failed submissions,
            you were disqualified from the program. Make sure to carefully read the
            submission guidelines for future challenges.
          </Text>
        </>
      ) : disqualificationReason === "1c" ? (
        <>
          <Text style={styles.messageSubtitle}>
            You've been disqualified because you ran out of grace days.
          </Text>
          <Text style={styles.messageInfo}>
            You've used all your available grace days in this challenge. Grace days are a crucial
            resource that help you recover from missed or failed submissions. Once depleted,
            disqualification is automatic according to program rules. Consider purchasing
            additional lives in future challenges if needed.
          </Text>
        </>
      ) : disqualificationReason === "2" ? (
        <>
          <Text style={styles.messageSubtitle}>
            You've been disqualified due to malpractice.
          </Text>
          <Text style={styles.messageInfo}>
            Our moderators have identified violations of program rules or submission guidelines.
            This may include falsified submissions, inappropriate content, or other forms of
            misconduct that compromise the integrity of the challenge. If you believe this
            was in error, you can file a dispute through the app.
          </Text>
          <TouchableOpacity
            style={styles.disputeButton}
            onPress={() => router.push('/Disputes')}
          >
            <Text style={styles.disputeButtonText}>File a Dispute</Text>
          </TouchableOpacity>
        </>
      ) : (
        <>
          <Text style={styles.messageSubtitle}>
            Unfortunately, you've been disqualified from this program.
          </Text>
          <Text style={styles.messageInfo}>
            Your participation in this challenge has ended due to disqualification.
            This could be due to various reasons including rule violations,
            missed submissions, or running out of lives. We encourage you to
            review the program guidelines for future challenges.
          </Text>
        </>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 20,
    margin: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  messageTitle: {
    fontSize: 24,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  messageSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  messageInfo: {
    fontSize: 14,
    fontFamily: 'Montserrat',
    color: colors.textMuted,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  disputeButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignSelf: 'center',
    marginTop: 8,
  },
  disputeButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    textAlign: 'center',
  },
});
