import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { calendarService, CalendarEvent } from '../../lib/services/calendarService';

export interface AddToCalendarProps {
  event: CalendarEvent;
  onSuccess?: (eventId: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  style?: any;
  buttonText?: string;
  showIcon?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  isAdded?: boolean; // Whether the event is already added to calendar
  showWarning?: boolean; // Whether to show warning before adding
  warningMessage?: string; // Custom warning message
}

export const AddToCalendar: React.FC<AddToCalendarProps> = ({
  event,
  onSuccess,
  onError,
  disabled = false,
  style,
  buttonText,
  showIcon = true,
  variant = 'primary',
  size = 'medium',
  isAdded = false,
  showWarning = false,
  warningMessage,
}) => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem);

  const [isLoading, setIsLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isSupported, setIsSupported] = useState(true);

  // Check if calendar integration is supported and get permissions
  useEffect(() => {
    const checkSupport = () => {
      const supported = calendarService.isSupported();
      setIsSupported(supported);
      
      if (!supported) {
        return;
      }

      // Check existing permissions
      checkPermissions();
    };

    checkSupport();
  }, []);

  const checkPermissions = async () => {
    try {
      const result = await calendarService.getCalendarPermissions();
      if (result.success && result.data) {
        setHasPermission(result.data.granted);
      }
    } catch (error) {
      console.error('Error checking calendar permissions:', error);
    }
  };

  const requestPermissions = async (): Promise<boolean> => {
    try {
      const result = await calendarService.requestCalendarPermissions();
      if (result.success && result.data) {
        setHasPermission(result.data.granted);
        return result.data.granted;
      } else {
        onError?.(result.error || 'Failed to request calendar permissions');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to request permissions';
      onError?.(errorMessage);
      return false;
    }
  };

  const handleAddToCalendar = async () => {
    if (isLoading || disabled) return;

    // Check if platform is supported
    if (!isSupported) {
      Alert.alert(
        'Not Supported',
        'Calendar integration is not available on this platform.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Show warning if needed
    if (showWarning && warningMessage) {
      Alert.alert(
        'Add to Calendar Again?',
        warningMessage,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Add Anyway',
            style: 'default',
            onPress: () => performCalendarAdd(),
          },
        ]
      );
      return;
    }

    performCalendarAdd();
  };

  const performCalendarAdd = async () => {
    setIsLoading(true);

    try {
      // Check permissions first
      if (hasPermission === null) {
        await checkPermissions();
      }

      if (!hasPermission) {
        const granted = await requestPermissions();
        if (!granted) {
          Alert.alert(
            'Permission Required',
            'Calendar access is required to add events. Please enable it in your device settings.',
            [{ text: 'OK' }]
          );
          setIsLoading(false);
          return;
        }
      }

      // Create the calendar event
      const result = await calendarService.createEvent(event);
      
      if (result.success && result.data) {
        onSuccess?.(result.data);
        Alert.alert(
          'Success! 📅',
          'Event has been added to your calendar successfully.',
          [{ text: 'OK' }]
        );
      } else {
        const errorMessage = result.error || 'Failed to add event to calendar';
        onError?.(errorMessage);
        Alert.alert(
          'Error',
          errorMessage,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      onError?.(errorMessage);
      Alert.alert(
        'Error',
        errorMessage,
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render if not supported
  if (!isSupported) {
    return null;
  }

  const getButtonText = () => {
    if (buttonText) return buttonText;
    if (isAdded) return 'Added to Calendar';
    return 'Add to Calendar';
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}`]];
    
    if (variant === 'primary') {
      baseStyle.push(styles.buttonPrimary);
    } else if (variant === 'secondary') {
      baseStyle.push(styles.buttonSecondary);
    } else if (variant === 'outline') {
      baseStyle.push(styles.buttonOutline);
    }

    if (disabled) {
      baseStyle.push(styles.buttonDisabled);
    }

    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText, styles[`buttonText${size.charAt(0).toUpperCase() + size.slice(1)}`]];
    
    if (variant === 'primary') {
      baseStyle.push(styles.buttonTextPrimary);
    } else if (variant === 'secondary') {
      baseStyle.push(styles.buttonTextSecondary);
    } else if (variant === 'outline') {
      baseStyle.push(styles.buttonTextOutline);
    }

    if (disabled) {
      baseStyle.push(styles.buttonTextDisabled);
    }

    return baseStyle;
  };

  const getIconName = () => {
    return 'event';
  };

  const getIconColor = () => {
    if (disabled) return colors.textMuted;
    if (variant === 'primary') return colors.white;
    if (variant === 'secondary') return colors.white;
    return colors.primary;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={handleAddToCalendar}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
    >
      {isLoading ? (
        <ActivityIndicator 
          size="small" 
          color={variant === 'outline' ? colors.primary : colors.white} 
        />
      ) : (
        <View style={styles.buttonContent}>
          {showIcon && (
            <MaterialIcons
              name={getIconName()}
              size={size === 'small' ? 16 : size === 'large' ? 24 : 20}
              color={getIconColor()}
              style={styles.buttonIcon}
            />
          )}
          <Text style={getTextStyle()}>
            {getButtonText()}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  button: {
    borderRadius: designSystem.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonSmall: {
    paddingHorizontal: designSystem.spacing.sm,
    paddingVertical: designSystem.spacing.xs,
    minHeight: 32,
  },
  buttonMedium: {
    paddingHorizontal: designSystem.spacing.md,
    paddingVertical: designSystem.spacing.sm,
    minHeight: 40,
  },
  buttonLarge: {
    paddingHorizontal: designSystem.spacing.lg,
    paddingVertical: designSystem.spacing.md,
    minHeight: 48,
  },
  buttonPrimary: {
    backgroundColor: colors.primary,
  },
  buttonSecondary: {
    backgroundColor: colors.secondary,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  buttonDisabled: {
    backgroundColor: colors.surface,
    borderColor: colors.border,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: designSystem.spacing.xs,
  },
  buttonText: {
    fontFamily: 'MontserratMedium',
    textAlign: 'center',
  },
  buttonTextSmall: {
    fontSize: designSystem.typography.fontSize.sm,
  },
  buttonTextMedium: {
    fontSize: designSystem.typography.fontSize.md,
  },
  buttonTextLarge: {
    fontSize: designSystem.typography.fontSize.lg,
  },
  buttonTextPrimary: {
    color: colors.white,
  },
  buttonTextSecondary: {
    color: colors.white,
  },
  buttonTextOutline: {
    color: colors.primary,
  },
  buttonTextDisabled: {
    color: colors.textMuted,
  },
});
