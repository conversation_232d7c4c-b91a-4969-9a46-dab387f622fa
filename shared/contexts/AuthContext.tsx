import React, { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from "../../lib/config/firebase";
import { getToken, updateToken, updateId, logout } from "../../lib/utils/variables";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    let mounted = true;
    let isUpdatingAuth = false; // Prevent concurrent auth updates

    // Check initial auth state from AsyncStorage
    const checkInitialAuthState = async () => {
      try {
        const hasToken = await getToken();

        // If we have a token but no Firebase user yet, wait a bit longer
        if (hasToken && !auth.currentUser) {
          // Give Firebase more time to restore the session on Android
          let attempts = 0;
          const maxAttempts = 10; // Wait up to 5 seconds

          while (attempts < maxAttempts && !auth.currentUser) {
            await new Promise(resolve => setTimeout(resolve, 500));
            attempts++;
          }

          if (!auth.currentUser) {
            await logout();
          }
        }
      } catch (error) {
        // Silent error handling
      }
    };

    checkInitialAuthState();

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (!mounted || isUpdatingAuth) return;

      isUpdatingAuth = true;

      try {
        if (firebaseUser && firebaseUser.emailVerified) {
          // User is signed in and email is verified
          // Batch state updates to prevent multiple re-renders
          setUser(firebaseUser);
          setIsAuthenticated(true);

          // Update local storage asynchronously without blocking render
          Promise.resolve().then(async () => {
            try {
              await updateToken(true);
              await updateId(firebaseUser.email || '');
            } catch (error) {
              console.warn('Failed to update local storage:', error);
            }
          });
        } else if (firebaseUser && !firebaseUser.emailVerified) {
          // User is signed in but email not verified
          setUser(null);
          setIsAuthenticated(false);

          // Don't clear storage completely, just set auth to false
          Promise.resolve().then(async () => {
            try {
              await updateToken(false);
            } catch (error) {
              console.warn('Failed to update token:', error);
            }
          });
        } else {
          // User is signed out
          setUser(null);
          setIsAuthenticated(false);

          // Clear local storage asynchronously
          Promise.resolve().then(async () => {
            try {
              await logout();
            } catch (error) {
              console.warn('Failed to logout:', error);
            }
          });
        }
      } catch (error) {
        console.error('Auth state change error:', error);
        setUser(null);
        setIsAuthenticated(false);

        // Handle logout asynchronously
        Promise.resolve().then(async () => {
          try {
            await logout();
          } catch (logoutError) {
            console.warn('Failed to logout after error:', logoutError);
          }
        });
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
        isUpdatingAuth = false;
      }
    });

    // Cleanup subscription on unmount
    return () => {
      mounted = false;
      unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      // Set loading state to prevent UI flicker during sign out
      setIsLoading(true);

      // Sign out from Firebase first
      await auth.signOut();

      // Clear local storage
      await logout();

      // Update state - these will be handled by onAuthStateChanged as well
      // but we set them here for immediate UI feedback
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Sign out error:', error);
      // Even if sign out fails, clear local state
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
