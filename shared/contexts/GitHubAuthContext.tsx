import React, { createContext, useContext, useState, useCallback } from 'react';
import { Platform } from 'react-native';
import GitHubAuthModal from "../../ui/components/GitHubAuthModal";

interface GitHubAuthContextType {
  openAuthModal: (authUrl: string) => Promise<{ success: boolean; code?: string; error?: string }>;
}

const GitHubAuthContext = createContext<GitHubAuthContextType | undefined>(undefined);

export const useGitHubAuth = () => {
  const context = useContext(GitHubAuthContext);
  if (!context) {
    throw new Error('useGitHubAuth must be used within a GitHubAuthProvider');
  }
  return context;
};

interface GitHubAuthProviderProps {
  children: React.ReactNode;
}

export const GitHubAuthProvider: React.FC<GitHubAuthProviderProps> = ({ children }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [authUrl, setAuthUrl] = useState('');
  const [authPromise, setAuthPromise] = useState<{
    resolve: (value: { success: boolean; code?: string; error?: string }) => void;
    reject: (reason?: any) => void;
  } | null>(null);

  const openAuthModal = useCallback((url: string): Promise<{ success: boolean; code?: string; error?: string }> => {
    // For web, we don't use the modal
    if (Platform.OS === 'web') {
      return Promise.resolve({ success: false, error: 'Use popup for web' });
    }

    return new Promise((resolve, reject) => {
      setAuthUrl(url);
      setModalVisible(true);
      setAuthPromise({ resolve, reject });
    });
  }, []);

  const handleSuccess = useCallback((code: string) => {
    if (authPromise) {
      authPromise.resolve({ success: true, code });
      setAuthPromise(null);
    }
    setModalVisible(false);
  }, [authPromise]);

  const handleError = useCallback((error: string) => {
    if (authPromise) {
      authPromise.resolve({ success: false, error });
      setAuthPromise(null);
    }
    setModalVisible(false);
  }, [authPromise]);

  const handleClose = useCallback(() => {
    if (authPromise) {
      authPromise.resolve({ success: false, error: 'Authentication cancelled' });
      setAuthPromise(null);
    }
    setModalVisible(false);
  }, [authPromise]);

  // Set up global callback for the GitHub service
  React.useEffect(() => {
    (global as any).openGitHubAuthModal = openAuthModal;
    
    return () => {
      delete (global as any).openGitHubAuthModal;
    };
  }, [openAuthModal]);

  const contextValue: GitHubAuthContextType = {
    openAuthModal,
  };

  return (
    <GitHubAuthContext.Provider value={contextValue}>
      {children}
      <GitHubAuthModal
        visible={modalVisible}
        authUrl={authUrl}
        onClose={handleClose}
        onSuccess={handleSuccess}
        onError={handleError}
      />
    </GitHubAuthContext.Provider>
  );
};

export default GitHubAuthProvider;
