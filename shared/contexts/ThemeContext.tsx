import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from "../hooks/useColorScheme";
import { Colors } from "./Colors";

export type ThemeMode = 'light' | 'dark' | 'system';
export type ColorScheme = 'light' | 'dark';

// Design System Constants
export const DesignSystem = {
  // Spacing scale (based on 4px grid)
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
    xxxxl: 40,
  },

  // Border radius scale
  borderRadius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    round: 50, // For circular elements
  },

  // Typography scale
  typography: {
    fontSize: {
      xs: 10,
      sm: 12,
      md: 14,
      lg: 16,
      xl: 18,
      xxl: 20,
      xxxl: 24,
      xxxxl: 28,
      xxxxxl: 32,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
      loose: 1.8,
    },
    letterSpacing: {
      tight: -0.5,
      normal: 0,
      wide: 0.5,
      wider: 1,
    },
  },

  // Shadow/Elevation scale
  shadows: {
    none: {
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    sm: {
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 6,
    },
    xl: {
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 8,
    },
  },

  // Animation durations
  animation: {
    fast: 150,
    normal: 250,
    slow: 350,
  },

  // Opacity scale
  opacity: {
    disabled: 0.6,
    overlay: 0.5,
    subtle: 0.8,
    medium: 0.9,
    full: 1,
  },
};

interface ThemeColors {
  // Background colors
  background: string;
  surface: string;
  card: string;
  header: string;

  // Text colors
  text: string;
  textSecondary: string;
  textMuted: string;

  // UI colors
  primary: string;
  tint: string;
  border: string;
  separator: string;

  // Icon colors
  icon: string;
  iconActive: string;
  iconInactive: string;

  // Tab colors
  tabBackground: string;
  tabIconDefault: string;
  tabIconSelected: string;

  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;

  // Gradient colors
  gradientStart: string;
  gradientEnd: string;

  // Glassmorphism colors
  glassBackground: string;
  glassBackgroundSecondary: string;
  glassBorder: string;
  glassHighlight: string;

  // Neumorphism colors
  neumorphicBackground: string;
  neumorphicShadowDark: string;
  neumorphicShadowLight: string;
  neumorphicHighlight: string;
}

interface ThemeContextType {
  themeMode: ThemeMode;
  colorScheme: ColorScheme;
  colors: ThemeColors;
  setThemeMode: (mode: ThemeMode) => void;
  isDark: boolean;
  designSystem: typeof DesignSystem;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = '@habit_royale_theme_mode';

// Define comprehensive color schemes
const createThemeColors = (scheme: ColorScheme): ThemeColors => {
  const baseColors = Colors[scheme];
  
  if (scheme === 'dark') {
    return {
      // Background colors
      background: '#0A0A0A',
      surface: '#1A1A1A',
      card: '#1F1F1F',
      header: '#151515',

      // Text colors
      text: '#FFFFFF',
      textSecondary: '#E0E0E0',
      textMuted: '#A0A0A0',

      // UI colors
      primary: '#E6C547', // Refined shiny gold - elegant but not overwhelming
      tint: baseColors.tint,
      border: '#2A2A2A',
      separator: '#333333',

      // Icon colors
      icon: baseColors.icon,
      iconActive: '#FFEB3B',
      iconInactive: baseColors.tabIconDefault,

      // Tab colors
      tabBackground: '#151515',
      tabIconDefault: baseColors.tabIconDefault,
      tabIconSelected: baseColors.tabIconSelected,

      // Status colors
      success: '#4CAF50',
      warning: '#D4AF37', // Classic gold for subtle shiny gradient
      error: '#E53935',
      info: '#1E88E5',

      // Gradient colors
      gradientStart: '#1a1a1a',
      gradientEnd: '#0f0f0f',

      // Glassmorphism colors - Black tinted glass for better readability
      glassBackground: 'rgba(0, 0, 0, 0.60)',
      glassBackgroundSecondary: 'rgba(0, 0, 0, 0.70)',
      glassBorder: 'rgba(255, 255, 255, 0.20)',
      glassHighlight: 'rgba(255, 255, 255, 0.15)',

      // Neumorphism colors
      neumorphicBackground: '#1A1A1A',
      neumorphicShadowDark: 'rgba(0, 0, 0, 0.8)',
      neumorphicShadowLight: 'rgba(255, 255, 255, 0.03)',
      neumorphicHighlight: 'rgba(255, 255, 255, 0.05)',
    };
  } else {
    // Refined luxury light theme
    return {
      // Background colors - Clean but warm
      background: '#F0F0F2', // Deeper sophisticated gray with subtle warmth
      surface: '#FFFFFF',
      card: '#FFFFFF', // Pure white cards for contrast
      header: '#FFFFFF',

      // Text colors - Rich but not too warm
      text: '#1A1A1A', // Clean dark gray
      textSecondary: '#2D2D2D',
      textMuted: '#6B7280', // Cool gray

      // UI colors - Rich gold that pops
      primary: '#D4AF37', // Classic gold - rich and luxurious
      tint: '#D4AF37', // Consistent with primary
      border: '#E5E7EB', // Clean light border
      separator: '#F3F4F6', // Very light separator

      // Icon colors
      icon: baseColors.icon, // Use base colors for consistency
      iconActive: '#D4AF37', // Classic gold for active icons
      iconInactive: baseColors.tabIconDefault,

      // Tab colors
      tabBackground: '#FFFFFF',
      tabIconDefault: baseColors.tabIconDefault,
      tabIconSelected: '#D4AF37', // Classic gold for selected tabs

      // Status colors - Clean and modern
      success: '#10B981', // Modern green
      warning: '#F59E0B', // Amber warning
      error: '#EF4444', // Clean red
      info: '#3B82F6', // Modern blue

      // Gradient colors - Clean gradients
      gradientStart: '#FFFFFF', // Pure white
      gradientEnd: '#F8F9FA', // Very light gray

      // Glassmorphism colors - Black tinted glass with gold borders for light theme
      glassBackground: 'rgba(0, 0, 0, 0.50)', // Black glass for better readability
      glassBackgroundSecondary: 'rgba(0, 0, 0, 0.60)',
      glassBorder: 'rgba(212, 175, 55, 0.40)', // Gold border
      glassHighlight: 'rgba(212, 175, 55, 0.30)',

      // Neumorphism colors - Clean neutral tones
      neumorphicBackground: '#F0F0F3', // Clean light background
      neumorphicShadowDark: 'rgba(163, 177, 198, 0.6)', // Clean shadow
      neumorphicShadowLight: 'rgba(255, 255, 255, 0.8)',
      neumorphicHighlight: 'rgba(255, 255, 255, 0.9)',
    };
  }
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [isLoading, setIsLoading] = useState(true);

  // Determine the actual color scheme based on theme mode
  const colorScheme: ColorScheme = themeMode === 'system' 
    ? (systemColorScheme ?? 'dark') 
    : themeMode === 'dark' 
    ? 'dark' 
    : 'light';

  const colors = createThemeColors(colorScheme);
  const isDark = colorScheme === 'dark';

  // Update CSS custom properties for web
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      root.setAttribute('data-theme', colorScheme);
      root.style.setProperty('--background-color', colors.background);
      root.style.setProperty('--surface-color', colors.surface);
      root.style.setProperty('--text-color', colors.text);
      root.style.setProperty('--border-color', colors.border);
      root.style.setProperty('--primary-color', colors.primary);
    }
  }, [colorScheme, colors]);

  // Load saved theme mode from storage
  useEffect(() => {
    const loadThemeMode = async () => {
      try {
        const savedThemeMode = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedThemeMode && ['light', 'dark', 'system'].includes(savedThemeMode)) {
          setThemeModeState(savedThemeMode as ThemeMode);
        }
      } catch (error) {
        console.error('Error loading theme mode:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemeMode();
  }, []);

  // Save theme mode to storage
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      // Update state immediately for responsive UI
      setThemeModeState(mode);

      // Save to storage asynchronously without blocking render
      Promise.resolve().then(async () => {
        try {
          await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
        } catch (error) {
          console.error('Error saving theme mode:', error);
        }
      });
    } catch (error) {
      console.error('Error setting theme mode:', error);
    }
  };

  // Don't render children until theme is loaded
  if (isLoading) {
    return null;
  }

  const value: ThemeContextType = {
    themeMode,
    colorScheme,
    colors,
    setThemeMode,
    isDark,
    designSystem: DesignSystem,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
